/*
 * Wally.c
 *
 * Copyright (C) 2013 Zscaler, Inc. All Rights Reserved
 */

#define _GNU_SOURCE

#include <stdio.h>
#include <unistd.h>
#include <stdbool.h>
#include <math.h>

#include "zthread/zthread.h"
#include "zpath_misc/zpath_misc.h"
#include "zevent/zevent.h"

#include "zevent/zevent.h"

#include "wally/wally.h"
#include "wally/wally_compiled.h"
#include "wally/wally_hash.h"
#include "wally/wally_private.h"
#include "wally/wally_db.h"
#include "wally/wally_oper.h"
#include "wally/wally_table_queue.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "jenkins_hash/lookup3_hash.h"

#include "zpath_lib/zpath_config_override_keys.h"
/* for auto test usage only */
#define CLEANUP_INTERVAL_US (4*1000000)  /* 4 seconds */
#define ROW_EXPIRE_SEC (60)            /* 1 minute */
#define MAX_CLEANUP_ROWS 11
#define MAX_SCAN_ROWS 12
#define MAX_TIMEOUT_CLEANUP_TRACK_COUNT 1000
#define MAX_TIMEOUT_TRACK_COUNT 7500
#define CLEANUP_DELAY_AT_STARTUP_US (10LL * 60LL * 1000000LL) /*10 minutes*/
#define TIMEOUT_THREAD_INTERVAL_US  30000
#define WALLY_DEFAULT_ENABLE_DB_ROW_PROCESS_THREAD 0
#define WALLY_DEFAULT_TABLE_THREADS 0
#define UINT64_MAX_DIGITS 20
#define WALLY_ORIGIN_GB_INDEX 0

struct zpath_allocator wally_allocator = ZPATH_ALLOCATOR_INIT("wally");
struct argo_structure_description *wally_stats_description = NULL;
struct argo_structure_description *generic_row_description = NULL;
struct argo_structure_description *wally_db_stats_description = NULL;
struct argo_structure_description *wally_db_table_stats_description = NULL;
struct argo_structure_description *wally_fohh_connection_stats_description = NULL;
struct argo_structure_description *wally_sync_pause_stats_description = NULL;
struct argo_structure_description *wally_db_pg_table_bootup_stats_description = NULL;
struct argo_structure_description *wally_db_pg_table_stats_description = NULL;
struct argo_structure_description *wally_postgres_stats_description = NULL;
struct argo_structure_description *wally_table_stats_description = NULL;
struct argo_structure_description *wally_registrant_stats_description = NULL;
struct argo_structure_description *wp_db_stats_description = NULL;
struct argo_structure_description *wp_db_table_stats_description = NULL;
struct argo_structure_description *wt_desc = NULL;
uint32_t  wally_max_hb_iteration = WALLY_DEFAULT_MAX_HB_CHECK_ITERATION;
struct wally_recovery_state g_recovery_state = {0};

/* TCP keepalives */
uint64_t tcp_keepalives_idle = WALLY_KEEPALIVES_IDLE;
uint64_t tcp_keepalives_interval = WALLY_KEEPALIVES_INTERVAL;
uint64_t tcp_keepalives_count = WALLY_KEEPALIVES_COUNT;
uint64_t tcp_user_timeout = WALLY_TCP_USER_TIMEOUT;
bool disable_wally_tcp_config = false;

 /* By default extended HB is enabled "ONLY" for Wallyd and its timeout is 300 secs
 * For other applications, default HB timeout is 20 seconds.
 */
uint32_t  wally_max_hb_miss_timeout_s = WALLY_DEFAULT_HB_THRESHOLD;
uint32_t  wally_default_hb_timeout_s = WALLY_DEFAULT_HB_TIMEOUT_S;
int timer_interval_us = WP_POLL_INTERVAL_US;
int64_t cleanup_delay_at_start_us = CLEANUP_DELAY_AT_STARTUP_US;

struct wally_global_config wally_gbl_cfg = {
    .wally_postgres_db_query_batch_size = WP_MAX_ROWS,
    .enable_row_process_thread = WALLY_DEFAULT_ENABLE_DB_ROW_PROCESS_THREAD,
    .statement_timeout = WALLY_STATEMENT_OUT_DEFAULT,
    .lock_timeout = WALLY_LOCK_TIMEOUT_DEFAULT,
    .idle_session_timeout = WALLY_IDLE_SESS_TIMEOUT_DEFAULT,
    .idle_in_trans_session_timeout = WALLY_IDLE_TRANS_SESS_TIMEOUT_DEFAULT,
    .enable_db_gvr_mode = WALLY_DEFAULT_ENABLE_GVR_MODE,
    .wally_threads = WALLY_DEFAULT_TABLE_THREADS,
    .enable_batch_write = WALLY_DEFAULT_ENABLE_BATCH_WRITE,
    .write_batch_size = WALLY_DEFAULT_WRITE_BATCH_SIZE,
};

int32_t responses_waiting = 0;
int32_t do_wts_done = 0;
static int table_resume_error_count = 0;
extern bool wally_origin_disc_manual;

int wally_multiple_index_consistency_support = 0;

static int wally_avl_comparator(void *a, void *b, void *cookie);
static void wally_updated_registrant_response_status(struct wally *wally, int64_t delta_time);
static void wally_updated_registrant_db_wait_status(struct wally *wally, int64_t delta_time);
void wally_check_post_resume_callback(struct wally *wally);
void wally_reset_recovery_mode(struct wally_table *table, uint8_t mode);
int wally_initiate_recovery_end_requests(struct wally_table *table, char *table_name, int64_t received_rows, int stop_call);
static void  wally_recovery_state_machine(struct wally_table *table);
static void wally_table_recovery_state_update(struct wally_table *table, enum recovery_state_t recovery_state);

const char *wally_error_strings[] = {
    [WALLY_RESULT_NO_ERROR]="WALLY_RESULT_NO_ERROR",
    [WALLY_RESULT_ERR]="WALLY_RESULT_ERR",
    [WALLY_RESULT_NOT_FOUND]="WALLY_RESULT_NOT_FOUND",
    [WALLY_RESULT_NO_MEMORY]="WALLY_RESULT_NO_MEMORY",
    [WALLY_RESULT_CANT_WRITE]="WALLY_RESULT_CANT_WRITE",
    [WALLY_RESULT_ERR_TOO_LARGE]="WALLY_RESULT_ERR_TOO_LARGE",
    [WALLY_RESULT_BAD_ARGUMENT]="WALLY_RESULT_BAD_ARGUMENT",
    [WALLY_RESULT_INSUFFICIENT_DATA]="WALLY_RESULT_INSUFFICIENT_DATA",
    [WALLY_RESULT_NOT_IMPLEMENTED]="WALLY_RESULT_NOT_IMPLEMENTED",
    [WALLY_RESULT_BAD_DATA]="WALLY_RESULT_BAD_DATA",
    [WALLY_RESULT_WOULD_BLOCK]="WALLY_RESULT_WOULD_BLOCK",
    [WALLY_RESULT_BAD_STATE]="WALLY_RESULT_BAD_STATE",
    [WALLY_RESULT_INCOMPLETE]="WALLY_RESULT_INCOMPLETE",
    [WALLY_RESULT_ASYNCHRONOUS]="WALLY_RESULT_ASYNCHRONOUS",
    [WALLY_RESULT_EXCESS_DYN_FIELDS]="WALLY_RESULT_EXCESS_DYN_FIELDS",
    [WALLY_RESULT_NOT_READY]="WALLY_RESULT_NOT_READY",
};

const char * wally_table_pause_status_str[] = {
    [wally_table_not_paused] = "Not Paused",
    [wally_table_paused] = "Paused",
    [wally_table_resume_in_progress] = "Resume in progress",
    [wally_table_resume_completed] = "Resume completed",
};

const char *wally_result_string(int result)
{
    if (result >= (sizeof(wally_error_strings) / sizeof (const char *))) return "INVALID_RESULT";
    if (result < 0) return "INVALID_RESULT";
    if (wally_error_strings[result] == NULL) return "INVALID_RESULT";
    return wally_error_strings[result];
}
const char * wally_table_recovery_state_str[] = {
    [recovery_state_noactive] = "recovery_state_noactive",
    [recovery_state_begin] = "recovery_state_begin",
    [recovery_state_rowsyncstart] = "recovery_state_rowsyncstart",
    [recovery_state_rowsyncend] = "recovery_state_rowsyncend",
    [recovery_state_timeout] = "recovery_state_timeout",
    [recovery_state_end] = "recovery_state_end",
    [recovery_state_complete] = "recovery_state_complete",
    [recovery_state_failure] = "recovery_state_failure",
    [recovery_state_stopped] = "recovery_state_stopped"
};

/* Function : wally_recovery_state_string
 * Arg      : state - state of recovery
 * Ret      : String representation of state
 * Desc     : This function converts enum to string representation
 *              of recovery state.
 */
const char *wally_recovery_state_string(int state)
{
    if (state >= (sizeof(wally_table_recovery_state_str) / sizeof (const char *))) return "INVALID_STATE";
    if (state < 0) return "INVALID_STATE";
    if (wally_table_recovery_state_str[state] == NULL) return "INVALID_STATE";
    return wally_table_recovery_state_str[state];
}

enum wally_app_state wally_state = wally_state_app_initilizing;

static  void wally_interest_update_stats_pending_window(int64_t now, struct wally_interest *interest);
static inline void wally_table_interest_update_pending_window(uint8_t delta, struct wally_interest *interest);

#define WALLY_TABLE_PAUSE_STATUS_COUNT (sizeof(wally_table_pause_status_str) / sizeof(wally_table_pause_status_str[0]))

/* Routine to trigger watchdog */
inline void wally_infinite_loop()
{
    WALLY_LOG(AL_CRITICAL, "Wally infinite loop to trigger watchdog");
    while(1) sleep(1);
}

/* Function     : set_wally_app_state
 * Arg          : state
 * Return       : None
 * Description  : Set wallyd application state from main
    *              This is not used in non-wally applications
 */
void set_wally_app_state (enum wally_app_state state)
{
    wally_state = state;
}

/* Function     : get_wally_app_state
 * Arg          : None
 * Return       : application bootup state
 * Description  : Get wallyd application state
 *                  This is not used in non-wally applications
 */
enum wally_app_state get_wally_app_state()
{
    return wally_state;
}

/* Function     : set_multiple_index_consistency_support
 * Return       : None
 * Description  : This is invoked to enable the multiple index consistency support feature
 */
void set_multiple_index_consistency_support()
{
    wally_multiple_index_consistency_support = 1;
}

const char * wally_table_get_pause_status_str(enum table_pause_status status)
{
    if ((status >= WALLY_TABLE_PAUSE_STATUS_COUNT) ||
         !wally_table_pause_status_str[status])
        return "";
    return wally_table_pause_status_str[status];
}

char *wally_origin_state_strings[] = {
#define XX(a, b, c) b,
    INTEREST_DB_STATE_XX(XX)
#undef XX
};
char *wally_origin_state_short_strings[] = {
#define XX(a, b, c) c,
    INTEREST_DB_STATE_XX(XX)
#undef XX
};

#define WALLY_ORIGIN_STATE_COUNT (sizeof(wally_origin_state_strings) / sizeof(wally_origin_state_strings[0]))

uint64_t wally_debug =
    (!WALLY_DEBUG_RESULT_BIT) |
    (!WALLY_DEBUG_TABLE_BIT) |
    (!WALLY_DEBUG_REGISTRATION_BIT) |
    (!WALLY_DEBUG_ROW_BIT) |
    (!WALLY_DEBUG_POSTGRES_BIT) |
    (!WALLY_DEBUG_FOHH_CLIENT_ROW_BIT) |
    (!WALLY_DEBUG_POSTGRES_POLL_BIT) |
    (!WALLY_DEBUG_POSTGRES_FC_BIT) |
    (!WALLY_DEBUG_POSTGRES_CONN_BIT) |
    (!WALLY_DEBUG_POSTGRES_EVENT_BIT) |
    (!WALLY_DEBUG_ROW_DETAIL_BIT) |
    (!WALLY_DEBUG_WRITE_ROW_BIT) |
    (!WALLY_DEBUG_POSTGRES_WRITE_BIT) |
    (!WALLY_DEBUG_INTEREST_CB_BIT) |
    (!WALLY_DEBUG_POSTGRES_SQL_BIT) |
    (!WALLY_DEBUG_SYNC_PAUSE_BIT) |
    (!WALLY_DEBUG_HB_TIMEOUT_BIT) |
    (!WALLY_DEBUG_TABLE_QUEUE_BIT) |
    (!WALLY_DEBUG_MIC_BIT) |
    (!WALLY_DEBUG_RECOVERY_BIT) |
    0;

uint64_t wally_debug_catch =
    (!WALLY_DEBUG_RESULT_BIT) |
    (!WALLY_DEBUG_TABLE_BIT) |
    (!WALLY_DEBUG_REGISTRATION_BIT) |
    (!WALLY_DEBUG_ROW_BIT) |
    (!WALLY_DEBUG_POSTGRES_BIT) |
    (!WALLY_DEBUG_FOHH_CLIENT_ROW_BIT) |
    (!WALLY_DEBUG_POSTGRES_POLL_BIT) |
    (!WALLY_DEBUG_POSTGRES_FC_BIT) |
    (!WALLY_DEBUG_POSTGRES_CONN_BIT) |
    (!WALLY_DEBUG_POSTGRES_EVENT_BIT) |
    (!WALLY_DEBUG_ROW_DETAIL_BIT) |
    (!WALLY_DEBUG_WRITE_ROW_BIT) |
    (!WALLY_DEBUG_POSTGRES_WRITE_BIT) |
    (!WALLY_DEBUG_INTEREST_CB_BIT) |
    (!WALLY_DEBUG_POSTGRES_SQL_BIT) |
    (!WALLY_DEBUG_SYNC_PAUSE_BIT) |
    (!WALLY_DEBUG_HB_TIMEOUT_BIT) |
    (!WALLY_DEBUG_TABLE_QUEUE_BIT) |
    (!WALLY_DEBUG_MIC_BIT) |
    (!WALLY_DEBUG_RECOVERY_BIT) |
    0;

struct argo_log_collection *wally_event_log = NULL;
struct argo_log_collection *wally_stats_log = NULL;

/* RDS DB connection is down, Stop the trimmer functionality for all the tables */
bool wally_stop_all_cleanup(struct wally *wally)
{
	struct wally_table *t = NULL;
	uint32_t i = 0;
    bool ret = false;

    if (wally == NULL) {
        WALLY_LOG(AL_ERROR,"Wally struct NULL");
        return false;
    }

	ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);
	for (i = 0; i < wally->tables_count; i++) {

		t = wally->tables[i];

		/* already stopped, so skip the table */
		if (t->cleanup_param.state == wally_table_cleanup_stop) {
			continue;
		}

		if (!get_null_column(t)) {
			continue;
		}

		if (t->fully_loaded) {

			t->cleanup_param.state = wally_table_cleanup_pause;
            ret = true;
            wally->table_cleanup_pause = true;
			if (t->cleanup_stat.start_time_us != 0) {
				t->cleanup_stat.elapsed_us += epoch_us() - t->cleanup_stat.start_time_us;
				t->cleanup_stat.start_time_us = 0;
				t->cleanup_stat.request_time_us = 0;
			}
			WALLY_LOG(AL_INFO,"Table %s stops cleanup", t->name);
		}
	}
	ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
	WALLY_LOG(AL_INFO, "---- Operational mode all tables stop cleanup");

	return ret;
}

/* RDS DB connection recovered, restart the trimmer for the tables, which
 * were running before */
void wally_start_all_cleanup(struct wally *wally)
{
	struct wally_table *t = NULL;
	int i = 0;

    if (wally == NULL) {
        WALLY_LOG(AL_ERROR,"Wally struct NULL");
        return;
    }

	ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);
    wally->table_cleanup_pause = false;
    for (i = 0; i < wally->tables_count; i++) {

        t = wally->tables[i];

        /* Restart the tables only paused by operational mode  */
        if (t->cleanup_param.state != wally_table_cleanup_pause) {
            continue;
        }
        t->cleanup_param.state = wally_table_cleanup_scanning;
        t->cleanup_stat.start_time_us = 0;
        t->cleanup_stat.request_time_us = 0;
        WALLY_LOG(AL_INFO, "Operational-mode: Table name %s cleanup state = %d",
                t->name, t->cleanup_param.state);
    }
	ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
	WALLY_LOG(AL_INFO, "---- Operational mode all tables start cleanup");

	return;
}

/* get cleanup process rate: rows/sec */
double get_process_rate(struct wally_table *t) {
    int64_t elapsed_us = t->cleanup_stat.elapsed_us;
    int64_t processed;

    if (t->cleanup_param.state != wally_table_cleanup_stop) {
        if (t->cleanup_param.state == wally_table_cleanup_scanning) {
            processed = t->cleanup_stat.scanned_count;
        } else {
            processed = t->cleanup_stat.touched_count;
        }
        if (t->cleanup_stat.start_time_us != 0) {
            elapsed_us += epoch_us() - t->cleanup_stat.start_time_us;
        }

        if (elapsed_us > t->cleanup_param.cleanup_interval_us) {
            t->cleanup_stat.process_rate = processed*1000000.0/elapsed_us;
        } else {
           t->cleanup_stat.process_rate = processed*1000000.0/t->cleanup_param.cleanup_interval_us;
        }
    }

    return t->cleanup_stat.process_rate;
}

int fill_wally_table_cleanup_stats(void *cookie, int counter, void *structure_data)
{
    struct wally_table *t = cookie;
    struct wally_table_cleanup_stats *stats = structure_data;

    stats->state = t->cleanup_param.state;
    stats->max_cleanup_rows = t->cleanup_param.max_cleanup_rows;
    stats->max_scan_rows= t->cleanup_param.max_scan_rows;
    stats->cleanup_interval_us = t->cleanup_param.cleanup_interval_us;
    stats->row_expire_sec = t->cleanup_param.row_expire_sec;
    stats->rotated_rows = t->cleanup_stat.rotated_rows;
    stats->deleted_rows = t->cleanup_stat.deleted_rows;
    stats->proccesed_rows = t->cleanup_stat.rotated_rows + t->cleanup_stat.deleted_rows;
    stats->scanned_rows = t->cleanup_stat.scanned_rows;
    stats->set_min_seq_times = t->cleanup_stat.set_min_seq_times;
    stats->max_retry_count = t->cleanup_stat.max_retry_count;
    stats->scanning_time_us = t->cleanup_stat.scan_time_us;
    stats->cleanup_time_us = t->cleanup_stat.clean_time_us;
    stats->process_time_us = t->cleanup_stat.process_time_us;
    stats->retry_time_us = t->cleanup_stat.retry_time_us;
    stats->retry_time_poll_us = t->cleanup_stat.retry_time_poll_us;
    stats->retry_time_busy_us = t->cleanup_stat.retry_time_busy_us;
    stats->retry_time_waiting_us = t->cleanup_stat.retry_time_waiting_us;
    stats->retry_time_error_us = t->cleanup_stat.retry_time_error_us;

    stats->process_row_per_sec = get_process_rate(t);

    return ARGO_RESULT_NO_ERROR;
}

int fill_wally_table_stats(void *cookie, int counter, void *structure_data)
{
    struct wally_table *t = cookie;
    struct wally_table_stats *stats = structure_data;
    if (!t || !stats) {
        return ARGO_RESULT_NO_ERROR;
    }

    stats->interest_count = t->interest_count;

    /* stats for interest_state  */
    stats->interest_registered = t->interest_state_count[registered];
    stats->registered_waiting_for_response = t->interest_state_count[registered_waiting_for_response];
    stats->interest_unregistered = t->interest_state_count[unregistered];
    stats->interest_registration_xmit_pending = t->interest_state_count[registration_xmit_pending];
    stats->interest_deregistration_xmit_pending = t->interest_state_count[deregistration_xmit_pending];
    stats->interest_passive = t->interest_state_count[passive];

    stats->total_row_count = t->row_count;

    stats->last_update_time_s = t->last_update_time_s;
    stats->max_sequence_seen = t->max_sequence_seen;

    stats->interest_registered_long_pending = t->interest_pending_long;
    stats->interest_registered_pending_10s = t->interest_pending_count_window[0];
    stats->interest_registered_pending_20s = t->interest_pending_count_window[1];
    stats->interest_registered_pending_30s = t->interest_pending_count_window[2];
    stats->interest_registered_pending_40s = t->interest_pending_count_window[3];
    stats->interest_registered_pending_50s = t->interest_pending_count_window[4];
    stats->interest_registered_pending_60s = t->interest_pending_count_window[5];

    stats->row_store_time = t->row_store_time;
    stats->row_callback_time = t->row_callback_time;
    stats->row_process_time = t->row_process_time;

    t->row_store_time = 0;
    t->row_callback_time = 0;
    t->row_process_time = 0;
    t->interest_pending_count_window[0] = 0;
    t->interest_pending_count_window[1] = 0;
    t->interest_pending_count_window[2] = 0;
    t->interest_pending_count_window[3] = 0;
    t->interest_pending_count_window[4] = 0;
    t->interest_pending_count_window[5] = 0;

     stats->write_queue_in = 0;
     stats->write_queue_out = 0;
     stats->write_queue_fail = 0;
     stats->total_executed_batch = 0;
     stats->total_success_batch = 0;
     stats->total_failed_batch = 0;
     stats->query_execution_time = 0;
     stats->query_creation_time  = 0;
     stats->batch_size =  wally_gbl_cfg.write_batch_size;
     stats->last_sequence_write = 0;

     stats->recovery_state = t->recovery_stats.state;
     stats->recovery_start_time = t->recovery_stats.start_time;
     stats->recovery_requests = t->recovery_stats.recovery_requests;
     stats->recovery_sequence = t->recovery_stats.sequence;
     stats->recovery_received_rows = t->recovery_stats.received_rows;
     stats->recovered_rows = t->recovery_stats.recovered_rows;
     stats->recovery_duration = t->recovery_stats.duration;
     stats->recovered_clients = t->recovery_stats.clients;
     stats->recovery_status = t->recovery_stats.status;
     stats->recovery_incomplete_report = t->recovery_stats.incomplete_reports;
     stats->recovery_timeout = t->recovery_stats.total_timeout;
     wally_reset_recovery_mode(t, RECOVERY_RESET_STATS);

     for (int ix = 0; ix < t->wally->origins_count; ix++) {
         struct wally_origin *origin = t->wally->origins[ix];
         int index = t->origin_write_queue_index[ix];
         int wq_count;
            // only count_in, count_out, count_fail are meaningful in accumulation
            wq_count = origin->write_queue_count;
            if (!origin->is_writable) {
                continue;
            }
            if (wq_count < index) {
                continue;
            }
            stats->write_queue_in += origin->wq_info[index].count_in;
            stats->write_queue_out += origin->wq_info[index].count_out;
            stats->write_queue_fail += origin->wq_info[index].count_fail;
            stats->total_executed_batch += origin->wq_info[index].total_executed_batch ;
            stats->total_success_batch += origin->wq_info[index].total_success_batch;
            stats->total_failed_batch += origin->wq_info[index].total_failed_batch;
            stats->query_execution_time += origin->wq_info[index].query_execution_time;
            stats->query_creation_time += origin->wq_info[index].query_creation_time;
            // batch size change only in case of failure
            if(origin->wq_info[index].total_failed_batch) {
               stats->batch_size = origin->wq_info[index].batch_size;
            }
            stats->mem_reallocation_count += origin->wq_info[index].mem_reallocation;
            stats->batch_row_conflict += origin->wq_info[index].batch_row_conflict;
            stats->object_out_of_memory += origin->wq_info[index].object_out_of_memory;
            stats->sequence_unordered += origin->wq_info[index].sequence_unordered;
            // only one origin write to local db
            if(stats->last_sequence_write < origin->wq_info[index].last_sequence) {
                stats->last_sequence_write = origin->wq_info[index].last_sequence;
            }
     }
     stats->write_queue_depth = stats->write_queue_in - stats->write_queue_out;
     stats->write_queue_in_rate = stats->write_queue_in - stats->write_queue_in_rate;
     stats->write_queue_out_rate = stats->write_queue_out - stats->write_queue_out_rate;

     return ARGO_RESULT_NO_ERROR;
}

struct wally_callback_track {
    struct wally *wally;
    struct wally_table *table;
    struct wally_index_column *column;
    int visited_count;
    int in_state[WALLY_ORIGIN_STATE_COUNT];
    int to_state[WALLY_ORIGIN_STATE_COUNT];
};

void set_row_sequence(struct wally_row *row, int64_t sequence)
{
    if (row && row->current_row) {
        struct argo_object *object = row->current_row;
        argo_object_set_sequence(object, sequence);
    }
}

int64_t get_row_sequence(struct wally_row *row, int64_t default_val)
{
    int64_t seq = default_val;
    if (row && row->current_row) {
        seq = argo_object_get_sequence(row->current_row);
    }
    return seq;
}

void registrant_interest_change_state_stat(struct wally_interest *interest, enum interest_db_state state)
{
    int64_t now = epoch_us();
    uint8_t window_index = 0;
    if (!interest || !interest->column ||!interest->column->table) {
        return;
    }

    struct wally *wally = interest->column->table->wally;
    if (wally) {
        wally->registrant_stat.interest_state_count[interest->origin_db_state] --;
        wally->registrant_stat.interest_state_count[state] ++;

        if (state == registered && wally->registrant_stat.last_stats_update_us ) {
            int64_t delta = now - wally->registrant_stat.last_stats_update_us;
            window_index = delta/(INTEREST_PENDING_WINDOW_INTERVAL);

            if (window_index >= WALLY_INT_REG_HIS_SIZE ) {
                window_index = WALLY_INT_REG_HIS_SIZE - 1;
            }
            while (window_index < WALLY_INT_REG_HIS_SIZE ) {
                wally->registrant_stat.interest_registered_his[window_index] ++;
                window_index ++;
            }
        }
    }
}

void registrant_interest_add_state_stat(struct wally_interest *interest)
{
    if (!interest || !interest->column ||!interest->column->table) {
        return;
    }

    struct wally *wally = interest->column->table->wally;
    if (wally) {
        wally->registrant_stat.interest_state_count[interest->origin_db_state] ++;
    }
}

void registrant_interest_remove_state_stat(struct wally_interest *interest)
{
    if (!interest || !interest->column ||!interest->column->table) {
        return;
    }

    struct wally *wally = interest->column->table->wally;
    if (wally) {
        wally->registrant_stat.interest_state_count[interest->origin_db_state] --;
    }
}


void interest_assign_state(struct wally_interest *interest, enum interest_db_state state)
{
    struct wally_table *table = interest->column->table;

    if (interest->ext_reg_cb_count > 0) {
         registrant_interest_change_state_stat(interest,state);
    }

    table->interest_state_count[interest->origin_db_state]--;
    interest->origin_db_state = state;;
    table->interest_state_count[interest->origin_db_state]++;
}

void interest_remove_state(struct wally_interest *interest)
{
    struct wally_table *table = interest->column->table;

    table->interest_state_count[interest->origin_db_state]--;

    if (interest->ext_reg_cb_count > 0) {
         registrant_interest_remove_state_stat(interest);
    }
}

void interest_add_state(struct wally_interest *interest, enum interest_db_state state)
{
    struct wally_table *table = interest->column->table;

    interest->origin_db_state = state;;
    table->interest_state_count[interest->origin_db_state]++;

    if (interest->ext_reg_cb_count > 0) {
         registrant_interest_add_state_stat(interest);
    }
}

void wally_init(void)
{
    wally_event_log = argo_log_get("event_log");
    wally_stats_log = argo_log_get("statistics_log");
    wally_stats_description = argo_register_global_structure(WALLY_STATS_HELPER);
    generic_row_description = argo_register_global_structure(GENERIC_ROW_HELPER);
    wally_db_stats_description = argo_register_global_structure(WALLY_DB_STATS_HELPER);
    wally_db_table_stats_description = argo_register_global_structure(WALLY_DB_TABLE_STATS_HELPER);
    wally_fohh_connection_stats_description = argo_register_global_structure(WALLY_FOHH_CONNECTION_STATS_HELPER);
    wally_registrant_stats_description = argo_register_global_structure(WALLY_REGISTRANT_STATS_HELPER);
    wally_sync_pause_stats_description = argo_register_global_structure(WALLY_SYNC_PAUSE_STATS_HELPER);
    wally_db_pg_table_bootup_stats_description = argo_register_global_structure(WALLY_POSTGRES_TABLE_BOOTUP_STATS_HELPER);
    wally_db_pg_table_stats_description = argo_register_global_structure(WALLY_POSTGRES_TABLE_STATS_HELPER);
    wally_postgres_stats_description = argo_register_global_structure(WALLY_POSTGRES_TABLE_BOOTUP_STATS_HELPER);
    wally_table_stats_description = argo_register_global_structure(WALLY_TABLE_STATS_HELPER);
    wp_db_stats_description = argo_register_global_structure(WP_DB_STATS_HELPER);
    wp_db_table_stats_description = argo_register_global_structure(WP_DB_TABLE_STATS_HELPER);

	(void)wally_layer_init();
}
struct argo_structure_description* get_wt_desc(){
    static int reg = 0;
    if ( !reg ) {
        wt_desc = argo_register_global_structure(WT_HELPER);
    }
    return wt_desc;
}

static char *stringit(char *dest, size_t dest_len, int *vals)
{
    char *s, *e;
    size_t i;
    s = dest;
    e = s + dest_len;
    for (i = 0; i < WALLY_ORIGIN_STATE_COUNT; i++) {
        s += sxprintf(s, e, " %s:%-5d ", wally_origin_state_short_strings[i], vals[i]);
    }
    return dest;
}

/*
 * This routine simply runs the state machine for the interests.
 */
int wally_walk_f(void *cookie, void *object, void *key, size_t key_len)
{
    struct wally_callback_track *track = (struct wally_callback_track *) cookie;
    struct wally_interest *interest = (struct wally_interest *) object;
    int64_t now_s = 0;
    int64_t delta_s = 0;
    struct zthread_info *zthread = track->wally->zthread;

#if 0
    if (interest->column->is_null) {
    } else {
        if (interest->column->data_type == argo_field_data_type_string) {
            fprintf(stderr, "Check state, table = %s, column = %s, key(string) = %s, origin = %d, interest state = %d, promote = %d\n",
                    interest->column->table->name,
                    interest->column->table->argo_description->description[interest->column->argo_field_index]->public_description.name,
                    interest->key_string,
                    interest->origin_db_index,
                    interest->origin_db_state,
                    interest->origin_db_promote);
        } else if (interest->column->data_type == argo_field_data_type_integer) {
            fprintf(stderr, "Check state, table = %s, column = %s, key(integer) = %ld\n",
                    interest->column->table->name,
                    interest->column->table->argo_description->description[interest->column->argo_field_index]->public_description.name,
                    (long)interest->key);
        } else if (interest->column->data_type == argo_field_data_type_binary) {
            fprintf(stderr, "Check state, table = %s, column = %s, key(binary)\n",
                    interest->column->table->name,
                    interest->column->table->argo_description->description[interest->column->argo_field_index]->public_description.name);
        }
    }
#endif // 0


    track->visited_count++;

    enum interest_db_state old = interest->origin_db_state;
    wally_interest_state_machine(interest);
    if (interest->origin_db_state != old) {
        track->to_state[interest->origin_db_state]++;
    }
    track->in_state[interest->origin_db_state]++;

    /*
     * FSM walk runs for all the registrant callback, and it could delay the HB. So tick the
     * HB for every "wally_max_hb_iteration" once
     * */
    if (zthread && (track->visited_count % wally_max_hb_iteration) == 0) {
        now_s = monotime_s();
        delta_s = (now_s - zthread->last_heartbeat_monotime_s);
        if (delta_s > wally_max_hb_miss_timeout_s) {
            WALLY_DEBUG_HB_TIMEOUT("Interest FSM walk heartbeat threshold reached. count %d delta_s %" PRId64 "",
                    track->visited_count, delta_s);
            track->wally->batch_hb_count++;
            zthread_heartbeat(zthread);
        }
    }

     if (track->visited_count > MAX_TIMEOUT_TRACK_COUNT) return WALLY_RESULT_INCOMPLETE;
     return WALLY_RESULT_NO_ERROR;
}

struct wally_index_column *get_null_column(struct wally_table *t)
{
    struct wally_index_column *column = NULL;
    size_t i;
    if(t) {
        for (i = 0; i < t->indexed_columns_count; i++) {
            if (t->indexed_columns[i] && t->indexed_columns[i]->is_null) {
                column = t->indexed_columns[i];
                break;
            }
        }
    }
    return column;
}

/*
 * Cleanup Table (remove out of date soft-deleted rows)
 * Must be called with wally lock
 */
int wally_cleanup_table(struct wally_callback_track *track)
{
    struct wally *wally = track->wally;
    struct wally_table *t = track->table;
    struct wally_index_column *column = track->column;

    wally_xfer_callout_cleanup_f *cleanup_callback = NULL;
    int state;
    int64_t max_cleanup_rows;
    int64_t max_scan_rows;
    int64_t cleanup_interval_us;
    int64_t row_expire_sec;
    void *wp_db;
    int res = WALLY_RESULT_NO_ERROR;
    int64_t now_us = epoch_us();

	ZPATH_RWLOCK_WRLOCK(&(t->lock), __FILE__, __LINE__);
    if (!column->is_null || !t->fully_loaded) {
        /* only cleanup if it is NULL column and wally has only one origin and table is fully loaded */
	   ZPATH_RWLOCK_UNLOCK(&(t->lock), __FILE__, __LINE__);
        return 0;
    }

    wp_db = wally->origins[0]->callout_handle;
    cleanup_callback = wally->origins[0]->cleanup;

    if (t->cleanup_unit_test) {
        t->cleanup_param.max_cleanup_rows     = MAX_CLEANUP_ROWS;
        t->cleanup_param.max_scan_rows        = MAX_SCAN_ROWS;
        t->cleanup_param.cleanup_interval_us  = CLEANUP_INTERVAL_US;
        t->cleanup_param.row_expire_sec       = ROW_EXPIRE_SEC;
    }

    if (t->cleanup_param.max_scan_rows < t->cleanup_param.max_cleanup_rows) {
        t->cleanup_param.max_scan_rows = t->cleanup_param.max_cleanup_rows;
    }
    max_cleanup_rows     = t->cleanup_param.max_cleanup_rows;
    max_scan_rows        = t->cleanup_param.max_scan_rows;
    cleanup_interval_us  = t->cleanup_param.cleanup_interval_us;
    row_expire_sec       = t->cleanup_param.row_expire_sec;
    state                = t->cleanup_param.state;

    if (t->last_cleanup_us == 0) {
        /* startup time */
        if (t->cleanup_unit_test) {
            /* skip one loop at startup time */
            t->last_cleanup_us = now_us;
        } else {
            /* delay cleanup start time by cleanup_delay_at_start in us
             * Default is CLEANUP_DELAY_AT_STARTUP_US 10 min */
            t->last_cleanup_us = now_us + cleanup_delay_at_start_us;
        }
    }

    if (state != wally_table_cleanup_stop && wp_db && cleanup_callback) {
        if ( now_us - t->last_cleanup_us > cleanup_interval_us) {
            struct wally_row *row;
            int i;
            int64_t seq = 0;
            int64_t del = 0;
            int64_t key_int=0;
            int64_t ts_sec = 0;

            t->last_cleanup_us = now_us;
            if (t->cleanup_stat.start_time_us == 0) t->cleanup_stat.start_time_us = now_us;

            if (t->cleanup_param.state == wally_table_cleanup_scanning) {

                /* in scanning state */

                if (t->cleanup_stat.restart) {
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: restart scanning from top of table: table=%s",  t->name);
                    t->cleanup_stat.touched_count = 0;
                    t->cleanup_stat.scanned_count = 0;
                    t->cleanup_stat.start_time_us = now_us;
                    t->cleanup_stat.elapsed_us = 0;
                    t->cleanup_stat.restart = 0;
                    t->cleanup_stop_sequence = 0;
                    t->cleanup_stop_key_int = 0;
                }

                int64_t cur_seq = get_row_sequence( &(t->cleanup_scan_start_row), 0);
                WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: scanning state: db=%s, table=%s, starting sequence=%" PRId64 "",
                                          t->db_name, t->name, cur_seq);

                if (cur_seq == 0) {
                    row = avl_get_first_object(column->tree);
                    if (row && !(t->cleanup_scan_start_row.current_row)) {
                        t->cleanup_scan_start_row.current_row = argo_object_copy(row->current_row);
                    }
                } else {
                    /* make sure the row is still in the tree */
                    row = avl_get_object(column->tree, &(t->cleanup_scan_start_row));
                }

                i = 0;
                do {
                    if (row) {
                        t->cleanup_stat.scanned_rows++;
                        t->cleanup_stat.scanned_count++;
                        /* first check modified_time */
                        if (ARGO_RESULT_NOT_FOUND == argo_object_read_int_by_column_name(row->current_row,
                                                                                         "modified_time",
                                                                                         (int64_t *)&ts_sec)) {
                            ts_sec = 0;
                        }

                        if (ts_sec ==  0) {
                            /* modified_time not found, then check creation_time */
                            if (ARGO_RESULT_NOT_FOUND == argo_object_read_int_by_column_name(row->current_row,
                                                                                             "creation_time",
                                                                                             (int64_t *)&ts_sec)) {
                                ts_sec = 0;
                            }
                        }

                        seq = get_row_sequence(row, 0);
                        if (t->integer_key) {
                            key_int = argo_object_get_key_int(row->current_row);
                        }
                        if (ts_sec == 0) {
                            WALLY_LOG(AL_CRITICAL, "Failted to find both modified and create time,"
                                                    "table=%s, sequence=%"PRId64", key_int=%"PRId64,
                                                    t->name, seq, key_int);
                        }
                        if (now_us/1000000 - ts_sec < row_expire_sec) {
                            /* rest rows are all within row_expire_sec, no need to cleanup */
                            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: timestamp has not expired, will re-scan from beginning. "
                                                      "table=%s, sequence=%"PRId64", ts_sec=%"PRId64
                                                      ", row_expire_sec=%"PRId64,
                                                      t->name, seq, ts_sec, row_expire_sec);
                            if (t->cleanup_unit_test) {
                                WALLY_LOG(AL_NOTICE, "cleanup_unit_test: timestamp has not expired");
                            }
                            break;
                        } else {
                            /* check deledted column */
                            del = argo_object_is_deleted(row->current_row);
                            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: scanning: check deleted, sequence=%"PRId64", deleted=%"PRId64
                                                      ", table=%s", seq, del, t->name);
                            if (del) {
                                /* need to cleanup */
                                t->cleanup_stop_sequence = seq;
                                t->cleanup_stop_key_int = key_int;
                            }
                        }
                        row = avl_get_next_object(column->tree, row);
                    } else {
                        /* restart scanning from lowest sequence */
                        if (t->cleanup_unit_test) {
                            WALLY_LOG(AL_NOTICE, "cleanup_unit_test: scanning reach end of table");
                        }
                        t->cleanup_stat.restart = 1;
                        WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: scanning reach end of table: table=%s",  t->name);
                        break;
                    }
                    i++;
                    track->visited_count++;
                    if (t->cleanup_unit_test) {
                        WALLY_LOG(AL_NOTICE, "cleanup_unit_test: i=%d, key_int=%ld, stop_seq=%ld",
                                             i, (long)key_int, (long)t->cleanup_stop_sequence);
                    }
                } while (i < max_scan_rows && track->visited_count < MAX_TIMEOUT_CLEANUP_TRACK_COUNT);

                if (!t->cleanup_unit_test && track->visited_count >= MAX_TIMEOUT_CLEANUP_TRACK_COUNT) {
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: scanning table=%s, visited_count: %d, i: %d",
                                  t->name, track->visited_count, i);
                    /*
                     * if we havent processed half of max rows, we will not increment the table index
                     * and will continue to process the same table in next interval
                     */
                    res = i < max_scan_rows/2 ? WALLY_RESULT_WOULD_BLOCK : WALLY_RESULT_INCOMPLETE;
                }

                if (t->cleanup_unit_test) {
                    WALLY_LOG(AL_NOTICE, "cleanup_unit_test: i=%d, key_int=%ld, stop_seq=%ld",
                                         i, (long)key_int, (long)t->cleanup_stop_sequence);
                }

                if (t->cleanup_stop_sequence) {
                    /* found at least a deleted row, switch to rotation and deletion */
                    t->cleanup_param.state = wally_table_cleanup_cleaning;
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: switch to cleaning state, sequence=%"PRId64
                                                          ", ts_sec=%" PRId64, seq, ts_sec);
                }

                cur_seq = get_row_sequence(row, 0);
                set_row_sequence(&(t->cleanup_scan_start_row), cur_seq);
                WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: next start sequence: %"PRId64, cur_seq);
                t->cleanup_stat.scan_time_us += epoch_us() - now_us;
                if (t->cleanup_unit_test) {
                    int64_t cur_id = 0;
                    if (row && row->current_row) cur_id = argo_object_get_key_int(row->current_row);
                    WALLY_LOG(AL_NOTICE, "cleanup_unit_test: next start id=%ld, seq=%ld", (long)cur_seq, (long)cur_id);
                }

                if (t->cleanup_unit_test) {
                    int64_t ts_test_sec = epoch_us()/1000000;
                    char cmd[512];
                    if (cur_seq == 13) {
                        /* test for the next start node being updated */
                        /* works with testing table initialized in wally_test_trim_table.sh */
                        snprintf(cmd, sizeof(cmd), "psql -U postgres -d wally_test"
                                                   " -c 'UPDATE wally_test SET modified_time = %"
                                                   PRId64 " where sequence = %" PRId64 ";'", ts_test_sec, cur_seq);
                        WALLY_LOG(AL_NOTICE, "cmd: %s", cmd);
                        system_nowarn(cmd);
                    }
                }

            } else if (state == wally_table_cleanup_cleaning) {

                /* in cleaning state */

                int sz = 0;
                int rc;
                int stop = 0;
                int del_cnt = 0;
                int clr_cnt = 0;
                int64_t key_int = 0;

                WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: in cleaning state db=%s, table=%s", t->db_name, t->name);

                if (t->cleanup_buf_sz < max_cleanup_rows) {
                    t->cleanup_sequence = WALLY_REALLOC(t->cleanup_sequence, max_cleanup_rows * sizeof(int64_t));
                    t->cleanup_deleted = WALLY_REALLOC(t->cleanup_deleted, max_cleanup_rows * sizeof(int64_t));
                    t->cleanup_key_int = WALLY_REALLOC(t->cleanup_key_int, max_cleanup_rows * sizeof(int64_t));
                    t->cleanup_rows = WALLY_REALLOC(t->cleanup_rows, max_cleanup_rows * sizeof(struct wally_row *));
                    t->cleanup_buf_sz = max_cleanup_rows;
                }

                seq = 0;
                row = avl_get_first_object(column->tree);
                while (row && row->current_row && sz < max_cleanup_rows && track->visited_count < MAX_TIMEOUT_CLEANUP_TRACK_COUNT) {
                    seq = argo_object_get_sequence(row->current_row);
                    del = argo_object_is_deleted(row->current_row);
                    if (t->integer_key) {
                        key_int = argo_object_get_key_int(row->current_row);
                    }
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: bufering row, table=%s, index=%d, sequence=%" PRId64
                                        ",ts_sec=%"PRId64", deleted=%"PRId64", key_int=%"PRId64,
                                        t->name, sz, seq, ts_sec, del, key_int);

                    t->cleanup_rows[sz] = row;
                    t->cleanup_sequence[sz] = seq;
                    t->cleanup_deleted[sz] = del;
                    t->cleanup_key_int[sz] = key_int;
                    sz++;
                    track->visited_count++;

                    if (del) {
                        del_cnt++;
                    } else {
                        clr_cnt++;
                    }
                    if (t->cleanup_unit_test) {
                        WALLY_LOG(AL_NOTICE, "cleanup_unit_test: sz=%d, seq=%ld, stop_seq=%ld",
                                              (int)sz, (long)seq, (long)t->cleanup_stop_sequence);
                    }
                    if (seq >= t->cleanup_stop_sequence) {
                        /* switch to scaning state after cleanup*/
                        WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: end of cleaning state, tabel=%s, sequence=%"
                                                  PRId64", key_int%"PRId64, t->name, seq, key_int);
                        stop = 1;
                        break;
                    }

                    row = avl_get_next_object(column->tree, row);
                }

                if (!t->cleanup_unit_test && track->visited_count >= MAX_TIMEOUT_CLEANUP_TRACK_COUNT) {
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: cleaning table=%s, visited_count: %d, sz: %d",
                                  t->name, track->visited_count, sz);
                    /*
                     * if we havent processed half of max rows, we will not increment the table index
                     * and will continue to process the same table in next interval
                     */
                    res = sz < max_cleanup_rows/2 ? WALLY_RESULT_WOULD_BLOCK : WALLY_RESULT_INCOMPLETE;
                }

                /* reach end of table, switch to scanning state after cleanup*/
                if(!row) {
                    stop = 1;
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: reached end of table, table=%s", t->name);
                }

                if (sz == 0) {
                    t->cleanup_param.state = wally_table_cleanup_scanning;
                    set_row_sequence(&(t->cleanup_scan_start_row), 0);
                } else {
                    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: cleanning %d rows, table=%s", sz, t->name);
                    rc = (cleanup_callback)(wp_db,
                                            t->name,
                                            t->cleanup_sequence,
                                            t->cleanup_deleted,
                                            t->cleanup_key_int,
                                            sz,
                                            t->cleanup_param.min_seq_auto_update);
                    if (rc == WALLY_TABLE_CLEANUP_NO_ERROR) {
                        t->cleanup_stat.deleted_rows += del_cnt;
                        t->cleanup_stat.rotated_rows += clr_cnt;
                        t->cleanup_stat.touched_count += del_cnt + clr_cnt;
                        t->cleanup_stat.set_min_seq_times++;
                        t->cleanup_retry_count = 0;
                        if(t->cleanup_stat.request_time_us) {
                            t->cleanup_stat.process_time_us += epoch_us() - t->cleanup_stat.request_time_us;
                        }

                        for (i = 0; i < sz; i++){
                            if (t->cleanup_deleted[i] != 0) {
                                wally_row_just_release(t, t->cleanup_rows[i]);
                            }
                        }
                        if (stop) {
                            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup:: switch to scanning state, sequence=%"
                                                      PRId64", key_int=%"PRId64", table=%s",
                                                      t->cleanup_stop_sequence, t->cleanup_stop_key_int, t->name);
                            t->cleanup_param.state = wally_table_cleanup_scanning;
                            t->cleanup_stop_sequence = 0;
                            t->cleanup_stop_key_int = 0;
                        }
                    } else {
                        t->cleanup_retry_count++;
                        if (t->cleanup_stat.max_retry_count < t->cleanup_retry_count) {
                            t->cleanup_stat.max_retry_count = t->cleanup_retry_count;
                        }
                        WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: rc=%d, previous cleanup for table %s is not done,"
                                                  "will retry from sequence %"PRId64", int_key %"PRId64,
                                                  rc, t->name, t->cleanup_sequence[0], t->cleanup_key_int[0]);
                        if(t->cleanup_stat.request_time_us) {
                            if (rc == WALLY_TABLE_CLEANUP_BUSY) {
                                t->cleanup_stat.retry_time_busy_us += epoch_us() - t->cleanup_stat.request_time_us;
                            } else if (rc == WALLY_TABLE_CLEANUP_POLL) {
                                t->cleanup_stat.retry_time_poll_us += epoch_us() - t->cleanup_stat.request_time_us;
                            } else if (rc == WALLY_TABLE_CLEANUP_WAITING) {
                                t->cleanup_stat.retry_time_waiting_us += epoch_us() - t->cleanup_stat.request_time_us;
                            } else if (rc == WALLY_TABLE_CLEANUP_ERROR) {
                                t->cleanup_stat.retry_time_error_us += epoch_us() - t->cleanup_stat.request_time_us;
                            }
                            t->cleanup_stat.retry_time_us += epoch_us() - t->cleanup_stat.request_time_us;
                        }
                    }
                    t->cleanup_stat.request_time_us = epoch_us();
                }

                t->cleanup_stat.clean_time_us += epoch_us() - now_us;

            } else {
                /* should never be here */
            }
        } else {
            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: start scanning table=%s, now_us: %"PRId64", last_cleanup time: %"PRId64", cleanup interval: %"PRId64,
                                      t->name, now_us, t->last_cleanup_us, cleanup_interval_us);
            res = WALLY_RESULT_WOULD_BLOCK;
        }
    }
   ZPATH_RWLOCK_UNLOCK(&(t->lock), __FILE__, __LINE__);
   return res;
}

/* Must call it with wally lock */
static void wally_cleanup_index_increment(struct wally *wally)
{
    wally->timeout_cleanup_table_index++;
    if (wally->timeout_cleanup_table_index >= wally->tables_count) {
        wally->timeout_cleanup_table_index = 0;
    }
}

static void wally_register_table_interests(struct wally *wally)
{
  struct wally_callback_track track;
  size_t ii;
  int res = WALLY_RESULT_NO_ERROR;

  memset(&track, 0, sizeof(track));
  track.wally = wally;

   //Adjust the reregn rate as needed. Right now it is set to 1
   for(ii=0; ii < WALLY_TABLE_REREGN_RATE; ii++) {

       //Rotated, let us start from the beginning
	   /* coverity[MISSING_LOCK:  FALSE] */
       if (wally->table_priority_idx >= wally->tables_count) wally->table_priority_idx = 0;

       track.table = wally->tables[wally->table_priority_idx];
       wally->table_priority_idx++;

       if (track.table) {
		   ZPATH_RWLOCK_WRLOCK(&(track.table->lock), __FILE__, __LINE__);
           if (track.table->fully_loaded) {
               track.column = get_null_column(track.table);

               if (track.column) {
                   if ((track.column->is_null) && (track.column->null_column_interest)) {
                       res = wally_walk_f(&track, track.column->null_column_interest, NULL, 0);
                       if (res) { //applicable only if reregn rate > 1. Else this is a NOOP
						   ZPATH_RWLOCK_UNLOCK(&(track.table->lock), __FILE__, __LINE__);
                           break;
                       } else {} //Fall through. done processing the interest machine restart
                   } else {} //Fall through to next null column. We are not interested in non-null
               } else {} //Fall through. Invalid column entry. Ignore?

           } else {} //Fall through to next table if table is NOT fully loaded
		   ZPATH_RWLOCK_UNLOCK(&(track.table->lock), __FILE__, __LINE__);
       } else {} // Next table
   } //END for

   return;
}


/*
 * Wally timeout thread. Kinda simple, for now.
 */
static void timeout_thread(int sock, short flags, void *cookie)
{
    struct wally *wally = (struct wally *) cookie;
    struct wally_callback_track track;
    struct wally_callback_track track1;
    int res = WALLY_RESULT_NO_ERROR;
    char out1[200];
    char out2[200];
    int do_cleanup = 0;
    int64_t now_s = 0, delta_s = 0;
    struct zthread_info *zthread = wally->zthread;

    memset(&track, 0, sizeof(track));
    memset(&track1, 0, sizeof(track1));

    track.wally = wally;
    zthread_heartbeat(zthread);

    wally_periodic_free(wally);

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    do_cleanup = (wally_is_wallyd(wally) && wally_is_fully_load_complete(wally)) && (wally->origins_count == 1) && (wally->origins[0]->cleanup);
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
	/*
	 * This code is added to accelerate leaf wally reconnect as part of origin wally switchover.
	 * So this is applicable for only leaf wallys
	 */
    if ((wally->origins_count > 1) && (wally->wally_tbl_intrst_reg_prio)) {
        wally_register_table_interests(wally);
    }
    while (1) {
        track.table = wally->tables[wally->timeout_table_index];
        if (track.table) {
			ZPATH_RWLOCK_WRLOCK(&(track.table->lock), __FILE__, __LINE__);

            wally_recovery_state_machine(track.table);

            track.column = track.table->indexed_columns[wally->timeout_column_index];
            if (track.column) {
                WALLY_DEBUG_TIMEOUT("%s: scanning table     = %45s, column = %20s, visited = %5d, changed: %s In: %s",
                                    wally->name,
                                    track.table->name,
                                    track.column->name,
                                    track.visited_count,
                                    stringit(out1, sizeof(out1), track.to_state),
                                    stringit(out2, sizeof(out2), track.in_state));
                if (track.column->is_null) {
                    if (track.column->null_column_interest) {
                        res = wally_walk_f(&track, track.column->null_column_interest, NULL, 0);
                    }
                } else if (track.column->data_type == argo_field_data_type_integer) {
                    res = zhash_table_walk(track.column->index_hash, &(wally->timeout_walk_key), wally_walk_f, &track);
                } else {
                    res = zhash_table_walk(track.column->string_index_hash, &(wally->timeout_walk_key), wally_walk_f, &track);
                }
                if (res) {
                    /* Stop scanning, and resume scanning after our little timer fires. */
                    WALLY_DEBUG_TIMEOUT("%s: scan stopped table = %45s, column = %20s, visited = %5d, changed: %s In: %s",
                                        wally->name,
                                        track.table->name,
                                        track.column->name,
                                        track.visited_count,
                                        stringit(out1, sizeof(out1), track.to_state),
                                        stringit(out2, sizeof(out2), track.in_state));
                    ZPATH_RWLOCK_UNLOCK(&(track.table->lock), __FILE__, __LINE__);
                    break;
                } else {
                    /* Fall through to next table or column... */
                }
            } else {
                /* Fall through to next table or column... */
            }
			ZPATH_RWLOCK_UNLOCK(&(track.table->lock), __FILE__, __LINE__);
        } else {
            /* Fall through to next table or column... */
        }

        /* Tick heartbeat before processing the next table */
        if (zthread) {
            now_s = monotime_s();
            delta_s = (now_s - zthread->last_heartbeat_monotime_s);
            if (delta_s > wally_max_hb_miss_timeout_s) {
                WALLY_DEBUG_HB_TIMEOUT("Timeout handler heartbeat threshold reached. table_index %d column_index %d delta_s %" PRId64 "" ,
                        wally->timeout_table_index, wally->timeout_column_index, delta_s);
                wally->batch_hb_count++;
                zthread_heartbeat(zthread);
            }
        }

		/* coverity[MISSING_LOCK:  FALSE] */
        wally->timeout_column_index++;
		/* coverity[MISSING_LOCK:  FALSE] */
        wally->timeout_walk_key = 0;
        if (track.table) {
            if (wally->timeout_column_index >= track.table->indexed_columns_count) {
                wally->timeout_column_index = 0;
				/* coverity[MISSING_LOCK:  FALSE] */
                wally->timeout_table_index++;
                if (wally->timeout_table_index >= wally->tables_count) {
                    wally->timeout_table_index = 0;
                    WALLY_DEBUG_TIMEOUT("%s: looped table, starting again", wally->name);
                    break;
                }
            }
        } else {
            wally->timeout_table_index = 0;
            wally->timeout_column_index = 0;
            WALLY_DEBUG_TIMEOUT("%s: looped table, starting again", wally->name);
            break;
        }

    }

    /* Cleanup Table */
    track1.visited_count = 0;
    int init_cleanup_table_index = wally->timeout_cleanup_table_index;
    while (do_cleanup) {
        track1.wally = wally;
        track1.table = wally->tables[wally->timeout_cleanup_table_index];
        res = WALLY_RESULT_NO_ERROR;
        if (track1.table) {
            track1.column = get_null_column(track1.table);
            if (track1.column) {
                res = wally_cleanup_table(&track1);
            }

        }
        ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);
        if (res != WALLY_RESULT_WOULD_BLOCK) wally_cleanup_index_increment(wally);
        if (res != WALLY_RESULT_NO_ERROR || wally->timeout_cleanup_table_index == init_cleanup_table_index) {
            char *tb_name = track1.table ? track1.table->name: "unknown";
            WALLY_DEBUG_TIMEOUT("%s: cleanup table %s, timeout_cleanup_table_index: %d", wally->name, tb_name, wally->timeout_cleanup_table_index);
            if (wally->timeout_cleanup_table_index == init_cleanup_table_index) {
                WALLY_DEBUG_TIMEOUT("%s: cleanup table %s, starting again", wally->name, tb_name);
            }
            ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
            break;
        } else {
            ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        }


    }

    zthread_heartbeat(zthread);

    struct timeval tv;
    tv.tv_sec = 0;
    tv.tv_usec = TIMEOUT_THREAD_INTERVAL_US;
    event_add(wally->timeout_thread_event, &tv);

    return;
}

void *wally_timeout_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct wally *wally = (struct wally *) cookie;
    struct event_base *ev_base = event_base_new();
    wally->timeout_thread_event = event_new(ev_base, -1, 0, timeout_thread, wally);
    wally->zthread = zthread_arg;
    struct timeval tv;
    tv.tv_sec = 1;
    tv.tv_usec = 0;
    event_add(wally->timeout_thread_event, &tv);

    zevent_base_dispatch(ev_base);

    return NULL;
}

int32_t wally_layer_init(void) {
	return wally_table_thread_init();
}

struct wally *wally_create(char *name,
                           int is_endpoint,
                           wally_debug_endpoints_init_f *debug_endpoints_init_cb,
                           wally_debug_register_f *debug_registration_callback,
                           void *callback_cookie,
                           wally_post_resume_callback_f *post_resume_callback)
{
    struct wally *w;

#if 0
    fprintf(stderr, "Wally_create; sizeof(struct wally_registrant_callback) == %d\n", (int) sizeof(struct wally_registrant_callback));
    fprintf(stderr, "Wally_create; sizeof(struct wally_row) == %d\n", (int) sizeof(struct wally_row));
    fprintf(stderr, "Wally_create; sizeof(struct wally_interest) == %d\n", (int) sizeof(struct wally_interest));
    fprintf(stderr, "Wally_create; sizeof(struct wally_index_column) == %d\n", (int) sizeof(struct wally_index_column));
    fprintf(stderr, "Wally_create; sizeof(struct wally_registrant) == %d\n", (int) sizeof(struct wally_registrant));
    fprintf(stderr, "Wally_create; sizeof(struct wally_table) == %d\n", (int) sizeof(struct wally_table));
    fprintf(stderr, "Wally_create; sizeof(struct wally_origin) == %d\n", (int) sizeof(struct wally_origin));
    fprintf(stderr, "Wally_create; sizeof(struct wally) == %d\n", (int) sizeof(struct wally));
#endif // 0

    if (!name) return NULL;

    w = (struct wally *) WALLY_MALLOC(sizeof(*w));
    if (w) {
        memset(w, 0, sizeof(*w));

        w->name = WALLY_MALLOC(strlen(name) + 1);
        if (!w->name) {
            WALLY_FREE(w);
            return NULL;
        }
        strcpy(w->name, name);


        ZTAILQ_INIT(&(w->slow_free_object_list));
        w->slow_free_object_list_lock = ZPATH_MUTEX_INIT;

        w->debug_registration_callback = debug_registration_callback;
        w->callback_cookie = callback_cookie;
        w->is_endpoint = is_endpoint;
        w->table_cleanup_pause = false;
        w->post_resume_callback = post_resume_callback;


        /* Hash table on name for each table. */
        w->tables_hash = zhash_table_alloc(&wally_allocator);
        if (!w->tables_hash) {
            WALLY_FREE(w->name);
            WALLY_FREE(w);
            return NULL;
        }

        for(int i = 0; i< WALLY_MAX_SERVER; i++) {
            w->wally_fohh_server_handle[i] = NULL;
        }

        /* Create global wally lock */
        w->lock = ZPATH_RWLOCK_INIT;
        //zpath_rwlock_init(&(w->lock), NULL, __FILE__, __LINE__);

        /* Create timeout/dispatch thread. */
        /* These are created with 10s timeout, because sometimes they
         * free a TON of data, and can take a second or so. */
        zthread_create(&(w->timeout_thread),
                       wally_timeout_thread,
                       w,
                       name,
                       wally_default_hb_timeout_s, /* 60s for wallyd and for others 20s thread watchdog */
                       16*1024*1024,   /* 16 MB stack. */
                       60*1000*1000,   /* 60s Statistics interval */
                       NULL);

        if (debug_endpoints_init_cb) debug_endpoints_init_cb(w);
    }
    return w;
}

char *wally_name(struct wally *wally)
{
    return wally->name;
}

int wally_is_endpoint(struct wally *wally)
{
    return wally->is_endpoint;
}

void wally_set_custom_data(struct wally *wally, void *data)
{
    wally->custom_data = data;
}

void *wally_get_custom_data(struct wally *wally)
{
    return wally->custom_data;
}

struct wally_origin *
wally_add_origin(struct wally *wally,
                 const char  *name,
                 void *callout_handle,
                 wally_xfer_callout_register_for_index_f *register_index,
                 wally_xfer_callout_deregister_for_index_f *deregister_index,
                 wally_xfer_callout_set_cookie_f *set_cookie,
                 wally_xfer_callout_get_status_f *get_status,
                 wally_xfer_callout_add_table_f *add_table,
                 wally_xfer_callout_set_min_sequence_f *set_sequence,
                 wally_xfer_callout_dump_state *dump_state,
                 int is_writable)
{
    return wally_add_origin_1(wally,
                              name,
                              callout_handle,
                              register_index,
                              deregister_index,
                              set_cookie,
                              get_status,
                              add_table,
                              set_sequence,
                              dump_state,
                              NULL, NULL,
                              is_writable);
}


struct wally_origin *
wally_add_origin_1(struct wally *wally,
                   const char  *name,
                   void *callout_handle,
                   wally_xfer_callout_register_for_index_f *register_index,
                   wally_xfer_callout_deregister_for_index_f *deregister_index,
                   wally_xfer_callout_set_cookie_f *set_cookie,
                   wally_xfer_callout_get_status_f *get_status,
                   wally_xfer_callout_add_table_f *add_table,
                   wally_xfer_callout_set_min_sequence_f *set_sequence,
                   wally_xfer_callout_dump_state *dump_state,
                   wally_xfer_callout_cleanup_f *cleanup,
                   wally_xfer_callout_cmd_trigger_f *cmd_trigger,
                   int is_writable)
{
    struct wally_origin *o_db;
    int i;
    char *tmp;
    pthread_mutexattr_t lock_attr;

    /* Write lock on wally. */
    ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);

    /* Verify we have room. */
    if (wally->origins_count >= WALLY_MAX_ORIGINS) {
        ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        return NULL;
    }

    o_db = (struct wally_origin *) WALLY_MALLOC (sizeof *o_db);
    if (o_db) {
        memset(o_db, 0, sizeof (*o_db));

        tmp = WALLY_MALLOC(strlen(name) + 1);
        if (!tmp) {
            WALLY_FREE(o_db);
            ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
            return NULL;
        }
        strcpy(tmp, name);
        o_db->name = tmp;

        o_db->wally = wally;
        o_db->callout_handle = callout_handle;
        o_db->register_index = register_index;
        o_db->deregister_index = deregister_index;
        o_db->set_cookie = set_cookie;
        o_db->get_status = get_status;
        o_db->last_status = wally_origin_status_not_ready;
        o_db->add_table = add_table;
        o_db->set_sequence = set_sequence;
        o_db->dump_state = dump_state;
        o_db->cleanup = cleanup;
        o_db->is_writable = is_writable;
        o_db->cmd_trigger = cmd_trigger;
        o_db->pauseable = 0;

        ZTAILQ_INIT(&(o_db->registering_interests_list));
        ZTAILQ_INIT(&(o_db->deregistering_interests_list));
        for (i = 0; i < WALLY_MAX_TABLES; i++) {
            ZTAILQ_INIT(&(o_db->write_queue[i]));
        }
        pthread_mutexattr_init(&lock_attr);
        pthread_mutexattr_settype(&lock_attr, PTHREAD_MUTEX_NORMAL);
        pthread_mutex_init(&(o_db->write_queue_lock), &lock_attr);
        pthread_mutex_init(&(o_db->interest_register_list_lock), &lock_attr);
        pthread_mutex_init(&(o_db->interest_deregister_list_lock), &lock_attr);
        (set_cookie)(callout_handle, o_db);

        o_db->my_index = wally->origins_count;
        wally->origins[wally->origins_count] = o_db;
        wally->origins_count++;
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    return o_db;
}

struct wally_origin *
wally_get_origin(struct wally *wally,
                 int origin_index)
{
    struct wally_origin *ret = NULL;
    ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);

    if ((origin_index < wally->origins_count) &&
        (origin_index >= 0)) {
        ret = wally->origins[origin_index];
    }

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    return ret;
}

int wally_dump_origins(struct wally *wally, char *buf, size_t buf_len, int quick)
{
    size_t ix;
    char *s = buf;
    char *e = buf + buf_len;
    int64_t total_rows = 0, total_maxseq = 0;

    s +=sxprintf(s, e, "Wally %s, origins:\n", wally->name);
    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    for (ix = 0; ix < wally->origins_count; ix++) {
        struct wally_origin *origin = wally->origins[ix];
        int reg_count = 0;
        int dereg_count = 0;
        struct wally_interest *walk;
        ZTAILQ_FOREACH(walk, &(origin->registering_interests_list), origin_db_processing) {
            reg_count++;
            if (reg_count == 9999) break;
        }
        ZTAILQ_FOREACH(walk, &(origin->deregistering_interests_list), origin_db_processing) {
            dereg_count++;
            if (dereg_count == 9999) break;
        }
        s += sxprintf(s, e, "  Origin %zu: %s: Regs: %d, Deregs: %d, incarnation=%d, status=%d\n",
                      ix, origin->name, reg_count, dereg_count, origin->incarnation, origin->last_status);
        s += (origin->dump_state)(origin->callout_handle, s, e);
    }
    if (quick) {
        int64_t total_total[WALLY_ORIGIN_STATE_COUNT + 1];
        memset(total_total, 0, sizeof(total_total));

#define XX(a, b, c)  s += sxprintf(s, e, "%20.20s", c);
        INTEREST_DB_STATE_XX(XX);
#undef XX
        s += sxprintf(s, e, "%20.20s", "Total");
        s += sxprintf(s, e, "%20.20s", "Max Seq");
        s += sxprintf(s, e, "%20.20s", "Row Count");
        s += sxprintf(s, e, "  Indexes FL MI %s <%d>\n", "Table (state count)", (int)WALLY_ORIGIN_STATE_COUNT);

        for (ix = 0; ix < wally->tables_count; ix++) {
            struct wally_table *table = wally->tables[ix];
            int64_t total = 0;
            int o_ix = 0;

#define XX(a, b, c)                                                                                     \
            s += sxprintf(s, e, "%20ld", (long)table->interest_state_count[(enum interest_db_state)a]); \
            total += table->interest_state_count[(enum interest_db_state)a];                            \
            total_total[o_ix] += table->interest_state_count[(enum interest_db_state)a];                \
            o_ix++;

            INTEREST_DB_STATE_XX(XX);
#undef XX
            total_total[o_ix] += total;
            total_rows += table->row_count;
            s += sxprintf(s, e, "%20ld", (long)total);
            s += sxprintf(s, e, "%20" PRId64 "", table->max_sequence_seen);
            s += sxprintf(s, e, "%20" PRId64 "", table->row_count);

            s += sxprintf(s, e, "        %ld  %d  %d", (long) (table->last_registered_column + 1), table->fully_loaded, table->multiple_index_consistency);

            s += sxprintf(s, e, " %s\n", table->name);
        }

        for (ix = 0; ix < WALLY_ORIGIN_STATE_COUNT; ix++) {
            s += sxprintf(s, e, "%20ld", (long)total_total[ix]);
        }
        s += sxprintf(s, e, "%20ld", (long)total_total[ix]);
        s += sxprintf(s, e, "%20" PRId64 "", total_maxseq);
        s += sxprintf(s, e, "%20" PRId64 "", total_rows);
        s += sxprintf(s, e, "            %s\n", "Totals");
    } else {
        for (ix = 0; ix < wally->tables_count; ix++) {
            struct wally_table *table = wally->tables[ix];
            int64_t total = 0;
            s += sxprintf(s, e, "  Table %s:\n", table->name);

#define XX(a, b, c)                                                                                                     \
            s += sxprintf(s, e, "    %10ld : %s\n", (long)table->interest_state_count[(enum interest_db_state)a], b);   \
            total += table->interest_state_count[(enum interest_db_state)a];
            INTEREST_DB_STATE_XX(XX);
#undef XX
            s += sxprintf(s, e, "    %10ld : Total\n", (long)total);
        }
    }

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    return WALLY_RESULT_NO_ERROR;
}



/* This internal function assumes we already have a write lock on the
 * wally table. It needs to snag a global argo read lock as well,
 * 'cause it's accessing internal argo state.
 *
 * See non-internal version/header for documentation.
 */
static struct wally_index_column *wally_table_get_index_internal(struct wally_table *t, const char *column_name)
{
    struct wally_index_column *column;
    struct argo_private_field_description *fd = NULL;
    int res;
    int is_null = 0;

    if (!column_name || (strlen(column_name) == 0)) {
        column_name = "";
        is_null = 1;
    }

    column = zhash_table_lookup(t->column_indexes, column_name, strlen(column_name), NULL);
    if (column) return column;

    /* Need to grab an argo read lock to look up internal argo state
     * for the column */
    if (!is_null) {
        argo_read_lock();
        fd = zhash_table_lookup(t->argo_description->described_fields, column_name, strlen(column_name), NULL);
        /* Once we have a private description, it will never go away (argo
         * don't roll that way) so we can drop the lock */
        argo_unlock();
        if (!fd) {
            /* XXX TO DO: Dynamically add column if it did not already exist */
            WALLY_LOG(AL_ERROR, "Could not find column named %s", column_name);
            return NULL;
        }
    }

    if (t->indexed_columns_count >= WALLY_MAX_INDEXED_COLUMNS) {
        /* XXX LOG */
        int i;
        WALLY_LOG(AL_ERROR, "Exceeded maximum indexed columns (compile option) asking for name <%s>", column_name);
        for (i = 0; i < WALLY_MAX_INDEXED_COLUMNS; i++) {
            WALLY_LOG(AL_INFO, "Column %d = %s", i,
                      t->argo_description->description[t->indexed_columns[i]->argo_field_index]->public_description.field_name);
        }
        return NULL;
    }

    /* Create an index. */
    column = (struct wally_index_column *) WALLY_MALLOC(sizeof(*column));
    if (!column) return NULL;

    memset(column, 0, sizeof(*column));

    column->name = WALLY_STRDUP(column_name, strlen(column_name));

    column->table = t;
    if (!is_null) {
        column->argo_field_index = fd->index;
    }
    column->is_null = is_null;

    /* Allocate AVL tree for sequence. */
    column->tree = avl_create(wally_avl_comparator, column);
    if (!column->tree) {
        if (column->name) WALLY_FREE(column->name);
        WALLY_FREE(column);
        return NULL;
    }

    if (!is_null) {
        /* allocating hash table for holding paused keys when table is paused */
        column->paused_keys = zhash_table_alloc(&wally_allocator);
        if (!column->paused_keys) {
            avl_free(column->tree);
            WALLY_FREE(column);
            return NULL;
        }

        switch (fd->public_description.argo_field_type) {
        case argo_field_data_type_binary:
            column->data_type = argo_field_data_type_binary;
            goto hop;
        case argo_field_data_type_string:
            column->data_type = argo_field_data_type_string;
        hop:
            /* Verify it's a reference. */
#if 0
            if (!fd->public_description.is_reference) {
                /* XXX LOG! */
                fprintf(stderr, "%s:%s:%d: Unsupported index column type- non-referenced string, %s\n",
                        __FILE__, __FUNCTION__, __LINE__, fd->public_description.name);
                avl_free(column->tree);
                if (column->name) WALLY_FREE(column->name);
                WALLY_FREE(column);
                return NULL;
            }
#endif // 0

            /* Allocate hash table as argo string hash. */
            column->string_index_hash = zhash_table_alloc(&wally_allocator);
            if (!column->string_index_hash) {
                zhash_table_free(column->paused_keys);
                avl_free(column->tree);
                if (column->name) WALLY_FREE(column->name);
                WALLY_FREE(column);
                return NULL;
            }
            break;
        case argo_field_data_type_integer:
            column->data_type = argo_field_data_type_integer;

            /* Verify it's not a reference. */
            if (fd->public_description.is_reference) {
                zhash_table_free(column->paused_keys);
                avl_free(column->tree);
                if (column->name) WALLY_FREE(column->name);
                WALLY_FREE(column);
                return NULL;
            }

            /* Allocate hash table as wally index hash. */
            column->index_hash = zhash_table_alloc(&wally_allocator);
            if (!column->index_hash) {
                zhash_table_free(column->paused_keys);
                avl_free(column->tree);
                if (column->name) WALLY_FREE(column->name);
                WALLY_FREE(column);
                return NULL;
            }
            break;
        default:
            /* These are not supported. */
            /* XXX LOG! */
            WALLY_LOG(AL_ERROR, "Unsupported index column type %d",
                      fd->public_description.argo_field_type);
            zhash_table_free(column->paused_keys);
            avl_free(column->tree);
            if (column->name) WALLY_FREE(column->name);
            WALLY_FREE(column);
            return NULL;
            /*break;*/
        }
    }

    t->indexed_columns[t->indexed_columns_count] = column;
    column->my_index = t->indexed_columns_count;
    t->indexed_columns_count++;
    {
        /* Bah, deconst. We don't do this much, so this isn't too
         * bad. */
        size_t len = strlen(column_name);
        char *cpy = WALLY_STRDUP(column_name, len);
        res = zhash_table_store(t->column_indexes, cpy, len, 0, column);
        WALLY_FREE(cpy);
    }
    if (res) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "Err: hash store returned %s when storing column", wally_result_string(res));
    }

    if (t->wally->debug_registration_callback) {
        (t->wally->debug_registration_callback)(column, t->wally->callback_cookie);
    }

    WALLY_DEBUG_TABLE("%s: %s: Created Column %s", t->wally->name, t->name, column_name);

    return column;
}

/*
 * wally_table_get_index_simple_internal
 *  This internal function assumes we already have a write lock on the wally table.
 *  This function is a lookup-only version ofwally_table_get_index_internal
 */
static struct wally_index_column *wally_table_get_index_simple_internal(struct wally_table *t,
                                                                        const char *column_name)
{
    struct wally_index_column *column = NULL;
    size_t column_length = 0;

    if (!column_name || (strlen(column_name) == 0)) {
        column_name = "";
        column_length = 0;
    } else {
        column_length = strlen(column_name);
    }
    column = zhash_table_lookup(t->column_indexes, column_name, column_length, NULL);
    return column;
}

static int wally_avl_comparator(void *a, void *b, void *cookie)
{
    struct argo_object *A = ((struct wally_row *) a)->current_row;
    struct argo_object *B = ((struct wally_row *) b)->current_row;
    struct wally_index_column *column = (struct wally_index_column *) cookie;
    int64_t comp;

    if (!column->is_null) {
        if (column->data_type == argo_field_data_type_string) {
            char *str_a;
            char *str_b;
            argo_object_read_string_by_column_index(A, column->argo_field_index, &str_a);
            argo_object_read_string_by_column_index(B, column->argo_field_index, &str_b);
            if (!str_a) str_a = "";
            if (!str_b) str_b = "";
            comp = strcmp(str_a, str_b);
        } else if (column->data_type == argo_field_data_type_integer) {
            int64_t int_a;
            int64_t int_b;
            argo_object_read_int_by_column_index(A, column->argo_field_index, &int_a);
            argo_object_read_int_by_column_index(B, column->argo_field_index, &int_b);
            //WALLY_LOG(AL_DEBUG, "A = %ld, B = %ld", (long) int_a, (long) int_b);
            comp = int_a - int_b;
        } else if (column->data_type == argo_field_data_type_binary) {
            uint8_t *value_a;
            size_t value_a_len;
            uint8_t *value_b;
            size_t value_b_len;
            size_t smaller;
            argo_object_read_binary_by_column_index(A, column->argo_field_index, &value_a, &value_a_len);
            argo_object_read_binary_by_column_index(B, column->argo_field_index, &value_b, &value_b_len);
            smaller = (value_a_len < value_b_len) ? value_a_len : value_b_len;
            comp = memcmp(value_a, value_b, smaller);
            if (comp == 0) {
                if (value_a_len < value_b_len) {
                    comp = -1;
                } else if (value_a_len > value_b_len) {
                    comp = 1;
                } else {
                    comp = 0;
                }
            }
        } else {
            comp = 0;
        }
    } else {
        comp = 0;
    }
    if (comp == 0) {
        int s_index = column->table->row_sequence_argo_index;
        int64_t int_a;
        int64_t int_b;
        argo_object_read_int_by_column_index(A, s_index, &int_a);
        argo_object_read_int_by_column_index(B, s_index, &int_b);
        comp = int_a - int_b;
    }

    if (comp < 0) return -1;
    if (comp > 0) return 1;
    return 0;
}


static int wally_table_add_origin_locked(struct wally *wally,
                                         struct wally_table *table,
                                         struct wally_origin *origin)
{
    int res;
    if (table->origins_count >= WALLY_MAX_ORIGINS) {
        return WALLY_RESULT_NO_MEMORY;
    }

    if (origin->add_table) {
        res = (origin->add_table)(origin->callout_handle, table->db_name, table->name);
    } else {
        res = WALLY_RESULT_NO_ERROR;
    }

    if (res == WALLY_RESULT_NO_ERROR) {
        struct wally_write_queue_info *wq;
        if (origin->write_queue_count >= WALLY_MAX_TABLES) {
            WALLY_LOG(AL_CRITICAL, "%s %s %s write_queue is full, write_queue_count=%d",
            table->db_name, table->name, origin->name, origin->write_queue_count);
            wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
        }
        table->origins[table->origins_count] = origin;
        table->global_to_local_origin_index_map[origin->my_index] = table->origins_count;
        table->origin_write_queue_index[table->origins_count] = origin->write_queue_count;

        wq = &origin->wq_info[origin->write_queue_count];
        wq->wq_indx = origin->write_queue_count;
        wq->name = table->name;
        wq->count_in = 0;
        wq->count_out = 0;
        wq->count_fail = 0;
        wq->conn_indx = -1; // -1 means not set (0 is valid index)
        wq->last_batch = 0;
        wq->batch_size = wally_gbl_cfg.enable_batch_write ? wally_gbl_cfg.write_batch_size : 0;
        wq->total_executed_batch = 0;
        wq->total_success_batch = 0;
        wq->total_failed_batch = 0;
        wq->query_execution_time = 0;
        wq->query_creation_time = 0;
        wq->retry = 0;
        origin->write_queue_count++;
        table->origins_count++;
    }
    return res;
}


int wally_table_add_origin(struct wally *wally,
                           struct wally_table *table,
                           struct wally_origin *origin)
{
    int res;
    if (!origin) return WALLY_RESULT_NO_ERROR;

    /* Grab a write lock */
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    ZPATH_RWLOCK_WRLOCK(&(origin->lock), __FILE__, __LINE__);
    res = wally_table_add_origin_locked(wally, table, origin);
    ZPATH_RWLOCK_UNLOCK(&(origin->lock), __FILE__, __LINE__);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return res;
}

struct wally_table *
wally_table_create_named_db(struct wally *wally,
                            int is_row_writable,
                            const char *db_table_name,
                            struct argo_structure_description *argo_description,
                            wally_row_callback_f *row_callback,
                            void *row_callback_cookie,
                            int use_all_origins,
                            int multiple_index_consistency,
                            int fully_loaded,
                            wally_row_fixup_f *fixup_f)
{
    struct table_cleanup_parameter cleanup_param = {0};
    return wally_table_create_named_db_1(wally,
                                         is_row_writable,
                                         db_table_name,
                                         argo_description,
                                         row_callback,
                                         row_callback_cookie,
                                         use_all_origins,
                                         multiple_index_consistency,
                                         fully_loaded,
                                         fixup_f,
                                         cleanup_param);

}

struct wally_table *
wally_table_create_named_db_1(struct wally *wally,
                              int is_row_writable,
                              const char *db_table_name,
                              struct argo_structure_description *argo_description,
                              wally_row_callback_f *row_callback,
                              void *row_callback_cookie,
                              int use_all_origins,
                              int multiple_index_consistency,
                              int fully_loaded,
                              wally_row_fixup_f *fixup_f,
                              struct table_cleanup_parameter cleanup_param)
{
    struct wally_table *t;
    struct argo_private_field_description *key_field = NULL;
    struct argo_private_field_description *sequence_field = NULL;
    struct argo_private_field_description *deleted_field = NULL;
    char *table_name = argo_description->type;
    char *tmp_name = NULL;
    int result;
    size_t i;

    /* first check if we have a table of this name already. We'll grab
     * the write lock right away. regardless. (could get away with a
     * read lock, but geez, table creation isn't all that
     * common...  */
    ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);

    t = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (t) {
        ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        return t;
    }

    if (wally->tables_count >= WALLY_MAX_TABLES) {
        ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        return NULL;
    }

    t = (struct wally_table *) WALLY_MALLOC(sizeof(*t));
    if (t) {
        memset(t, 0, sizeof(*t));
        t->lock = ZPATH_RWLOCK_INIT;
        t->is_row_writable = is_row_writable;
        t->multiple_index_consistency = multiple_index_consistency;
        t->last_registered_column = -1;

        t->row_fixup_f = fixup_f;

        t->fully_loaded = fully_loaded;

        t->wally = wally;
        t->cleanup_param = cleanup_param;

        /* Copy name */
        tmp_name = WALLY_MALLOC(strlen(table_name) + 1);
        if (!tmp_name) goto free_err;
        strcpy(tmp_name, table_name);
        t->name = tmp_name;
        if (db_table_name) {
            t->db_name = WALLY_STRDUP(db_table_name, strlen(db_table_name));
        } else {
            t->db_name = WALLY_STRDUP(table_name, strlen(table_name));
        }

        /* Copy description */
        t->argo_description = argo_description;

        /* Set up tables so that we can add sequences/indexes. */
        t->column_indexes = zhash_table_alloc(&wally_allocator);
        if (!t->column_indexes) goto free_err;
        t->column_sequences = zhash_table_alloc(&wally_allocator);
        if (!t->column_sequences) goto free_err;

        /* Initialize our registrant list. */
        ZLIST_INIT(&(t->all_registrants));

        /* Create default registrant. */
        t->default_registrant = wally_table_create_registrant_internal(t,
                                                                       row_callback,
                                                                       row_callback_cookie,
                                                                       "default");
        if (!t->default_registrant) goto free_err;

        /* Lock argo, and read description to find sequence/key */
        argo_read_lock();
        t->row_deleted_argo_index = -1;
		t->exists = 1;
        for (i = 0; i < argo_description->description_count; i++) {
            if (argo_description->description[i]->public_description.is_key) {
                key_field = argo_description->description[i];
            }
            if (argo_description->description[i]->public_description.is_sequence) {
                sequence_field = argo_description->description[i];
            }
            if (argo_description->description[i]->public_description.is_deleted) {
                deleted_field = argo_description->description[i];
                t->row_deleted_argo_index = deleted_field->index;
            }
        }
        argo_unlock();

        if (!key_field || !sequence_field) {
            /* XXX LOG */
            goto free_err;
        }
        if (key_field->public_description.argo_field_type == argo_field_data_type_integer) {
            t->integer_key = 1;
        } else if (key_field->public_description.argo_field_type == argo_field_data_type_string) {
            t->integer_key = 0;
        } else {
            /* XXX LOG */
            goto free_err;
        }

        /* Create row hash table */
        t->row_key_argo_index = key_field->index;

        if (t->integer_key) {
            t->key_rows_integer = zhash_table_alloc(&wally_allocator);
            if (!t->key_rows_integer) {
                /* XXX LOG */
                goto free_err;
            }
        } else {
            t->key_rows_string = zhash_table_alloc(&wally_allocator);
            if (!t->key_rows_string) {
                /* XXX LOG */
                goto free_err;
            }
        }
        /* Remember sequence field. */
        t->row_sequence_argo_index = sequence_field->index;

        /* No need to do anything for origins- they have their own
         * attachment routines. */

        /* Add table to set of all tables. */
        if (zhash_table_store(wally->tables_hash,
                            t->name,
                            strlen(t->name),
                            1,
                            t)) {
            goto free_err;
        }

        wally->tables[wally->tables_count] = t;
        wally->tables_count++;

        /* Initialize request sequence hash table. */
        t->request_id = 1;
        t->requests = zhash_table_alloc(&wally_allocator);
        if (!t->requests) {
			goto free_err;
        }

        if (use_all_origins) {
            for (i = 0; i < wally->origins_count; i++) {
                result = wally_table_add_origin(wally, t, wally->origins[i]);
                if (result) {
                    goto free_err;
                }
            }
        }

        if (t->fully_loaded && wally->origins_count == 1
                            && wally->origins[0]->callout_handle
                            && wally->origins[0]->cleanup) {
            struct wally_table_cleanup_stats *z_stats = WALLY_MALLOC(sizeof(*z_stats));
            struct argo_structure_description *wally_table_cleanup_stats_description
                     = argo_register_global_structure(WALLY_TABLE_CLEANUP_STATS_HELPER);
            char name[250];
            snprintf(name, sizeof(name), "wally_table_%s_cleanup_stats", t->name);
            argo_log_register_structure(wally_stats_log,
                                        name,
                                        AL_INFO,
                                        60*1000*1000,    /* 1 minute */
                                        wally_table_cleanup_stats_description,
                                        z_stats,
                                        0,
                                        fill_wally_table_cleanup_stats,
                                        t);
        }

		if (!t->wally_table_stats_structure) {
			char wally_table_name[MAX_TABLE_NAME_LEN] = {0};

			snprintf(wally_table_name, sizeof(wally_table_name), "%s-%s_stats", t->wally->name, t->name);
			t->wt_stats = WALLY_CALLOC(sizeof(struct wally_table_stats));
			if (t->wt_stats) {
				t->wally_table_stats_structure = argo_log_register_structure(wally_stats_log,
													wally_table_name,
													AL_INFO,
													60*1000*1000,    /* 1 minute */
													wally_table_stats_description,
													t->wt_stats,
													0,
													fill_wally_table_stats,
													t);
			}
		}
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    if (t) {
        if (fully_loaded) {
            for (i = 0; i < argo_description->description_count; i++) {
                if (argo_description->description[i]->public_description.is_index) {
                    wally_table_get_index(t, argo_description->description[i]->public_description.field_name);
                }
            }
        }
    }
    return (t);

 free_err:
    result = WALLY_RESULT_ERR;
    if (t->integer_key) {
        if (t->key_rows_integer) zhash_table_free(t->key_rows_integer);
    } else {
        if (t->key_rows_string) zhash_table_free(t->key_rows_string);
    }
    if (tmp_name) WALLY_FREE(tmp_name);
    if (t->column_indexes) zhash_table_free(t->column_indexes);
    if (t->column_sequences) zhash_table_free(t->column_sequences);
    if (t->default_registrant) wally_table_destroy_registrant_internal(t->default_registrant);
    if ((t->column_indexes) || (t->column_sequences)) {
        /* Wasn't planning on implementing index destruction yet... */
        result = WALLY_RESULT_NOT_IMPLEMENTED;
    }
    WALLY_FREE(t);
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    return NULL;
}

struct wally_table *
wally_table_create(struct wally *wally,
                              int is_row_writable,
                              struct argo_structure_description *argo_description,
                              wally_row_callback_f *row_callback,
                              void *row_callback_cookie,
                              int use_all_origins,
                              int multiple_index_consistency,
                              wally_row_fixup_f *fixup_f)
{
    return wally_table_create_named_db(wally,
                                       is_row_writable,
                                       argo_description->type,
                                       argo_description,
                                       row_callback,
                                       row_callback_cookie,
                                       use_all_origins,
                                       multiple_index_consistency,
                                       0 /* Not fully_loaded */,
                                       fixup_f);
}


struct wally_table *wally_table_get(struct wally *wally, const char *table_name)
{
    struct wally_table *result;

    result = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    return result;
}

/*
 * wally_table_is_null_column_registered
 *  Checks if a table has registered for NULL column or not.
 */
int wally_table_is_null_column_registered(struct wally_table *table)
{
    int is_null = 0;

    for (int i = 0; i < table->indexed_columns_count; i++) {
        if (table->indexed_columns[i]->is_null) {
            is_null = 1;
            break;
        }
    }

    return is_null;
}

struct wally_index_column *wally_table_get_index(struct wally_table *table, const char *column_name)
{
    struct wally_index_column *res;

    /* Grab a write lock and call the internal version. */
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    res = wally_table_get_index_internal(table, column_name);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return res;
}

/*
 * wally_table_get_index_simple
 *  this function lookups the table for a column_name index match
 *  If no match is found, we return NULL (no new index is created)
 */
struct wally_index_column *wally_table_get_index_simple(struct wally_table *table, const char *column_name)
{
    struct wally_index_column *res;

    /* Grab a write lock and call the internal version. */
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    res = wally_table_get_index_simple_internal(table, column_name);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return res;
}

enum argo_field_data_type wally_column_type(struct wally_index_column *column)
{
    return (column->data_type);
}

int wally_column_is_inet(struct wally_index_column *column)
{
    return (column->table->argo_description->description[column->argo_field_index]->public_description.is_inet);
}
struct wally_paused_keys
{
    enum argo_field_data_type data_type;
    union {
        int64_t key_int;
        char *key_str;
    };
};

static int wally_resume_response_callback(void *cookie,
                                          struct wally_registrant *registrant,
                                          struct wally_table *table,
                                          int64_t sequence,
                                          int status)
{
    WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Wally resume response cb,  seq: %"PRId64"  status(count) = %d", sequence, status);
    return WALLY_RESULT_NO_ERROR;
}


static inline int wally_table_resume_table_per_registrant(struct wally_registrant *registrant,
                                                         struct wally_table *table,
                                                         struct wally_index_column *column,
                                                         int64_t paused_seq, const void *key,
                                                         const size_t key_length)
{
    int result = WALLY_RESULT_NO_ERROR;

    if (!registrant) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: NULL registrant parameter for table: %s",
                  table ? table->name : "unknown");
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: resume initiated for table: %s registrant: %s from seq: %"PRId64"",
                            table->name, registrant->debug_str ? registrant->debug_str : "<None>", paused_seq);
    result = wally_table_deregister_for_row_internal(registrant,
                                                     column, key, key_length);
    if (result) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: table: %s registrant: %s "
                            "Wally row deregister internal failed with error: %s",
                            table->name, registrant->debug_str ? registrant->debug_str : "",
                            wally_result_string(result));
        return result;
    }

    result = wally_table_register_for_row_internal(registrant,
            column,
            key,
            key_length,
            0, // request_id,
            paused_seq, // request_sequence,
            1, // request_atleast_one,
            0, // just_callback,
            0, // unique_registration,
            wally_resume_response_callback,
            NULL,
            NULL,
            NULL,
            0,
            0);
    if (result && (result != WALLY_RESULT_ASYNCHRONOUS)) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: table: %s  registrant: %s "
                            "Wally row re-register failed with error: %s",
                            table->name, registrant->debug_str ? registrant->debug_str : "",
                            wally_result_string(result));
        return result;
    }
    return WALLY_RESULT_NO_ERROR;
}

/*
 * wally_refresh_paused_keys
 *  Called up during resume operation, where we need to retrieve the skipped entries.
 *  NOTE: assume lock already taken
 */
static void wally_refresh_paused_keys(void *element, void *cookie)
{
    int result;
    void *key;
    size_t key_length;

    /* validate input parameters */
    if (!element || !cookie) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: NULL parameters.. returning !!");
        return;
    }

    struct wally_index_column *column = (struct wally_index_column *)cookie;
    struct wally_paused_keys *paused_key_obj = (struct wally_paused_keys *)element;
    struct wally_table *table = column->table;

    if (paused_key_obj->data_type == argo_field_data_type_integer) {
        key = &paused_key_obj->key_int;
        key_length = sizeof(paused_key_obj->key_int);
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Request refresh for table.col: %s.%s key:%"PRId64", len:%ld",
                                table->name, column->name, paused_key_obj->key_int, key_length);
    } else {
        key = paused_key_obj->key_str;
        if (!key) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: NULL string key.. returning !!");
            return;
        }
        key_length = strlen(paused_key_obj->key_str);
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Request refresh for table.col: %s.%s key:%s, len:%ld",
                                table->name, column->name, paused_key_obj->key_str, key_length);
    }

    struct wally_registrant *tbl_registrant, *tmp_r;
    ZLIST_FOREACH_SAFE(tbl_registrant, &(table->all_registrants), all_registrants, tmp_r) {
        result = wally_table_resume_table_per_registrant(tbl_registrant, table, column, 0, key, key_length);
        if (result)
            table_resume_error_count++;
    }

    if ((paused_key_obj->data_type != argo_field_data_type_integer) &&
        (paused_key_obj->key_str)) {
        WALLY_FREE(paused_key_obj->key_str);
    }
    WALLY_FREE(element);
}

/*
 * wally_column_refresh_interest
 *  For each indexed column of the table, refresh the interest to get latest rows.
 *  NOTE: Applicable only for non-null registered tables.
 */
void wally_column_refresh_interest(struct wally_index_column *column)
{
    struct zhash_table *paused_keys = column->paused_keys;

    if (column->is_null) {
        return;
    }
    column->paused_keys = NULL;

    if (paused_keys) {
        zhash_table_free_and_call(paused_keys, wally_refresh_paused_keys, column);
    }
    /* Re-allocating hash table for sync pause */
    column->paused_keys = zhash_table_alloc(&wally_allocator);
}

int wally_add_keys_to_paused_table(struct argo_object *object,
                                   struct wally_index_column *column)
{
   int res = WALLY_RESULT_NO_ERROR;
   struct wally_paused_keys *paused_key_obj = NULL;

    if (column->data_type == argo_field_data_type_integer) {
        int64_t key_int;
        res = argo_object_read_int_by_column_index(object, column->argo_field_index, &key_int);
        if (res) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: failed to get key for column name %s fd %d (Integer)",
                        column->name, column->argo_field_index);
            return WALLY_RESULT_ERR;
        }

        if (!zhash_table_lookup(column->paused_keys, &key_int, sizeof(key_int), NULL)) {
            paused_key_obj = WALLY_CALLOC(sizeof(struct wally_paused_keys));
            if (!paused_key_obj) {
                return WALLY_RESULT_NO_MEMORY;
            }
            paused_key_obj->data_type = column->data_type;
            paused_key_obj->key_int = key_int;
            zhash_table_store(column->paused_keys, &key_int, sizeof(key_int), 0, paused_key_obj);
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: added Int key: %"PRId64" for column: %s fd %d to paused_keys hash",
                        key_int, column->name, column->argo_field_index);
        }
    } else if (column->data_type == argo_field_data_type_string){

        char *key_str = NULL;
        res = argo_object_read_string_by_column_index(object, column->argo_field_index, &key_str);
        if (res || !key_str) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: failed to get key for column name: %s fd %d (String)",
                        column->name, column->argo_field_index);
            return WALLY_RESULT_ERR;
        }
        if (!zhash_table_lookup(column->paused_keys, key_str, strlen(key_str), NULL)) {
            paused_key_obj = WALLY_CALLOC(sizeof(struct wally_paused_keys));
            if (!paused_key_obj) {
                return WALLY_RESULT_NO_MEMORY;
            }
            paused_key_obj->data_type = column->data_type;
            paused_key_obj->key_str = WALLY_STRDUP(key_str, strlen(key_str));
            zhash_table_store(column->paused_keys, paused_key_obj->key_str,
                                strlen(key_str), 0, paused_key_obj);
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: added String key: %s for column name %s fd %d to paused_keys hash",
                        paused_key_obj->key_str, column->name, column->argo_field_index);
        }
    } else if (column->data_type == argo_field_data_type_binary) {

        size_t key_length;
        uint8_t *key;
        res = argo_object_read_binary_by_column_index(object, column->argo_field_index, &key, &key_length);
        if (res) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: failed to get key for column name: %s fd %d (Binary)",
                        column->name, column->argo_field_index);
            return WALLY_RESULT_ERR;
        }
        if (!zhash_table_lookup(column->paused_keys, key, key_length, NULL)) {
            paused_key_obj = WALLY_CALLOC(sizeof(struct wally_paused_keys));
            if (!paused_key_obj) {
                return WALLY_RESULT_NO_MEMORY;
            }
            paused_key_obj->data_type = column->data_type;
            paused_key_obj->key_str = WALLY_MALLOC(key_length);
            memcpy(paused_key_obj->key_str, key, key_length);
            zhash_table_store(column->paused_keys, key, key_length, 0, paused_key_obj);
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: added Binary key for column name %s fd %d to paused_keys hash",
                        column->name, column->argo_field_index);
        }
    } else {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: unsupported data type! Not adding to paused_keys");
        return WALLY_RESULT_ERR;
    }

    return WALLY_RESULT_NO_ERROR;
}

/*
 * wally_column_is_interest_registered_for_key
 *  Checks if we have an interest already registered for the key
 *  If so, we add it to paused keys; which will be used to re-register interest when table resumes.
 */
int wally_column_is_interest_registered_for_key(struct argo_object *object,
                                                struct wally_index_column *column)
{
    int result = 0;
    int res;
    struct wally_interest *interest = NULL;

    if (column->is_null) {
        return 0;
    }

    if (column->data_type == argo_field_data_type_integer) {
        int64_t key_int;
        res = argo_object_read_int_by_column_index(object, column->argo_field_index, &key_int);
        if (res) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: failed to get key for column name %s fd %d (Integer)",
                        column->name, column->argo_field_index);
            return 0;
        }
        interest = zhash_table_lookup(column->index_hash, &key_int, sizeof(key_int), NULL);
        if (interest != NULL) {
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: (Integer) column %s has interest for key %"PRId64"",
                        column->name, key_int);
            return 1;
        } else {
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: (Integer) column %s has no interest for key %"PRId64"",
                        column->name, key_int);
        }
    } else if (column->data_type == argo_field_data_type_string) {
        char *key_str = NULL;
        res = argo_object_read_string_by_column_index(object, column->argo_field_index, &key_str);
        if (res || !key_str) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: failed to get key for column name %s fd %d (String)",
                        column->name, column->argo_field_index);
            return 0;
        }
        interest = zhash_table_lookup(column->string_index_hash, key_str, strlen(key_str), NULL);
        if (interest != NULL) {
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: (String) column %s has interest for key %s",
                                    column->name, key_str);
            return 1;
        } else {
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: (String) column %s has no interest for key %s",
                                    column->name, key_str);
        }
    } else if (column->data_type == argo_field_data_type_binary) {
        size_t key_length;
        uint8_t *key;
        res = argo_object_read_binary_by_column_index(object, column->argo_field_index, &key, &key_length);
        if (res) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: failed to get key for column name %s fd %d (Binary)",
                        column->name, column->argo_field_index);
            return 0;
        }
        interest = zhash_table_lookup(column->string_index_hash, key, key_length, NULL);
        if (interest != NULL) {
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: (Binary) column %s has interest for key %s",
                                    column->name, key);
            return 1;
        } else {
            WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: (Binary) column %s has no interest for key %s",
                                    column->name, key);
        }
    }

    return result;
}
/*
 * wally_table_update_resume_stats.
 *  Idea is..we paused all rows with seq > paused_seq up until max_seq_paused
 * Hence, if table is unpaused and we get row in this range, this is a resumed row
 */
static void
wally_table_update_resume_stats(struct wally_origin *origin,
                                struct wally_table *table,
                                struct argo_object *object)
{
    if (!origin->pauseable)
        return;

    if (!table->is_pausable)
        return;

    /* update resume stats only in resume state */
    if (table->paused)
        return;

    int64_t sequence = 0;
    sequence = argo_object_get_sequence(object);
    if (!sequence)
        return;

    int valid_resume = 0;
    /* for null column registered, we strictly check for seq in the range. */
    if (wally_table_is_null_column_registered(table)) {
        if ((sequence > table->paused_seq) && (sequence <= table->max_seq_paused))
            valid_resume = 1;
    } else {
        if (sequence <= table->max_seq_paused)
            valid_resume = 1;
    }
    if (valid_resume) {
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: resume: received %s row from origin %s, table: %s.%s,"
                        "with sequence %"PRId64" ",
                        (sequence == table->max_seq_paused) ? "last paused" : "",
                        origin->name, origin->wally->name, table->name, sequence);
        table->rows_resumed_count++;
        /* Update resume status */
        if ((wally_table_resume_in_progress == table->pause_status)  &&
            (sequence >= table->max_seq_paused)) {
            table->pause_status = wally_table_resume_completed;
        }
    } else {
        /* any new row coming after resume completion, flips state to Not_paused */
        if (sequence > table->max_seq_paused) {
            table->pause_status = wally_table_not_paused;
        }
    }
}

static void wally_table_update_paused_keys_list(struct wally_table *table, struct argo_object *object)
{
    /* additional activity for non NULL registered tables. */
    if (!wally_table_is_null_column_registered(table)) {
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: table %s is non-NULL column registered",  table->name);

        for (int i = 0; i < table->indexed_columns_count; i++) {
            if (wally_column_is_interest_registered_for_key(object, table->indexed_columns[i])) {
                wally_add_keys_to_paused_table(object,
                                        table->indexed_columns[i]);
            }
        }
    }
}

/*
 * wally_table_is_pause_capability_enabled
 *  checks if this origin.table has been registered for sync pause capability.
 *  The check returns true only for tables registered for sync_pause
 */
static int
wally_table_is_pause_capability_enabled(struct wally_origin *origin,
                                        struct wally_table *table)
{
    /* Origin itself is non-pausable; hence not pause capable */
    if (!origin->pauseable) {
        return 0;
    }

    /* Table is not registered for sync pause functionality */
    if (!table->is_pausable) {
        return 0;
    }

    return 1;
}

/*
 * wally_should_pause_row
 *  Decides whether the row should be paused on not, based on different criteria.
 *  This routine also updates the pause statistics.
 *  Return: 1 -> this row has to be paused; ie. should NOT Process
 *          0 -> row is not paused, need to process.
 */
static int wally_should_pause_row(struct wally_origin *origin,
                                  struct wally_table *table,
                                  struct argo_object *object)
{
    /* Origin itself is non-pausable, do not pause the row */
    if (!origin->pauseable)
        return 0;
    /* Table is not registered for sync pause functionality */
    if (!table->is_pausable)
        return 0;
    /* Table is not paused, do not pause the row */
    if (!table->paused)
        return 0;

    int64_t sequence = 0;
    sequence = argo_object_get_sequence(object);

    /* no valid sequence found in the object */
    if (!sequence) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: invalid sequence in object..origin %s  Table %s.%s",
                            origin->name, origin->wally->name, table->name);
    }

    WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: skip row transfer from origin %s, %s.%s, "
                            "paused_seq %"PRId64" seq %"PRId64"",
                            origin->name, origin->wally->name, table->name,
                            table->paused_seq, sequence);

    return 1;   /* indicate row to be paused */
}
/*
 * When a row arrives, use this callback
 *
 * The row is REALEASED when this routine returnes. Hold onto the
 * object if you want it.
 */
int wally_xfer_row(void *cookie, char *table_name,
                          struct argo_object *object, int64_t request_id, int64_t *store_time, int64_t *cb_time)
{
    struct wally_origin *origin = (struct wally_origin *) cookie;
    struct wally *wally = origin->wally;
    struct wally_table *table;
    int res = WALLY_RESULT_NO_ERROR;
    int64_t row_store_time = 0, row_cb_time = 0;
    int64_t *row_store_ptr = store_time?store_time:&row_store_time;
    int64_t *row_cb_ptr = cb_time?cb_time:&row_cb_time;
    int64_t row_process_start_time = epoch_us();

    if (wally_debug & WALLY_DEBUG_ROW_DETAIL_BIT) {
        char str[4000];
        if (argo_object_dump(object, str, sizeof(str), NULL, 0)) {
            str[0] = 0;
        }
        WALLY_DEBUG_ROW_DETAIL("%s.%s(%p): %s: Xfer: row received for req=%ld: %s", origin->name, wally->name, wally, table_name, (long) request_id, str);
    }


    table = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (!table) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "wally_pause_debug: Could not find wally table: %s in xfer row", table_name);
        return WALLY_RESULT_NOT_FOUND;
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);

    if (table->row_fixup_f) (table->row_fixup_f)(object);

    if (wally_table_is_pause_capability_enabled(origin, table)) {
        /* if Row has to be paused, release lock and exit. */
        if (wally_should_pause_row(origin, table, object)) {
            table->max_seq_paused = argo_object_get_sequence(object);
            table->rows_skipped_count++;
            wally_table_update_paused_keys_list(table, object);

            ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
            return WALLY_RESULT_NO_ERROR;
        }

        /* update resume stats, if identified to be a resume case */
        wally_table_update_resume_stats(origin, table, object);
    }

    res = wally_table_process_row(table, object, table->global_to_local_origin_index_map[origin->my_index], request_id, row_store_ptr, row_cb_ptr);
    table->row_store_time += *row_store_ptr;
    table->row_callback_time += *row_cb_ptr;
    table->row_process_time += (epoch_us() - row_process_start_time);

    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    /* check for post-resume callback if all tables have been resumed */
    if (table->pause_status == wally_table_resume_completed) {
        WALLY_LOG(AL_NOTICE, "wally_pause_debug: Transfer row for table %s, triggering post-resume check",
                    table_name);
        wally_check_post_resume_callback(table->wally);
    }

    return res;
}

int wally_update_paused_seq_internal(void *cookie, char *table_name)
{
    int res = WALLY_RESULT_NO_ERROR;
    struct wally_origin *origin = (struct wally_origin *) cookie;
    struct wally *wally = origin->wally;
    struct wally_table *table;

    table = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (!table) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: Could not find wally table: %s in update paused seq", table_name);
        return WALLY_RESULT_NOT_FOUND;
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);

    /*
     * Update the paused_seq iff table is paused and max_sequence_seen has been updated
     *  and max_sequence_seen is a newer value
     * */
    if (table->paused &&
        table->max_sequence_seen &&
        (table->paused_seq != table->max_sequence_seen)) {
        WALLY_LOG(AL_NOTICE, "wally_pause_debug: Updating paused_seq: %"PRId64"  => %"PRId64" "
                             "for origin: %s table: %s.%s",
                             table->paused_seq, table->max_sequence_seen,
                             origin->name, wally->name, table->name);
        table->paused_seq = table->max_sequence_seen;
    }
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return res;
}
/*
 * wally_update_paused_seq
 *  When broker boots up, sync pause infra is initialized before all tables come up.
 *  Hence paused_seq per table has to be populated only after local db repopulation is done.
 *  This routine is called up after rows are populated from local db(i_0)
 */
int wally_update_paused_seq(void *cookie, char *table_name)
{
	int res = 0;
    res = wally_update_paused_seq_internal(cookie, table_name);
    return res;
}

static void result_callback_on_thread(struct zevent_base *base,
                                      void *void_cookie,
                                      int64_t int_cookie,
                                      void *extra_cookie1,
                                      void *extra_cookie2,
                                      void *extra_cookie3,
                                      int64_t extra_int_cookie)
{
    wally_response_callback_f *callback = extra_cookie3;
    void *callback_cookie = void_cookie;
    int64_t callback_request_id = int_cookie;
    struct wally_registrant *registrant = extra_cookie1;
    struct wally_table *table = extra_cookie2;
    int64_t row_count = extra_int_cookie;

    int res;

    //WALLY_LOG(AL_NOTICE, "on_thread call!");

    res = (callback)(callback_cookie, registrant, table, callback_request_id, row_count);
    if (res) {
        WALLY_LOG(AL_ERROR, "result_callback_on_thread: Result is %s, %s:%s", wally_result_string(res), table->wally->name, table->name);
    }
}

void wally_interest_result_callback_cb(struct wally_interest *interest, struct wally_registrant_callback *cb)
{
    int res;
    struct wally *wally = NULL;
    int64_t diff = 0;
    struct wally_table *table = NULL;

    if ((cb->request_atleast_one && interest->request_complete_atleast_one) ||
        (!cb->request_atleast_one && interest->request_complete)) {
        if (!cb->is_feeding) {
            if (cb->callback) {
                /* If we have a zevent_base associated with this
                 * callback, then the callback is to be performed
                 * without concern for response status (it will never
                 * 'block'), and furthermore is to be done on the
                 * appropriate thread */
                if (cb->callback_to_zevent_base) {
                    zevent_base_big_call(cb->callback_to_zevent_base, result_callback_on_thread, cb->callback_cookie, cb->callback_request_id, cb->registrant, interest->column->table, cb->callback, cb->row_count);
                    res = WALLY_RESULT_NO_ERROR;
                } else {
                    if (cb->request_received_us) {
                        cb->registrant->turnaround_time_us = (epoch_us() - cb->request_received_us);
                    }
                    if (interest->column) {
                       table = interest->column->table;
                    }
                    res = (cb->callback)(cb->callback_cookie, cb->registrant, table, cb->callback_request_id, cb->row_count);
                }
                if (res == WALLY_RESULT_WOULD_BLOCK) {
                    ZTAILQ_INSERT_TAIL(&(cb->registrant->feed_registrant_callbacks), cb, feed_list);
                    cb->is_feeding = 1;
                } else {
                    cb->callback = NULL;
                    cb->row_count_complete = cb->row_count;
                    cb->request_complete_us = epoch_us();
                    if (cb->request_received_us) {
                        if (interest->column && interest->column->table) {
                             wally = interest->column->table->wally;
                             if (wally && cb->registrant != interest->column->table->default_registrant ) {
                                 diff = cb->request_complete_us - cb->request_received_us;
                                 wally_updated_registrant_response_status(wally,diff);
                            }
                        }
                    }
                }
            } else {
                /* For those that don't have callbacks, they can be complete as well. */
                if (!cb->request_complete_us) {
                    cb->request_complete_us = epoch_us();
                    cb->row_count_complete = cb->row_count;
                }
            }
        }
    }
}


/*
 * Make all the interest callbacks...
 *
 * We only perform result callbacks if the callback is
 * completed/ready. (i.e. not on the feed queue)
 */
void wally_interest_result_callbacks(struct wally_interest *interest)
{
    struct wally_registrant_callback *cb;

    ZLIST_FOREACH(cb, &(interest->registrant_callbacks), interest_list) {
        wally_interest_result_callback_cb(interest, cb);
    }
}

/*
 * When a request has been satisfied, use this callback.
 *
 * This routine will promote a request from one database to another.
 */
int wally_xfer_response(void *cookie, int64_t sequence, int64_t row_count, int64_t table_exists, char *table_name)
{
    struct wally_origin *origin = (struct wally_origin *) cookie;
    struct wally *wally = origin->wally;
    struct wally_interest *interest;
	struct wally_table *table;
    int res;
    int64_t diff = 0;



	table = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (!table) {
        WALLY_LOG(AL_ERROR, "%s: %s:: Xfer table lookup failed ", wally->name, table_name);
        return WALLY_RESULT_NOT_FOUND;
    }

    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);

    interest = zhash_table_lookup(table->requests, &sequence, sizeof(sequence), NULL);
    if (!interest) {
		WALLY_DEBUG_REGISTRATION("%s: %s: Xfer interest does not exists for req_id=%"PRId64", row_count = %"PRId64,
				 wally->name, table_name, sequence, row_count);
        /* XXX LOG */
        /* This can happen when an interest is deleted just before response comes back. */
        //fprintf(stderr, "Error: Did not find outstanding requests sequence = %ld\n", (long) sequence);
    } else {
		/*
		 * This case to be executed only for leaf wally, We have received a response from Origin wally
		 * with table_exists set to 0.  This particular table is not present in RDS for Origin wally.
		 * We need to take care of updating whether table exists or not in leaf wally.
		 * NOTE: always DB schema should be upgraded before itasca argo support added.
		 */
		if (wally_is_wallyd(wally) && !table_exists &&
				interest->column->table->exists &&
				interest->column->table->origins_count > 1) {
			wally_xfer_update_table_exists(cookie, interest->column->table->name, table_exists, false);
		}
        /* Now that we have a response, mark this entry as such. The
         * state machine farther down this routine will perform
         * promotion as a result of this indication */
        if (interest->origin_db_state == registered_waiting_for_response) {
            interest_assign_state(interest, registered);
            interest->origin_db_waiting_for_response_complete_us = epoch_us();
            interest->waiting_response_time_hist = 0;
            if (interest->ext_reg_cb_count > 0 && interest->origin_db_waiting_for_response_start_us > 0) {
                diff = interest->origin_db_waiting_for_response_complete_us - interest->origin_db_waiting_for_response_start_us;
                if (diff > 0) {
                    wally_updated_registrant_db_wait_status(wally,diff);
                }
            }
			/* When interest request is sent first remote origin will send all rows and response indicating
			 * all rows are sent. With first time interest being registered we will not add lookup table entry
			 * for all rows only at the end when response is sent will update the lookup table only iff
			 * 1) multiple index consistency feature flag is enabled
			 * 2) if table is not fully loaded
			 * 3) if interest is pierce_fully_loaded
             * 4) If Interest max_sequence_seen is > 0(there can be a case where invalid regi)
			 * 5) if interest if for key we need not create entry in lookup table, as key is unique.
			 */
			if (wally_multiple_index_consistency_support) {
			    if ((interest->origin_db_index > 0) &&
			        (!(interest->column->table->fully_loaded) ||
				    (interest->pierce_fully_loaded)) &&
					(interest->column->is_null || (interest->column->table->row_key_argo_index != interest->column->argo_field_index)) &&
                    (interest->max_sequence_seen > 0)) {
				     res = wally_origin_enqueue_write_lookup_table(interest, 0);

				    if(res) {
					    WALLY_LOG(AL_ERROR, "%s:%s Xfer Resp: Enqueue write lookup table failed",
							 interest->column->table->wally->name,
							 interest->column->table->name);
					    return res;
                    }
				    WALLY_DEBUG_MIC("%s: %s: Xfer Resp: Lookup table update column",
						    interest->column->table->wally->name,
						    interest->column->table->name);
                }
			}
        } else {
            WALLY_LOG(AL_ERROR, "%s: %s: %s: Xfer: Not registered? Unexpected state: %s",
                      interest->column->table->wally->name,
                      interest->column->table->name,
                      interest->column->name,
                      wally_interest_state_str(interest->origin_db_state));
        }
        WALLY_DEBUG_REGISTRATION("%s: %s: %s: Xfer: origin response received from %s, ID = %ld, row_count = %ld, table_exists = %ld",
                                 interest->column->table->wally->name,
                                 interest->column->table->name,
                                 interest->column->name,
                                 origin->name,
                                 (long) sequence,
                                 (long) row_count,
								 (long) table_exists);
        //fprintf(stderr, "Processing interest from xfer queue: %p\n", interest);
        zhash_table_remove(table->requests, &sequence, sizeof(sequence), NULL);

        if (interest->origin_db_state != deleted) {
            /* We only send row result if either we received rows from
             * the current database, or if there is no further
             * database to query. (The extra DB has to be ready,
             * too...) */

            /*
             * We need to update our completeness state.
             *
             * If this is the response from the last origin, the
             * request is completed for all cases.
             *
             * If this is NOT the response from the last origin, then:
             *
             * For request_complete: We are complete only if the last
             * origin is not ready.
             *
             * For request_complete_atleast_one: We are complete only
             * if we have received rows.
             */
            if (interest->origin_db_index == (interest->column->table->origins_count - 1)) {
                interest->request_complete = 1;
                interest->request_complete_atleast_one = 1;
            } else if (!interest->column->is_null){
                struct wally_origin *last_origin = interest->column->table->origins[interest->column->table->origins_count - 1];
                if (ZLIST_FIRST(&(interest->rows))) {
                    interest->request_complete_atleast_one = 1;
                    interest->request_complete = 1;
                } else {
                    if (last_origin) {
                        int status = (last_origin->get_status)(last_origin->callout_handle);
                        if (status != wally_origin_status_ready) {
                            interest->request_complete = 1;
                        }
                    }
                }
            }

            WALLY_DEBUG_RESULT("%s: %s: %s: Xfer: origin response received: sending result, origin = %s sequence = %ld row_count = %ld, complete = %u, complete_atleast_one = %u",
                               interest->column->table->wally->name,
                               interest->column->table->name,
                               interest->column->name,
                               origin->name,
                               (long) sequence,
                               (long) row_count,
                               interest->request_complete,
                               interest->request_complete_atleast_one);

            wally_interest_result_callbacks(interest);

            /* Now, run registration state machine... Will change registration/promote as necessary. */
            res = wally_interest_state_machine(interest);
            if (res != WALLY_RESULT_NO_ERROR) {
                /* XXX LOG, and figure out what to do. */
            }
        } else {
            /* Interests are never deleted in current software. */
            WALLY_LOG(AL_CRITICAL, "Implement Me");
            zhash_table_remove(table->requests, &sequence, sizeof(sequence), NULL);
            /* ? Why free this memory here? This seems the wrong place
             * for it- otherwise it should probably be reference
             * counted. */
            //WALLY_FREE(interest);
        }
    }

    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return WALLY_RESULT_NO_ERROR;
}

/*
 * When postgres read is performed and table is does not exists invoke
 * this to update table->exists in wally_table.
 * This is also invoked when as part of server_result_callback to update table->exists
 * in leaf wally as well
 *
 */
int wally_xfer_update_table_exists(void *cookie, const char *table_name, uint8_t table_exists, bool locked)
{
	struct wally_origin *origin = (struct wally_origin *)(cookie);
	struct wally *wally = origin->wally;
	struct wally_table *table = NULL;

    table = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (!table) {
        WALLY_LOG(AL_ERROR, "wally_xfer_update_table_exists: Update table exits for wally %s table %s, "
                            "but table doesn't exist!",  wally->name, table_name);
        return WALLY_RESULT_NOT_FOUND;
	}

	if (locked) {
		ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
	}
	table->exists = table_exists;

	//Deregister for wally table stats for non-existent tables
	if (table->wally_table_stats_structure) {
		argo_log_deregister_structure(table->wally_table_stats_structure, 1);
		table->wally_table_stats_structure = NULL;
                if(table->wt_stats != NULL) {
                   WALLY_FREE(table->wt_stats);
                   table->wt_stats = NULL;
                }
	}

	if (locked) {
		ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
	}
	return WALLY_RESULT_NO_ERROR;
}

/*
 * If any of our calls returned a block indication (any of the
 * registration/deregistrations), then this call MUST eventually be
 * used to resume that registration/deregistration. The exception is a
 * disconnect event, which simply clears out all registration
 * state, blocking/queued or not.
 */
int wally_xfer_resume_xmit(void *cookie)
{
    struct wally_origin *origin = (struct wally_origin *) cookie;
    struct wally_interest *interest;

    /* For each outstanding registration, re-register it, but stop if
     * we just ended up back on this queue! */
    do {
        pthread_mutex_lock(&(origin->interest_register_list_lock));
        interest = ZTAILQ_FIRST(&(origin->registering_interests_list));
        pthread_mutex_unlock(&(origin->interest_register_list_lock));
        if ( !interest)
            break;
        ZPATH_RWLOCK_WRLOCK(&(interest->column->table->lock), __FILE__, __LINE__);
        /* Verify state is correct- this interest is on the list, so
         * it basically has to be registration_xmit_pending. Other
         * states could end up in an infinite loop of failures,
         * here.  */
        if ( interest->still_in_register_pending_list == 0 )
            goto next_register_pending;
        if (interest->origin_db_state != registration_xmit_pending) {
            /* Abort via watchdog */
            WALLY_LOG(AL_CRITICAL, "%s/%s/%s/%s: Origin %s, Invalid state: %s",
                      interest->column->table->wally->name,
                      interest->column->table->name,
                      interest->column->name,
                      interest->description,
                      interest->column->table->origins[interest->origin_db_index]->name,
                      wally_origin_state_strings[interest->origin_db_state]);
            wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
        }

        send_register_interest_internal(interest);

        // If the interest ended up back on the registration pending
        // list (this queue) then we want to stop processing this
        // queue.
        if (interest->origin_db_state == registration_xmit_pending) {
            ZPATH_RWLOCK_UNLOCK(&(interest->column->table->lock), __FILE__, __LINE__);
            break;
        }
        next_register_pending:
        ZPATH_RWLOCK_UNLOCK(&(interest->column->table->lock), __FILE__, __LINE__);
    } while (interest);

    /* For each outstanding deregistration, re-register it. */
    do {
        pthread_mutex_lock(&(origin->interest_deregister_list_lock));
        interest = ZTAILQ_FIRST(&(origin->deregistering_interests_list));
        pthread_mutex_unlock(&(origin->interest_deregister_list_lock));
        if ( !interest)
            break;
        /* Verify state is correct- this interest is on the list, so
         * it basically has to be deregistration_xmit_pending. Other
         * states could end up in an infinite loop of failures,
         * here.  */
        ZPATH_RWLOCK_WRLOCK(&(interest->column->table->lock), __FILE__, __LINE__);
        if ( interest->still_in_deregister_pending_list == 0 )
            goto next_deregister_pending;
        if (interest->origin_db_state != deregistration_xmit_pending) {
            /* Abort via watchdog */
            WALLY_LOG(AL_CRITICAL, "%s/%s/%s/%s: Origin %s, Invalid state: %s",
                      interest->column->table->wally->name,
                      interest->column->table->name,
                      interest->column->name,
                      interest->description,
                      interest->column->table->origins[interest->origin_db_index]->name,
                      wally_origin_state_strings[interest->origin_db_state]);
            wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
        }

        send_deregister_interest_internal(interest);

        // If the interest ended up back on the deregistration pending
        // list (this queue) then we want to stop processing this
        // queue.
        if (interest->origin_db_state == deregistration_xmit_pending) {
            ZPATH_RWLOCK_UNLOCK(&(interest->column->table->lock), __FILE__, __LINE__);
            break;
        }
        next_deregister_pending:
        ZPATH_RWLOCK_UNLOCK(&(interest->column->table->lock), __FILE__, __LINE__);
    } while (interest);


    return WALLY_RESULT_NO_ERROR;
}

/*
 * For asynchronouse indication of major database
 * events. i.e. connect/disconnect
 */
int wally_xfer_status_update(void *cookie, enum wally_origin_status status)
{
    struct wally_origin *origin = (struct wally_origin *) cookie;

    /* It's possible for cookie to be NULL very early during
     * initialization- particularly if origin database initialization
     * is synchronous, becuase we create the origin database before
     * really attaching it to wally. */

    if (!origin) return WALLY_RESULT_NO_ERROR;

    ZPATH_RWLOCK_WRLOCK(&(origin->lock), __FILE__, __LINE__);
    if (origin->last_status == status) {
		ZPATH_RWLOCK_UNLOCK(&(origin->lock), __FILE__, __LINE__);
		return WALLY_RESULT_NO_ERROR;
	}

    origin->last_status = status;

    /* Incrementing incarnation indirectly invalidates every
     * interest registered on that origin database. */
    origin->incarnation++;

    WALLY_DEBUG_REGISTRATION("Xfer: Origin db %s, status changed to %d, incarnation now %d", origin->name, status, origin->incarnation);

    ZPATH_RWLOCK_UNLOCK(&(origin->lock), __FILE__, __LINE__);

    return WALLY_RESULT_NO_ERROR;
}


/*
 * Write the given row to the specified origin database, if that
 * database allows writes. The write queue interface is protected by
 * its own lock in order to keep from grabbing the global wally lock.
 *
 * This routine will queue up the row for writing if the write attempt
 * fails.
 *
 * This routine holds a refcount on the row (argo object).
 */
int wally_origin_enqueue_write_row(struct wally_origin *origin, int write_queue_index, struct argo_object *row, int insert_head)
{
    struct wally_local_db_write_q *q;
    if (origin->is_writable) {
        q = (struct wally_local_db_write_q *)WALLY_MALLOC(sizeof(*q));
		if (!q) return WALLY_RESULT_NO_MEMORY;

		q->wrq_obj = (struct wally_local_db_write_queue_object*)WALLY_MALLOC(sizeof(struct wally_local_db_write_queue_object));
		if (!(q->wrq_obj)) {
			WALLY_FREE(q);
			return WALLY_RESULT_NO_MEMORY;
		}

        q->wrq_obj->data.row_to_write = row;
        q->wrq_obj->db_op = db_oper_rowargo_object;
        argo_object_hold(row);
        pthread_mutex_lock(&(origin->write_queue_lock));
        if (insert_head) {
            ZTAILQ_INSERT_HEAD(&(origin->write_queue[write_queue_index]), q, write_queue);
        } else {
            ZTAILQ_INSERT_TAIL(&(origin->write_queue[write_queue_index]), q, write_queue);
        }
        origin->wq_info[write_queue_index].count_in++;
        pthread_mutex_unlock(&(origin->write_queue_lock));
    } else {
        WALLY_LOG(AL_WARNING, "%s: %s: write row, but origin not writable", origin->wally->name, origin->name);
    }

    return WALLY_RESULT_NO_ERROR;
}


/*
 * Enqueue a give write object to the specified origin database, if that
 * database allows writes. The write queue interface is protected by
 * its own lock in order to keep from grabbing the global wally lock.
 *
 * This routine will queue up the object for writing if the write attempt
 * fails.
 *
 */
int wally_origin_enqueue_write_object(struct wally_origin *origin,
									 int write_queue_index,
									 struct wally_local_db_write_queue_object *write_object,
									 int insert_head)
{
	struct wally_local_db_write_q *q;
	int res = WALLY_RESULT_NO_ERROR;


	q = (struct wally_local_db_write_q *)WALLY_MALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->wrq_obj = write_object;

	if (write_object->db_op == db_oper_rowargo_object) {
		argo_object_hold(write_object->data.row_to_write);
	}
	pthread_mutex_lock(&(origin->write_queue_lock));
	if (insert_head) {
		ZTAILQ_INSERT_HEAD(&(origin->write_queue[write_queue_index]), q, write_queue);
	} else {
		ZTAILQ_INSERT_TAIL(&(origin->write_queue[write_queue_index]), q, write_queue);
	}
	origin->wq_info[write_queue_index].count_in++;
	pthread_mutex_unlock(&(origin->write_queue_lock));

	return res;
}

int wally_origin_enqueue_write_recovery_update(struct wally_table *table, int64_t recovery_sequence,
											int insert_head)
{
	int write_queue_index = -1;
	int res = WALLY_RESULT_NO_ERROR;
    struct wally *wally = table->wally;
    int origin_db_index = WALLY_ORIGIN_GB_INDEX;
	struct wally_origin *origin = wally->origins[origin_db_index];

	struct wally_local_db_write_queue_object *write_object = NULL;

	origin = wally->origins[origin_db_index];
	write_queue_index = table->origin_write_queue_index[origin_db_index];

	if (origin->is_writable) {
		write_object = (struct wally_local_db_write_queue_object *)WALLY_CALLOC(sizeof(struct wally_local_db_write_queue_object));
		if (!write_object) return WALLY_RESULT_NO_MEMORY;

		write_object->db_op = db_oper_recovery_update;
		snprintf(write_object->data.recovery_update.table_name, MAX_TABLE_NAME_LEN,
				"%s", table->name);
		write_object->data.recovery_update.recovery_sequence = recovery_sequence;

		WALLY_DEBUG_RECOVERY("%s:%s Equeueing queue object table = %s, "
				"sequence = %"PRId64"  queue_indx = %d",
				origin->wally->name, origin->name,
				 write_object->data.recovery_update.table_name,
				 write_object->data.recovery_update.recovery_sequence,
				 write_queue_index);

		res = wally_origin_enqueue_write_object(origin, write_queue_index, write_object, insert_head);
		if (res) {
			WALLY_LOG(AL_ERROR, "%s:%s Enqueue failed recovery  entry for table=%s",
					origin->wally->name, origin->name, write_object->data.recovery_update.table_name);
			wp_release_write_object(write_object, 0);
		}
	} else {
		WALLY_LOG(AL_WARNING, "%s: %s: write recovery table for %s, but origin not writable",
				wally->name, origin->name,
				table->name);
	}
	return res;
}


int wally_origin_enqueue_write_lookup_table(struct wally_interest *interest,
											int insert_head)
{
	struct wally_origin *origin = NULL;
	int write_queue_index = -1;
	int res = WALLY_RESULT_NO_ERROR;

	struct wally_local_db_write_queue_object *write_object = NULL;

	if (!interest) {
		WALLY_LOG(AL_ERROR, "Bad Argument");
		return WALLY_RESULT_ERR;
	}

	origin = interest->column->table->origins[interest->origin_db_index - 1];
	write_queue_index = interest->column->table->origin_write_queue_index[interest->origin_db_index - 1];

	if (origin->is_writable) {
		write_object = (struct wally_local_db_write_queue_object *)WALLY_CALLOC(sizeof(struct wally_local_db_write_queue_object));
		if (!write_object) return WALLY_RESULT_NO_MEMORY;

		write_object->db_op = db_oper_lookuptable_update;
		snprintf(write_object->data.lookup_table_write.table_name, MAX_TABLE_NAME_LEN,
				"%s", interest->column->table->name);
		write_object->data.lookup_table_write.max_sequence_seen = interest->max_sequence_seen;
		if (interest->column->is_null) {
			WALLY_DEBUG_MIC("%s:%s NULL column interest for table %s",
							origin->wally->name,
							origin->name,
							interest->column->table->name);
			write_object->data.lookup_table_write.value = (char *)WALLY_CALLOC(strlen("null") + 1);
			if (!(write_object->data.lookup_table_write.value)) return WALLY_RESULT_NO_MEMORY;

			snprintf(write_object->data.lookup_table_write.value, strlen("null")+1, "%s", "null");
			write_object->data.lookup_table_write.column_index = WALLY_MAX_COLUMNS;
		} else {
			write_object->data.lookup_table_write.column_index = interest->column->argo_field_index;
			if (interest->column->data_type == argo_field_data_type_integer) {
				WALLY_DEBUG_MIC("%s:%s Interest for lookup table=%s key=%"PRIu64", "
								"key_length = %zu, value_len = %d",
								 origin->wally->name, origin->name,
								 interest->column->table->name,
								 interest->key, // uint64_t key
								 interest->key_length,
								 UINT64_MAX_DIGITS + 1);

				write_object->data.lookup_table_write.value = (char *)WALLY_CALLOC(UINT64_MAX_DIGITS + 1);
				if (!(write_object->data.lookup_table_write.value)) return WALLY_RESULT_NO_MEMORY;
                // uint64_t key
				snprintf(write_object->data.lookup_table_write.value, UINT64_MAX_DIGITS+ 1, "%"PRIu64"", interest->key);
		} else {
			write_object->data.lookup_table_write.value = (char *)WALLY_CALLOC(interest->key_length + 1);
			if (!(write_object->data.lookup_table_write.value)) return WALLY_RESULT_NO_MEMORY;

			snprintf(write_object->data.lookup_table_write.value, interest->key_length + 1, "%s", interest->key_data);
			}
		}

		WALLY_DEBUG_MIC("%s:%s Equeueing queue object table = %s, "
				"sequence = %"PRId64", column_index = %d, value=%s queue_indx = %d",
				origin->wally->name, origin->name,
				 write_object->data.lookup_table_write.table_name,
				 write_object->data.lookup_table_write.max_sequence_seen, // int64_t max_sequence_seen;
				 write_object->data.lookup_table_write.column_index,
				 write_object->data.lookup_table_write.value,
				 write_queue_index);

		res = wally_origin_enqueue_write_object(origin, write_queue_index, write_object, insert_head);
		if (res) {
			WALLY_LOG(AL_ERROR, "%s:%s Enqueue failed lookuptable entry for table=%s",
					origin->wally->name, origin->name, write_object->data.lookup_table_write.table_name);
			wp_release_write_object(write_object, 0);
		}
	} else {
		WALLY_LOG(AL_WARNING, "%s: %s: write lookup table for %s, but origin not writable",
				origin->wally->name, origin->name,
				interest->column->table->name);
	}
	return res;
}

static int wally_origin_dequeue_write_objects_specific_locked(struct wally_origin *origin, int queue_index ,struct wally_local_db_write_queue_object *write_objects[])
{
    struct wally_local_db_write_q *q;
    struct wally_local_db_write_queue_object *write_object = NULL;
    int desc_index = -1;
    int column_count = -1;
    struct argo_structure_description *argo_desc = NULL;

    if (!origin) {
        WALLY_LOG(AL_CRITICAL, "Attempt to dequeue from null origin");
        return 0;
    }
    if (queue_index >= origin->write_queue_count) {
        WALLY_LOG(AL_CRITICAL, "Attempt to access non-existing write queue from origin %s, index=%d, queue_count=%d", origin->name, queue_index, origin->write_queue_count);
        return 0;
    }
    int row_count = 0;
    int i = 0;
    int curr_batch_size = origin->wq_info[queue_index].batch_size;

    for( i = 0 ; (i < curr_batch_size  && i < WALLY_MAX_WRITE_BATCH_SIZE) ;  i++) {
        bool should_break_loop = false;
        q = ZTAILQ_FIRST(&(origin->write_queue[queue_index]));
        if (q) {
            write_object = q->wrq_obj;
			if (write_object) {
				if ((write_object->db_op == db_oper_lookuptable_update) ||
                        (write_object->db_op == db_oper_recovery_update)) {
					if (row_count == 0) {
						write_objects[i] = q->wrq_obj;
						ZTAILQ_REMOVE(&(origin->write_queue[queue_index]), q, write_queue);
						origin->wq_info[queue_index].count_out++;
						WALLY_FREE(q);
						q = NULL;
						origin->write_queue_last_index = queue_index;
						row_count ++;
					}
                    should_break_loop = true;

				} else {
					argo_read_lock();
					argo_desc = argo_global.all_descriptions[write_object->data.row_to_write->base_structure_index];
					if (column_count == -1) {
						column_count = argo_desc->static_description_count + write_object->data.row_to_write->excess_description_count;
					}
					if (desc_index == -1) {
						desc_index = write_object->data.row_to_write->base_structure_index;
					}
					if (desc_index != write_object->data.row_to_write->base_structure_index) {
						should_break_loop = true;
					}
					if (column_count != argo_desc->static_description_count + write_object->data.row_to_write->excess_description_count) {
						should_break_loop = true;
					}
                    argo_unlock();

                    if (!should_break_loop) {
					    write_objects[i] = q->wrq_obj;
					    ZTAILQ_REMOVE(&(origin->write_queue[queue_index]), q, write_queue);
					    origin->wq_info[queue_index].count_out++;
					    WALLY_FREE(q);
					    q = NULL;
					    origin->write_queue_last_index = queue_index;
					    row_count ++;
                    }
				}
#if 1
           {
                static int64_t last_us = 0;
                int64_t us;
                static int64_t count = 0;
                double s_diff;
                static int next_set = 1;
                count++;
                if(origin->write_queue_start_time == 0) {
                   origin->write_queue_start_time = epoch_us();
                }
                origin->write_queue_last_update_time = epoch_us();

                if (last_us == 0) {
                    last_us = epoch_us_accuracy_us();
                 }
                if (((count / 10000) == next_set) ) {
                    us = epoch_us_accuracy_us();
                    s_diff = (double)(us - last_us);
                    s_diff /= 1000000.0;
                     WALLY_LOG(AL_NOTICE, "Wrote %ld objects, rate = %8.3f/s current batch size is %d",
                            (long)count ,((double) 10000.0 / s_diff), curr_batch_size);
                    last_us = us;
                    next_set ++;
                }
           }
#endif
		   } else {
			   should_break_loop = true;
		   }
		} else {
			should_break_loop = true;
		}

        if (should_break_loop) {
            break;
        }
    }
    return row_count ;
}

static struct wally_local_db_write_queue_object *wally_origin_dequeue_write_object_specific_locked(struct wally_origin *origin, int queue_index)
{
    struct wally_local_db_write_q *q;
    struct wally_local_db_write_queue_object *queue_object = NULL;

    if (!origin) {
        WALLY_LOG(AL_CRITICAL, "ATTEMPT TO DEQUEUE FROM NULL ORIGIN");
        return NULL;
    }
    if (queue_index >= origin->write_queue_count) {
        WALLY_LOG(AL_CRITICAL, "ATTEMPT TO ACCESS NON-EXISTING WRITE QUEUE FROM ORIGIN %s, index=%d, queue_count=%d", origin->name, queue_index, origin->write_queue_count);
        return NULL;
    }
    q = ZTAILQ_FIRST(&(origin->write_queue[queue_index]));
    if (q) {
        queue_object = q->wrq_obj;
        ZTAILQ_REMOVE(&(origin->write_queue[queue_index]), q, write_queue);
        origin->wq_info[queue_index].count_out++;
        WALLY_FREE(q);
        origin->write_queue_last_index = queue_index;
#if 1
        {
            static int64_t last_us = 0;
            int64_t us;
            static int64_t count = 0;
            double s_diff;
            count++;
            if (last_us == 0) {
                last_us = epoch_us_accuracy_us();
            }
            if(origin->write_queue_start_time == 0) {
              origin->write_queue_start_time = epoch_us();
            }
            origin->write_queue_last_update_time = epoch_us();
            if ((count % 10000) == 0) {
                us = epoch_us_accuracy_us();
                s_diff = us - last_us;
                s_diff /= 1000000.0;
                WALLY_LOG(AL_NOTICE, "Wrote %ld objects, rate = %8.3f/s",
                            (long)count,
                            (double) 10000.0 / s_diff);
                last_us = us;
            }
        }
#endif
    }
    return queue_object;
}



int wally_origin_dequeue_write_objects_specific(struct wally_origin *origin, int queue_index , struct wally_local_db_write_queue_object *write_object[] )
{
    int queue_object_count = 0;
    pthread_mutex_lock(&(origin->write_queue_lock));
    queue_object_count =  wally_origin_dequeue_write_objects_specific_locked(origin, queue_index, write_object);
    pthread_mutex_unlock(&(origin->write_queue_lock));
    return queue_object_count;
}




struct wally_local_db_write_queue_object *wally_origin_dequeue_write_object_specific(struct wally_origin *origin, int queue_index)
{
    struct wally_local_db_write_queue_object *write_object = NULL;
    pthread_mutex_lock(&(origin->write_queue_lock));
    write_object = wally_origin_dequeue_write_object_specific_locked(origin, queue_index);
    pthread_mutex_unlock(&(origin->write_queue_lock));
	if (write_object) {
		if (write_object->db_op == db_oper_lookuptable_update) {
			WALLY_DEBUG_MIC("Dequeue lookup table successfull %s", write_object->data.lookup_table_write.table_name);
		} else if (write_object->db_op == db_oper_recovery_update) {
            WALLY_DEBUG_RECOVERY("wally_origin_dequeue_write_object_specific dequeue of recovery update object with "
                    "table_name = %s recovery_sequence = %"PRId64" ", write_object->data.recovery_update.table_name,
                        write_object->data.recovery_update.recovery_sequence);
        }
	}
    return write_object;
}

/*
 * The dequeue is responsible for dropping the refcount on the row,
 * as ownership is passed by this routine.
 */
struct wally_local_db_write_queue_object *wally_origin_dequeue_write_row(struct wally_origin *origin, int *queue_index)
{
    struct wally_local_db_write_queue_object *row = NULL;
    if (origin) {
        pthread_mutex_lock(&(origin->write_queue_lock));
        for (int i = 1; i <= origin->write_queue_count; i++) {
            int ix = (origin->write_queue_last_index + i) % origin->write_queue_count;
            row = wally_origin_dequeue_write_object_specific_locked(origin, ix);
            if (row) {
                *queue_index = ix;
                break;
            }
        }
        pthread_mutex_unlock(&(origin->write_queue_lock));
    }
    return row;
}

/*
 * This routine handles all the behind-the scenes registrations and
 * deregistrations with backend origin databases for an interest. It
 * also does some small amount of cleanup when nobody is interested in
 * it any more, after all deregistrations are complete.
 *
 * This routine can be run idempotently on an interest in any state,
 * with origin databases in any state.
 *
 * It should be noted that an interest NEVER drops its origin DB
 * index, as that information has already been retrieved, and exists
 * in wally. The origin DB is only promoted, never demoted.
 *
 * The calls we make actually change the interest state.
 */
int wally_interest_state_machine(struct wally_interest *interest)
{
    int have_registrants;
    int do_delete = 0;
    int res = WALLY_RESULT_NO_ERROR;

    /* If we are fully_loaded, then leave registered interests alone,
     * unless they are the NULL column itself. */
    if (interest->column->table->fully_loaded &&
        !interest->column->is_null &&
        !interest->pierce_fully_loaded) return res;

    /* Short circuit passive interests- they are never registered in any way. */
    if (interest->origin_db_state == passive) return res;

    /* Check how long this interest has been outstanding... */
    if (interest->origin_db_state == registered_waiting_for_response) {
        int64_t now = epoch_us();
        wally_interest_update_stats_pending_window(now, interest);
        /* If we have been waiting too long without any data, log something! */
        if (!(interest->logged)) {
            if (((now - interest->last_row_us) > (60*1000*1000)) &&
                ((now - interest->origin_db_waiting_for_response_start_us) > (60*1000*1000))) {
                //fprintf(stderr, "%s:%s:%d: Origin not ready\n", __FILE__, __FUNCTION__, __LINE__);
                WALLY_LOG(AL_WARNING, "%s:%s:%s: %s: Origin = %s: No activity waiting on response for at least 60s",
                          interest->column->table->wally->name,
                          interest->column->table->name,
                          interest->column->name,
                          interest->description,
                          interest->column->table->origins[interest->origin_db_index]->name);
                interest->column->table->interest_pending_long++;
                interest->logged = 1;
            }
        }
    }

    ZPATH_RWLOCK_RDLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);
    if (interest->origin_incarnation != interest->column->table->origins[interest->origin_db_index]->incarnation) {
        /* We have an incarnation mismatch- our current registration
         * state with the current origin DB is completely invalid.  We
         * should not have promotion set, but we clear it here just in
         * case. */
        interest->origin_db_promote = 0;
        if (interest->origin_db_request_id) {
            zhash_table_remove(interest->column->table->requests, &(interest->origin_db_request_id), sizeof(interest->origin_db_request_id), NULL);
            interest->origin_db_request_id = 0;
        }
        if (interest->origin_db_state == deregistration_xmit_pending) {
            pthread_mutex_lock(&(interest->column->table->origins[interest->origin_db_index]->interest_deregister_list_lock));
            ZTAILQ_REMOVE(&(interest->column->table->origins[interest->origin_db_index]->deregistering_interests_list),
                    interest, origin_db_processing);
            pthread_mutex_unlock(&(interest->column->table->origins[interest->origin_db_index]->interest_deregister_list_lock));
            interest->still_in_deregister_pending_list = 0;
            interest->db_block_queue_insert_us = 0;
        }
        if (interest->origin_db_state == registration_xmit_pending) {
            pthread_mutex_lock(&(interest->column->table->origins[interest->origin_db_index]->interest_register_list_lock));
            ZTAILQ_REMOVE(&(interest->column->table->origins[interest->origin_db_index]->registering_interests_list),
                    interest, origin_db_processing);
            pthread_mutex_unlock(&(interest->column->table->origins[interest->origin_db_index]->interest_register_list_lock));
            interest->still_in_register_pending_list = 0;
            interest->db_block_queue_insert_us = 0;
        }
        interest_assign_state(interest, unregistered);
        interest->origin_incarnation = interest->column->table->origins[interest->origin_db_index]->incarnation;
    }

    /* If the origin DB is not ready, we do no registration/etc. (And
     * that is good/acceptable/normal) However, if the db BECAME
     * not-ready while processing a request, there are some
     * circumstances where we can now satisfy our request completion
     * logic. */
    if (interest->column->table->origins[interest->origin_db_index]->last_status == wally_origin_status_not_ready) {
        //fprintf(stderr, "%s:%s:%d: Origin not ready\n", __FILE__, __FUNCTION__, __LINE__);
        int do_call = 0;
        if ((!interest->request_complete_atleast_one) ||
            (!interest->request_complete)) {
            if (ZLIST_FIRST(&(interest->rows))) {
                interest->request_complete_atleast_one = 1;
                interest->request_complete = 1;
                do_call = 1;
            }
        }
        if (!interest->request_complete) {
            if (interest->origin_db_index == (interest->column->table->origins_count - 1)) {
                interest->request_complete = 1;
                do_call = 1;
            }
        }

        if (do_call) {
            wally_interest_result_callbacks(interest);
        }

		ZPATH_RWLOCK_UNLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);
        return WALLY_RESULT_NO_ERROR;
    }

    if ((ZLIST_FIRST(&(interest->registrant_callbacks)))) {
        /* In this case, the goal is to end up registered. */
        have_registrants = 1;
    } else {
        /* In this case, the goal is to end up unregistered. */
        have_registrants = 0;
    }

    switch (interest->origin_db_state) {
    case unregistered:
        if (have_registrants) {
            /* Check if we're post-deregistration during our promotion
             * phase. */
            if (interest->origin_db_promote && (interest->origin_db_index < (interest->column->table->origins_count - 1 ))) {
                interest->origin_db_promote = 0;
				ZPATH_RWLOCK_UNLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);
                interest->origin_db_index++;
				ZPATH_RWLOCK_RDLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);
                interest->origin_incarnation = interest->column->table->origins[interest->origin_db_index]->incarnation;
                WALLY_DEBUG_REGISTRATION("Promotion complete: %s", interest->description);
            }
            /* Send registration to CURRENT origin DB */
            send_register_interest_internal(interest);
        } else {
            do_delete = 1;
        }
        break;
    case registered_waiting_for_response:
        if (have_registrants) {
            /* Do nothing. We are waiting for the request_complete callback. */
        } else {
            /* Need to deregister this interest before it goes away. */
            send_deregister_interest_internal(interest);
            if (interest->origin_db_state == deregistration_xmit_pending) {
                /* Need to wait for it to clear... */
            } else {
                do_delete = 1;
            }
        }
        break;
    case registered:
        if (have_registrants) {
            /* We have received a response from the current origin DB,
             * so we can do the sole DB promotion check... */
            if (interest->origin_db_index < (interest->column->table->origins_count - 1)) {
                /* There is room for promotion. We promote regardless
                 * of whether the origin DB is ready, because we
                 * already have all the info out of the local DB that
                 * we could possibly gather. */
                interest->origin_db_promote = 1;
                WALLY_DEBUG_REGISTRATION("%s: %s Promoting from %s to %s, max = %d",
                                         interest->column->table->wally->name,
                                         interest->description,
                                         interest->column->table->origins[interest->origin_db_index]->name,
                                         interest->column->table->origins[interest->origin_db_index+1]->name,
                                         (int)(interest->column->table->origins_count - 1));
                send_deregister_interest_internal(interest);
                if (interest->origin_db_state == deregistration_xmit_pending) {
                    /* This will be handled later, when we can xmit
                     * the deregistration. */
                } else {
                    /* We should be unregistered now. */
                    if (interest->origin_db_state == unregistered) {
						ZPATH_RWLOCK_UNLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);
                        interest->origin_db_index++;
						ZPATH_RWLOCK_RDLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);
                        interest->origin_db_promote = 0;
                        interest->origin_incarnation = interest->column->table->origins[interest->origin_db_index]->incarnation;
                        WALLY_DEBUG_REGISTRATION("Promotion complete: %s", interest->description);
                        if (interest->column->table->origins[interest->origin_db_index]->last_status == wally_origin_status_ready) {
                            send_register_interest_internal(interest);
                        }
                    } else {
                        /* XXX LOG. Nasty. */
                    }
                }
            } else {
                /* We are registered on our ultimate origin DB. Do
                 * nothing. */
            }
        } else {
            /* We're registered, but we shouldn't be. Deregister. */
            send_deregister_interest_internal(interest);
            if (interest->origin_db_state != deregistration_xmit_pending) {
                do_delete = 1;
            }
        }
        break;
    case registration_xmit_pending:
        if (have_registrants) {
            /* Do nothing */
        } else {
            /* Undo registration and delete. */
            send_deregister_interest_internal(interest);
            if (interest->origin_db_state != deregistration_xmit_pending) {
                do_delete = 1;
            }
        }
        break;
    case deregistration_xmit_pending:
        if (have_registrants) {
            /* Undo deregistration... */
            send_register_interest_internal(interest);
        } else {
            /* Do nothing. */
        }
        break;
    default:
        /* XXX Log */
        break;
    }

    if (do_delete) {
        wally_interest_delete(interest);
    }

    ZPATH_RWLOCK_UNLOCK(&(interest->column->table->origins[interest->origin_db_index]->lock), __FILE__, __LINE__);

    return WALLY_RESULT_NO_ERROR;
}

void wally_interlock_lock_1(struct wally_interlock *i)
{
    i->cond = (pthread_cond_t)PTHREAD_COND_INITIALIZER;
    i->lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    pthread_mutex_lock(&(i->lock));
}

void wally_interlock_lock_2(struct wally_interlock *i)
{
    pthread_cond_wait(&(i->cond), &(i->lock));
}

void wally_interlock_release(struct wally_interlock *i)
{
    pthread_mutex_lock(&(i->lock));
    pthread_cond_signal(&(i->cond));
    pthread_mutex_unlock(&(i->lock));
}

const char *wally_version(void)
{
    return ZPATH_VERSION;
}

const char *wally_table_name(struct wally_table *table)
{
    return table->name;
}

int64_t wally_table_max_sequence_seen(struct wally_table *table)
{
    return table->max_sequence_seen;
}

int64_t wally_table_get_remote_update_time(struct wally_table *table)
{
    return table->last_remote_update_time_s;
}

int64_t wally_table_paused_sequence(struct wally_table *table)
{
    return table->paused_seq;
}
int64_t wally_table_max_sequence_paused(struct wally_table *table)
{
    return table->max_seq_paused;
}

enum table_pause_status wally_table_pause_status(struct wally_table *table)
{
    return table->pause_status;
}

const char *wally_table_wally_name(struct wally_table *table)
{
    return table->wally->name;
}

void wally_table_set_is_pausable(struct wally_table *table, int value)
{
    table->is_pausable = value;
}

struct wally_callback_queue *wally_callback_queue_create(void)
{
    struct wally_callback_queue *queue;

    queue = WALLY_CALLOC(sizeof(*queue));
    if (queue) {
        queue->lock = ZPATH_MUTEX_INIT;
        ZLIST_INIT(&(queue->callbacks));
    }
    return queue;
}

int wally_callback_queue_destroy(struct wally_callback_queue *queue)
{
    struct wally_callback *ele;
    if (!queue) return WALLY_RESULT_BAD_ARGUMENT;
    /* Should not need to lock... */
    while ((ele = ZLIST_FIRST(&(queue->callbacks)))) {
        ZLIST_REMOVE(ele, list);
        WALLY_FREE(ele);
    }
    WALLY_FREE(queue);
    return WALLY_RESULT_NO_ERROR;
}

int wally_callback_queue_depth(struct wally_callback_queue *queue)
{
    struct wally_callback *ele;
    int count = 0;
    if (!queue) return 0;
    /* Should not need to lock... */
    ZLIST_FOREACH(ele, &(queue->callbacks), list) {
        count++;
    }
    return count;
}

int wally_callback_queue_add(struct wally_callback_queue *queue,
                             wally_response_callback_f *callback,
                             void *void_cookie,
                             int64_t int_cookie)
{
    struct wally_callback *ele;

    ele = WALLY_CALLOC(sizeof(*ele));
    if (!ele) return WALLY_RESULT_NO_MEMORY;
    ele->callback = callback;
    ele->void_cookie = void_cookie;
    ele->int_cookie = int_cookie;
    ele->zbase = zevent_self();
    ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);
    ZLIST_INSERT_HEAD(&(queue->callbacks), ele, list);
    ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
    return WALLY_RESULT_NO_ERROR;
}

/* Deferred callback... */
static void callback_queue_callback_defer(void *cookie1, void *cookie2)
{
    struct wally_callback *ele = cookie1;
    (ele->callback)(ele->void_cookie, NULL, NULL, ele->int_cookie, 0);
    WALLY_FREE(ele);
}

static void callback_queue_callback_thread(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct wally_callback *ele = void_cookie;
    (ele->callback)(ele->void_cookie, NULL, NULL, ele->int_cookie, 0);
    WALLY_FREE(ele);
}

int wally_callback_queue_callback(struct wally_callback_queue *queue)
{
    struct wally_callback *ele;
    int ret = WALLY_RESULT_NO_ERROR;

    if (!queue) return WALLY_RESULT_BAD_ARGUMENT;

    ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);

    while ((ele = ZLIST_FIRST(&(queue->callbacks)))) {
        ZLIST_REMOVE(ele, list);
        if (ele->callback) {
            if (ele->zbase) {
                zevent_base_call(ele->zbase, callback_queue_callback_thread, ele, 0);
            } else {
                zevent_defer(callback_queue_callback_defer, ele, NULL, 0);
            }
        } else {
            WALLY_FREE(ele);
        }
    }

    ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
    return ret;
}

void wally_object_release_slow(struct wally *wally, struct argo_object *object)
{
    struct wally_object_queue *q;

    q = WALLY_MALLOC(sizeof(*q));
    if (!q) {
        argo_object_release(object);
        return;
    }
    q->object = object;
    q->timestamp_s = epoch_s();

    ZPATH_MUTEX_LOCK(&(wally->slow_free_object_list_lock), __FILE__, __LINE__);
    ZTAILQ_INSERT_TAIL(&(wally->slow_free_object_list), q, list);
    ZPATH_MUTEX_UNLOCK(&(wally->slow_free_object_list_lock), __FILE__, __LINE__);

    return;
}


void wally_periodic_free(struct wally *wally)
{
    int64_t now = epoch_s();
    struct wally_object_queue *q;

    while (1) {
        ZPATH_MUTEX_LOCK(&(wally->slow_free_object_list_lock), __FILE__, __LINE__);
        q = ZTAILQ_FIRST(&(wally->slow_free_object_list));
        if (!q) {
            ZPATH_MUTEX_UNLOCK(&(wally->slow_free_object_list_lock), __FILE__, __LINE__);
            break;
        }
        if ((q->timestamp_s + 120) >= now) {
            ZPATH_MUTEX_UNLOCK(&(wally->slow_free_object_list_lock), __FILE__, __LINE__);
            break;
        }
        ZTAILQ_REMOVE(&(wally->slow_free_object_list), q, list);
        ZPATH_MUTEX_UNLOCK(&(wally->slow_free_object_list_lock), __FILE__, __LINE__);
        argo_object_release(q->object);
        WALLY_FREE(q);
    }
}

const char *wally_interest_state_str(enum interest_db_state state)
{
    switch (state) {
    default:
        return "invalid";
        /*break;*/
    case unregistered:
        return "unregistered";
        /*break;*/
    case registered_waiting_for_response:
        return "registered_waiting";
        /*break;*/
    case registered:
        return "registered";
        /*break;*/
    case registration_xmit_pending:
        return "registration_xmit_pending";
        /*break;*/
    case deregistration_xmit_pending:
        return "deregistration_xmit_pending";
        /*break;*/
    case passive:
        return "passive";
        /*break;*/
    case deleted:
        return "deleted";
        /*break;*/
    }
}

void wally_fill_interest_state_stats(struct wally_table *table, struct wally_stats *wally_stats)
{
    memset(wally_stats, 0, sizeof(struct wally_stats));
    wally_stats->unregistered    = table->interest_state_count[unregistered];
    wally_stats->reg_waiting     = table->interest_state_count[registered_waiting_for_response];
    wally_stats->registered      = table->interest_state_count[registered];
    wally_stats->reg_xmit_pend   = table->interest_state_count[registration_xmit_pending];
    wally_stats->dereg_xmit_pend = table->interest_state_count[deregistration_xmit_pending];
    wally_stats->passive         = table->interest_state_count[passive];
    wally_stats->deleted         = table->interest_state_count[deleted];
}

void wally_fill_table_level_sync_pause_stats(struct wally_table *table,
                                             struct wally_sync_pause_stats *stats)
{
    memset(stats, 0, sizeof(struct wally_sync_pause_stats));
    stats->table_pause_count = table->table_pause_count;
    stats->table_resume_count = table->table_resume_count;
    stats->table_resumed_err = table->table_resumed_err;
    stats->rows_skipped_count = table->rows_skipped_count;
    stats->rows_resumed_count = table->rows_resumed_count;
}

void wally_fill_wally_level_sync_pause_stats(struct wally *wally,
                                             struct wally_sync_pause_stats *stats)
{
    memset(stats, 0, sizeof(struct wally_sync_pause_stats));
    for (size_t i = 0; i < wally->tables_count; i++) {
        struct wally_table *table = wally->tables[i];
        if (!table->is_pausable)
            continue;
        stats->table_pause_count += table->table_pause_count;
        stats->table_resume_count += table->table_resume_count;
        stats->table_resumed_err += table->table_resumed_err;
        stats->rows_skipped_count += table->rows_skipped_count;
        stats->rows_resumed_count += table->rows_resumed_count;
    }
}

void wally_fill_stats(struct wally *wally, struct wally_stats *wally_stats)
{
    int64_t accumulate[WALLY_ORIGIN_STATE_COUNT];
    size_t i;
    size_t j;
    memset(accumulate, 0, sizeof(accumulate));
    for (i = 0; i < wally->tables_count; i++) {
        struct wally_table *table = wally->tables[i];
        for (j = 0; j < WALLY_ORIGIN_STATE_COUNT; j++) {
            accumulate[j] += table->interest_state_count[j];
        }
    }
    wally_stats->unregistered = accumulate[unregistered];
    wally_stats->reg_waiting = accumulate[registered_waiting_for_response];
    wally_stats->registered = accumulate[registered];
    wally_stats->reg_xmit_pend = accumulate[registration_xmit_pending];
    wally_stats->dereg_xmit_pend = accumulate[deregistration_xmit_pending];
    wally_stats->passive = accumulate[passive];
    wally_stats->deleted = accumulate[deleted];

    // sum write queues
    int64_t total_in;
    int64_t total_out;
    total_in = wally_stats->write_queue_in;
    total_out = wally_stats->write_queue_out;
    wally_stats->write_queue_depth = 0;
    wally_stats->write_queue_in = 0;
    wally_stats->write_queue_out = 0;
    wally_stats->write_queue_fail = 0;
    wally_stats->total_executed_batch = 0;
    wally_stats->total_success_batch = 0;
    wally_stats->total_failed_batch = 0;
    int64_t max_delta = 0;
    for (int origin_ix = 0; origin_ix < wally->origins_count; origin_ix++) {
        struct wally_origin *origin = wally->origins[origin_ix];
        if (!origin->is_writable) continue;
        for (int wq_ix = 0; wq_ix < origin->write_queue_count; wq_ix++) {
            wally_stats->write_queue_in += origin->wq_info[wq_ix].count_in;
            wally_stats->write_queue_out += origin->wq_info[wq_ix].count_out;
            wally_stats->write_queue_fail += origin->wq_info[wq_ix].count_fail;
            wally_stats->total_executed_batch += origin->wq_info[wq_ix].total_executed_batch ;
            wally_stats->total_success_batch += origin->wq_info[wq_ix].total_success_batch;
            wally_stats->total_failed_batch += origin->wq_info[wq_ix].total_failed_batch;
        }
        if (max_delta < (origin->write_queue_last_update_time - origin->write_queue_start_time)) {
            max_delta = (origin->write_queue_last_update_time - origin->write_queue_start_time);
        }
    }
    wally_stats->write_queue_depth = wally_stats->write_queue_in - wally_stats->write_queue_out;
    wally_stats->write_queue_in_rate = wally_stats->write_queue_in - total_in;
    wally_stats->write_queue_out_rate = wally_stats->write_queue_out - total_out;

    wally_stats->batch_size = wally_gbl_cfg.write_batch_size;
    wally_stats->write_queue_delta_time_s = max_delta/1000000; // to seconds

    wally_stats->batch_hb_count = wally->batch_hb_count;


}


void wally_fill_fohh_connection_stats(struct wally *wally, struct wally_fohh_connection_stats *wally_fohh_connection_stats) {
    wally_fohh_connection_stats->num_server_connect = wally->num_server_connect;
    wally_fohh_connection_stats->num_server_disconnect = wally->num_server_disconnect;
    wally_fohh_connection_stats->num_server_active = wally->num_server_active;
    wally_fohh_connection_stats->num_client_connect = wally->num_client_connect;
    wally_fohh_connection_stats->num_client_disconnect = wally->num_client_disconnect;
    wally_fohh_connection_stats->num_client_active = wally->num_client_active;
}

int compare(const void* a, const void* b) {
   return (*(int64_t*)a - *(int64_t*)b);
}

void wally_fill_registrant_stats(struct wally *wally, struct wally_registrant_stats *wally_registrant_stats)
{
    int64_t sum_time = 0;
    int p50 = 0;
    int p75 = 0;
    int p90 = 0;
    int64_t total_res_60s = 0;

    total_res_60s = wally->registrant_stat.total_response -  wally_registrant_stats->total_registration_response;
    memset(wally_registrant_stats, 0, sizeof(struct wally_registrant_stats));
    wally_registrant_stats->total_registration_requests = wally->registrant_stat.total_requests;
    wally_registrant_stats->total_registration_failed_requests = wally->registrant_stat.total_failed_requests;
    wally_registrant_stats->max_response_us = wally->registrant_stat.max_response_us;
    wally_registrant_stats->min_response_us = wally->registrant_stat.min_response_us;
    wally_registrant_stats->row_notification_count = wally->registrant_stat.row_notification_count;
    wally_registrant_stats->row_notification_failed_count = wally->registrant_stat.row_notification_failed_count;
    wally_registrant_stats->interest_duplicate = wally->registrant_stat.interest_duplicate;
    wally_registrant_stats->total_deregister_count = wally->registrant_stat.total_deregister_count;

    wally_registrant_stats->total_registration_response = wally->registrant_stat.total_response;
    if(total_res_60s > 0) {
        wally_registrant_stats->avg_response_time_last_60s_us = (wally->registrant_stat.total_response_time_60s)/total_res_60s;
    }

    wally_registrant_stats->interest_unregistered    =  wally->registrant_stat.interest_state_count[unregistered];
    wally_registrant_stats->interest_reg_waiting     =  wally->registrant_stat.interest_state_count[registered_waiting_for_response];
    wally_registrant_stats->interest_registered      =  wally->registrant_stat.interest_state_count[registered];
    wally_registrant_stats->interest_reg_xmit_pend   =  wally->registrant_stat.interest_state_count[registration_xmit_pending];
    wally_registrant_stats->interest_dereg_xmit_pend =  wally->registrant_stat.interest_state_count[deregistration_xmit_pending];
    wally_registrant_stats->interest_passive         =  wally->registrant_stat.interest_state_count[passive];
    wally_registrant_stats->interest_deleted         =  wally->registrant_stat.interest_state_count[deleted];

    wally_registrant_stats->interest_registered_last_10s = wally->registrant_stat.interest_registered_his[0];
    wally_registrant_stats->interest_registered_last_20s = wally->registrant_stat.interest_registered_his[1];
    wally_registrant_stats->interest_registered_last_30s = wally->registrant_stat.interest_registered_his[2];
    wally_registrant_stats->interest_registered_last_40s = wally->registrant_stat.interest_registered_his[3];
    wally_registrant_stats->interest_registered_last_50s = wally->registrant_stat.interest_registered_his[4];
    wally_registrant_stats->interest_registered_last_60s = wally->registrant_stat.interest_registered_his[5];

    wally_registrant_stats->current_time_us = epoch_us();

    if (wally->registrant_stat.total_response_60s > 0) {
        wally_registrant_stats->total_response_last_60s = wally->registrant_stat.total_response_60s;

        qsort(wally->registrant_stat.response_time_60s, wally->registrant_stat.total_response_60s, sizeof(int64_t), compare);

        p50 = wally->registrant_stat.total_response_60s / 2;
        p75 = (wally->registrant_stat.total_response_60s * 3) / 4;
        p90 = (wally->registrant_stat.total_response_60s * 9) / 10;

        wally_registrant_stats->response_time_p50_last_60s_us = wally->registrant_stat.response_time_60s[p50];
        wally_registrant_stats->response_time_p75_last_60s_us = wally->registrant_stat.response_time_60s[p75];
        wally_registrant_stats->response_time_p90_last_60s_us = wally->registrant_stat.response_time_60s[p90];
    }

    sum_time = 0;
    wally_registrant_stats->max_interest_up_registration_us = wally->registrant_stat.max_interest_registration_us;
    wally_registrant_stats->min_interest_up_registration_us = wally->registrant_stat.min_interest_registration_us;

    if (wally->registrant_stat.total_interest_registration_60s > 0) {

        wally_registrant_stats->total_interest_up_registration_last_60s = wally->registrant_stat.total_interest_registration_60s;
        for(int i = 0; i < wally->registrant_stat.total_interest_registration_60s ; i++){
           sum_time += wally->registrant_stat.interest_registration_60s[i];
        }
        wally_registrant_stats->avg_interest_up_registration_last_60s_us = sum_time / wally->registrant_stat.total_interest_registration_60s;
        qsort(wally->registrant_stat.interest_registration_60s, wally->registrant_stat.total_interest_registration_60s, sizeof(int64_t), compare);

        p50 = wally->registrant_stat.total_interest_registration_60s / 2;
        p75 = (wally->registrant_stat.total_interest_registration_60s * 3) / 4;
        p90 = (wally->registrant_stat.total_interest_registration_60s * 9) / 10;

        wally_registrant_stats->interest_up_registration_time_p50_last_60s_us = wally->registrant_stat.interest_registration_60s[p50];
        wally_registrant_stats->interest_up_registration_time_p75_last_60s_us = wally->registrant_stat.interest_registration_60s[p75];
        wally_registrant_stats->interest_up_registration_time_p90_last_60s_us = wally->registrant_stat.interest_registration_60s[p90];
    }

    wally->registrant_stat.last_stats_update_us = epoch_us();
    wally->registrant_stat.total_response_60s = 0;
    wally->registrant_stat.total_interest_registration_60s = 0;
    wally->registrant_stat.total_response_time_60s = 0;
    wally->registrant_stat.max_response_us = 0;
    wally->registrant_stat.min_response_us = 0;

    memset(wally->registrant_stat.response_time_60s, 0, sizeof(wally->registrant_stat.response_time_60s));
    memset(wally->registrant_stat.interest_registration_60s, 0, sizeof(wally->registrant_stat.interest_registration_60s));
    memset(wally->registrant_stat.interest_registered_his, 0, sizeof(wally->registrant_stat.interest_registered_his));

}

void wally_fohh_server_connect_stat(struct wally *wally) {
    wally->num_server_connect++;
    wally->num_server_active++;
}

void wally_fohh_server_disconnect_stat(struct wally *wally) {
    wally->num_server_disconnect++;
    wally->num_server_active--;
}


void wally_fohh_client_connect_stat(struct wally *wally) {
    wally->num_client_connect++;
    wally->num_client_active++;
}

void wally_fohh_client_disconnect_stat(struct wally *wally) {
    wally->num_client_disconnect++;
    wally->num_client_active--;
}

void wally_set_wallyd(struct wally *wally)
{
    wally->is_wallyd = 1;
}

int wally_is_wallyd(struct wally *wally)
{
    return wally->is_wallyd;
}

void wally_set_fully_load_complete(struct wally *wally)
{
    wally->is_fully_load_complete = 1;
}

int wally_is_fully_load_complete(struct wally *wally)
{
    return wally->is_fully_load_complete;
}


/* Descending order comparator for write queue.
 * Expected behavior of comparator(const void* p1, const void* p2):
 *     <0 The element pointed by p1 goes before the element pointed by p2
 *     >0 The element pointed by p1 goes after the element pointed by p2
 */
static inline int compare_wq_depth_descending(const void *a, const void *b) {
    int64_t depth_a = ((struct wally_write_queue_info *)a)->count_in - ((struct wally_write_queue_info *)a)->count_out;
    int64_t depth_b = ((struct wally_write_queue_info *)b)->count_in - ((struct wally_write_queue_info *)b)->count_out;
    return (depth_b - depth_a);
}

static inline int compare_wq_name_ascending(const void *a, const void *b) {
    return strcmp(((struct wally_write_queue_info *)a)->name, ((struct wally_write_queue_info *)b)->name);
}

int wally_dump_write_queue(struct wally *wally, int method, char *buf, size_t buf_len, int num_rows)
{
    size_t ix;
    char *s = buf;
    char *e = buf + buf_len;
    struct wally_write_queue_info *wq_info = WALLY_CALLOC(sizeof(struct wally_write_queue_info) * WALLY_MAX_TABLES);

    if (num_rows <= 0 || num_rows > WALLY_MAX_TABLES) num_rows = WALLY_MAX_TABLES;

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    {
        for (ix = 0; ix < wally->origins_count; ix++) {
            struct wally_origin *origin = wally->origins[ix];
            struct wally_write_queue_info accumulate;
            int wq_count;

            // only count_in, count_out, count_fail are meaningful in accumulation
            accumulate.count_in = accumulate.count_out = accumulate.count_fail = 0;
            wq_count = origin->write_queue_count;
            if (wq_count > WALLY_MAX_TABLES) wq_count = WALLY_MAX_TABLES;

            if (!origin->is_writable) {
                s += sxprintf(s, e, "  origin is not writable.\n");
                continue;
            }
            if (wq_count <= 0) {
                s += sxprintf(s, e, "  origin has no write queues.\n");
                continue;
            }

            s += sxprintf(s, e, "Wally %s origin %s: status=%d, #write_q=%d\n", wally->name, origin->name, origin->last_status, wq_count);

            memcpy(wq_info, &origin->wq_info, sizeof(struct wally_write_queue_info) * wq_count);
            switch(method) {
            case 1: // by name ascending
                qsort(wq_info, wq_count, sizeof(struct wally_write_queue_info), compare_wq_name_ascending);
                break;
            case 2: // by index, no sorting needed
                break;
            case 0: // by count descending
            default:
                qsort(wq_info, wq_count, sizeof(struct wally_write_queue_info), compare_wq_depth_descending);
                break;
            }

            for (int wq_ix = 0; wq_ix < num_rows && wq_ix < wq_count; wq_ix++) {
                int64_t net = (wq_info[wq_ix].count_in - wq_info[wq_ix].count_out);
                if (method == 0 && net == 0) break;  // no need to print a bunch of 0s

                s += sxprintf(s, e, "       q_indx: %3d  conn: %2d  pending: %12"PRId64"  in: %12"PRId64"  out: %12"PRId64"  fail: %12"PRId64"  batch: %6d  %s\n",
                                            wq_info[wq_ix].wq_indx,
                                            wq_info[wq_ix].conn_indx,
                                            net,
                                            wq_info[wq_ix].count_in,
                                            wq_info[wq_ix].count_out,
                                            wq_info[wq_ix].count_fail,
                                            wq_info[wq_ix].last_batch,
                                            wq_info[wq_ix].name);

                accumulate.count_in += wq_info[wq_ix].count_in;
                accumulate.count_out += wq_info[wq_ix].count_out;
                accumulate.count_fail += wq_info[wq_ix].count_fail;
            }
            s += sxprintf(s, e, "  SUM: q_indx: %3s  conn: %2s  pending: %12"PRId64"  in: %12"PRId64"  out: %12"PRId64"  fail: %12"PRId64"  batch: %6s  %s\n",
                                        "-",
                                        "-",
                                        (accumulate.count_in - accumulate.count_out),
                                        accumulate.count_in,
                                        accumulate.count_out,
                                        accumulate.count_fail,
                                        "-",
                                        "-");
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    WALLY_FREE(wq_info);
    return WALLY_RESULT_NO_ERROR;
}
/*
 *  wally_table_pause
 *      sets the given table to paused state
 *      Any row updates for the table with seq number above paused_seq will be ignored.
 */
int wally_table_pause(struct wally *wally, const char *table_name)
{
    int result = WALLY_RESULT_NO_ERROR;
    struct wally_table *table;

    table = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (!table) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: Trying to pause wally %s table %s, "
                            "but table doesn't exist!",  wally->name, table_name);
        result = WALLY_RESULT_NOT_FOUND;
        return result;
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    if (table->paused) {
        WALLY_LOG(AL_WARNING, "wally_pause_debug: Trying to pause wally %s table %s, "
                            "but it's already paused!", wally->name, table_name);
        result = WALLY_RESULT_BAD_STATE;
    } else {
        table->paused = 1;
        table->pause_status = wally_table_paused;
        table->paused_seq = table->max_sequence_seen;
        table->max_seq_paused = 0;  /* relevant only when we actually pause a row */
        table->table_pause_count++;
        WALLY_LOG(AL_NOTICE, "wally_pause_debug: Pausing remote sync for wally %s table %s seq: %"PRId64" ",
                    wally->name, table_name, table->paused_seq);
    }
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return result;
}

static inline int wally_table_resume_null_column_registered_table(struct wally_table *table)
{
    struct wally_index_column *column;
    int result = WALLY_RESULT_NO_ERROR;

    WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Initiate Resuming NULL-registered table %s", table->name);

    /* If no rows were paused, we can bail out rightaway */
    if (!table->max_seq_paused) {
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: null-reg table %s. "
                                "Skip deregister/register as no rows paused", table->name);
        /* Instead of directly setting to not_paused, set to resume_completed to trigger callback */
        table->pause_status = wally_table_resume_completed;
        return result;
    }

    column = wally_table_get_index_simple_internal(table, "");
    if (!column) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: Could not get null column for table %s for resume", table->name);
        return WALLY_RESULT_ERR;
    }

    result = wally_table_resume_table_per_registrant(table->default_registrant, table, column, table->paused_seq, NULL, 0);
    if (result)
        return WALLY_RESULT_ERR;

    return WALLY_RESULT_NO_ERROR;
}

static inline int wally_table_resume_non_null_column_registered_table(struct wally_table *table)
{
    WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Initiate Resuming non NULL-registered table %s", table->name);

    /* Quick check if we paused atleast one row */
    int64_t table_size = 0;
    for (int i = 0; i < table->indexed_columns_count; i++)
        table_size += zhash_table_get_size(table->indexed_columns[i]->paused_keys);

    if (!table_size) {
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: non-null-reg table %s. "
                                "Skip deregister/register as no rows paused", table->name);
        /* Instead of directly setting to not_paused, set to resume_completed to trigger callback */
        table->pause_status = wally_table_resume_completed;
        return WALLY_RESULT_NO_ERROR;
    }

    table_resume_error_count = 0;
    for (int i = 0; i < table->indexed_columns_count; i++) {
        WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Resume table %s : refreshing for column: %s",
                                table->name, table->indexed_columns[i]->name);
        wally_column_refresh_interest(table->indexed_columns[i]);
    }
    if (table_resume_error_count)
        return WALLY_RESULT_ERR;

    return WALLY_RESULT_NO_ERROR;
}
/*
 * wally_table_resume
 *  unpause a table which we paused previously.
 *  For NULL registered tables,
 *      we request rows with seq > paused_seq
 *  For non-NULL registered tables,
 *      we run through the cached keys, and re-register interest for them.
 *      This will cause the remote to sent the latest update for the key.
 */
int wally_table_resume(struct wally *wally, const char *table_name)
{
    int result = WALLY_RESULT_NO_ERROR;
    struct wally_table *table;

    table = zhash_table_lookup(wally->tables_hash, table_name, strlen(table_name), NULL);
    if (!table) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: Trying to resume wally %s table %s, "
                            "but table doesn't exist!", wally->name, table_name);
        result = WALLY_RESULT_NOT_FOUND;
        return result;
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);

    if (!table->paused) {
        WALLY_LOG(AL_ERROR, "wally_pause_debug: Trying to resume wally %s table %s, "
                            "but it's not in paused state!", wally->name, table_name);
        result = WALLY_RESULT_BAD_STATE;
    } else {
        table_resume_error_count = 0;

        /* Not resetting paused_seq yet, as we use it for calculating stats */
        table->paused  = 0;
        table->pause_status = wally_table_resume_in_progress;

        if (wally_table_is_null_column_registered(table)) {
            result = wally_table_resume_null_column_registered_table(table);
        }
        else { //not fully loaded
            result = wally_table_resume_non_null_column_registered_table(table);
        }
        table->table_resume_count++;
        if (result)
            table->table_resumed_err++;

        WALLY_LOG(AL_NOTICE, "wally_pause_debug: Resuming remote sync for wally %s table %s",
                            wally->name, table_name);
    }

    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);

    return result;
}

/*
 * Check if all tables in the wally have completed resuming.
 * Returns 1 if all resumed tables are in completed state, 0 otherwise.
 */
int wally_all_tables_resume_completed(struct wally *wally)
{
    struct wally_table *table;
    int i;
    int has_resuming_tables = 0;

    if (!wally) {
        WALLY_LOG(AL_CRITICAL, "wally_pause_debug: NULL wally parameter while checking if all tables resumed");
        return 0;
    }

    for (i = 0; i < wally->tables_count; i++) {
        table = wally->tables[i];
        if (table) {
            ZPATH_RWLOCK_RDLOCK(&(table->lock), __FILE__, __LINE__);
            if (!table->paused && table->pause_status == wally_table_resume_in_progress) {
                has_resuming_tables = 1;
                ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
                break;
            }
            ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        }
    }

    return !has_resuming_tables;
}

/*
 * Execute the post-resume callback if all tables have completed resuming
 * and the callback is registered.
 */
void wally_check_post_resume_callback(struct wally *wally)
{
    int all_completed;

    if (!wally) {
        WALLY_LOG(AL_CRITICAL, "wally_pause_debug: NULL wally parameter during post-resume processing");
        return;
    }

    /* If no callback registered, nothing to do */
    if (!wally->post_resume_callback) {
        return;
    }

    /* Check if all tables have completed resuming */
    all_completed = wally_all_tables_resume_completed(wally);

    wally_post_resume_callback_f *callback = all_completed ? wally->post_resume_callback : NULL;

    if (callback) {
        WALLY_LOG(AL_NOTICE, "wally_pause_debug: All tables resumed, executing post_resume_callback for wally %s",
                    wally->name);

        /* Call the post-resume callback */
        int result = callback(wally);
        if (result) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: post_resume_callback for wally %s returned error %d",
                    wally->name, result);
        }
    }

    /*
    * After callback is executed, reset all table states from
    * wally_table_resume_completed to wally_table_not_paused
    */
    for (int i = 0; i < wally->tables_count; i++) {
        struct wally_table *table = wally->tables[i];
        if (table) {
            ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
            if (table->pause_status == wally_table_resume_completed) {
                WALLY_DEBUG_SYNC_PAUSE("wally_pause_debug: Changing table %s state from resume_completed to not_paused",
                                        table->name);
                table->pause_status = wally_table_not_paused;
            }
            ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        }
    }
}

static void wally_interest_update_stats_pending_window (int64_t now, struct wally_interest *interest)
{
    int64_t diff = 0;
    int window_index = 0;
    uint8_t mask = 0;
    uint8_t delta = 0;

    if (interest == NULL) {
        return;
    }

    diff = (now - interest->origin_db_waiting_for_response_start_us);

    if(diff > (now - interest->last_row_us) ) {
        diff = (now - interest->last_row_us);
    }

    if (diff > (6*INTEREST_PENDING_WINDOW_INTERVAL)) {
        return ; // more then 60 sec is long pending
    }

    window_index = diff/(INTEREST_PENDING_WINDOW_INTERVAL); // 10 sec

    /* window_index = 0 then mask = 00000001 , window_index = 1 then mask = 00000011 ..*/
    mask = (1 << (window_index + 1)) - 1;

    delta = mask ^ interest->waiting_response_time_hist;
    if (delta) {
        wally_table_interest_update_pending_window(delta, interest);
    }
    interest->waiting_response_time_hist = mask;
}

static inline void wally_table_interest_update_pending_window(uint8_t delta, struct wally_interest *interest)
{
    if (!interest || !interest->column || !interest->column->table) {
        return;
    }

    struct wally_table *table  = interest->column->table;
    for (int i = 0; i < INTEREST_PENDING_WINDOW_SIZE; i++) {
        if (delta & (1 << i) ) {
            table->interest_pending_count_window[i] ++;
        }
    }
}

static void wally_updated_registrant_response_status(struct wally *wally, int64_t delta_time)
{
	ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);
   if ( wally->registrant_stat.min_response_us  == 0 && wally->registrant_stat.max_response_us == 0 ) {
       wally->registrant_stat.max_response_us = delta_time;
       wally->registrant_stat.min_response_us = delta_time;
   } else {
         if (delta_time < wally->registrant_stat.min_response_us) {
              wally->registrant_stat.min_response_us = delta_time;
         }

         if (delta_time > wally->registrant_stat.max_response_us) {
             wally->registrant_stat.max_response_us = delta_time;
         }
   }
   wally->registrant_stat.total_response_time_60s += delta_time;

   if (wally->registrant_stat.total_response_60s < MAX_REG_REQ_CNT)
   {
        wally->registrant_stat.response_time_60s[wally->registrant_stat.total_response_60s] = delta_time;
        wally->registrant_stat.total_response_60s ++;
   }
   wally->registrant_stat.total_response ++;
   ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
}


static void wally_updated_registrant_db_wait_status(struct wally *wally, int64_t delta_time)
{
	ZPATH_RWLOCK_WRLOCK(&(wally->lock), __FILE__, __LINE__);
   if ( wally->registrant_stat.min_interest_registration_us == 0 && wally->registrant_stat.max_interest_registration_us == 0 ) {
       wally->registrant_stat.max_interest_registration_us = delta_time;
       wally->registrant_stat.min_interest_registration_us = delta_time;
   } else {
         if (delta_time < wally->registrant_stat.min_interest_registration_us) {
              wally->registrant_stat.min_interest_registration_us = delta_time;
         }

         if (delta_time > wally->registrant_stat.max_interest_registration_us) {
             wally->registrant_stat.max_interest_registration_us = delta_time;
         }
   }

   if (wally->registrant_stat.total_interest_registration_60s < MAX_REG_REQ_CNT)
   {
        wally->registrant_stat.interest_registration_60s[wally->registrant_stat.total_interest_registration_60s] = delta_time;
        wally->registrant_stat.total_interest_registration_60s++;
   }
	ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
}

int has_null_column(struct wally_table *t)
{
    int has_null = 0;
    size_t i;
    if(t) {
        for (i = 0; i < t->indexed_columns_count; i++) {
            if (t->indexed_columns[i]->is_null) {
                has_null = 1;
                break;
            }
        }
    }
    return has_null;
}

void init_min_seq_auto_update(struct wally_table *t)
{
    struct wally *wally = t->wally;
    void *wp_db = NULL;
    wally_xfer_callout_cleanup_f *cleanup_callback = NULL;

    if (t->cleanup_param.min_seq_auto_update && t->fully_loaded && wally->origins_count == 1 && has_null_column(t)) {
        cleanup_callback = wally->origins[0]->cleanup;
        wp_db = wally->origins[0]->callout_handle;
        if (cleanup_callback && wp_db) {
            (cleanup_callback)(wp_db,
                               t->name,
                               NULL,
                               NULL,
                               NULL,
                               0,
                               t->cleanup_param.min_seq_auto_update);
        }
    }
}

static int fetch_callback(void *response_callback_cookie,
                          struct wally_registrant *registrant,
                          struct wally_table *table,
                          int64_t request_id,
                          int row_count)
{
    char *table_name = response_callback_cookie;
    char total_time[50] = "NA";


    if(registrant && registrant->turnaround_time_us > 0) {
        snprintf(total_time , sizeof(total_time),"%"PRId64" us" , registrant->turnaround_time_us);
        registrant->turnaround_time_us = 0;
    }
    int32_t current_count = __sync_sub_and_fetch_4(&(responses_waiting), 1);
    WALLY_LOG(AL_NOTICE, "Table %s: Fully loaded: Read %d rows. %d tables left  load time %s", table_name, row_count, (int) current_count,total_time);
    table->fully_loaded_end_epoch_us = epoch_us();
    return WALLY_RESULT_NO_ERROR;
}


int do_wts_internal(struct wt *wt ,
                    struct wally_origin *wallyd_slave_db,
                    struct wally_origin *wallyd_remote_db,
                    struct wally *wallyd,
                    int fully_loaded,
                    struct table_cleanup_parameter cleanup_param ,
                    int load_tables,
                    wally_row_fixup_f *fixup_f,
                    int is_gwally,
                    wallyd_zpath_table_register_cb_f *zptr_f)
{
    struct argo_structure_description *desc;
    struct wally_table *table;
    int res = ZPATH_RESULT_NO_ERROR;
    __sync_sub_and_fetch_4(&(do_wts_done),1);

    desc = argo_register_global_structure(wt->table_name,
                                          wt->struct_size,
                                          wt->desc,
                                          wt->struct_field_count);
    if (!desc) {
        WALLY_LOG(AL_ERROR, "Could not register structure %s", wt->table_name);
        return WALLY_RESULT_ERR;
    }



    if (is_gwally) {
        if (wt->db_name) {
            table = wally_table_create_named_db_1(wallyd,
                                                  wt->writable_true_origin,
                                                  wt->db_name,
                                                  desc,
                                                  NULL,
                                                  NULL,
                                                  0,  /* Global table has to do hand-coded origins */
                                                  1,  /* Yes multiple index consistency */
                                                  fully_loaded,
                                                  NULL,
                                                  cleanup_param);
        } else {
            table = wally_table_create_named_db_1(wallyd,
                                                  wt->writable_true_origin,
                                                  wt->table_name,
                                                  desc,
                                                  NULL,
                                                  NULL,
                                                  0,  /* Global table has to do hand-coded origins */
                                                  1,
                                                  fully_loaded,
                                                  NULL,
                                                  cleanup_param);
        }
        if (!table) {
            WALLY_LOG(AL_ERROR, "Could not register create table - structure %s",
                       wt->table_name);
            return WALLY_RESULT_ERR;
        }
        if (wallyd_slave_db) {
            if ((res = wally_table_add_origin(wallyd,
                                              table,
                                              wallyd_slave_db))) {
                WALLY_LOG(AL_ERROR, "Could not add slave origin: %s", wally_result_string(res));
                return res;
            }
        }
        if (wallyd_remote_db) {
            if ((res = wally_table_add_origin(wallyd,
                                              table,
                                              wallyd_remote_db))) {
                WALLY_LOG(AL_ERROR, "Could not add remote origin: %s", wally_result_string(res));
                return res;
            }
        }
    } else {
        if (wt->db_name) {
            table = wally_table_create_named_db_1(wallyd,
                                                  wt->writable_true_origin,
                                                  wt->db_name,
                                                  desc,
                                                  NULL,
                                                  NULL,
                                                  1,  /* use_all_origins? Yes! */
                                                  1,  /* Yes multiple index consistency */
                                                  fully_loaded,
                                                  fixup_f,
                                                  cleanup_param);
        } else {
            table = wally_table_create_named_db_1(wallyd,
                                                  wt->writable_true_origin,
                                                  wt->table_name,
                                                  desc,
                                                  NULL,
                                                  NULL,
                                                  1,
                                                  1,
                                                  fully_loaded,
                                                  fixup_f,
                                                  cleanup_param);
        }
        if (!table) {
            WALLY_LOG(AL_ERROR, "Could not register create table - structure %s",
                       wt->table_name);
            return WALLY_RESULT_ERR;
        }

        if (zptr_f) {
            res = (zptr_f)(wallyd,table);
            if ( res ) return res;
        }
    }
    /* If fully loading tables, we need to register for each full table... */
    if (fully_loaded) {
        struct wally_index_column *col;
        col = wally_table_get_index(table, "");
        if (!col) {
            WALLY_LOG(AL_ERROR, "Could not get full table index for %s", wt->table_name);
            return WALLY_RESULT_ERR;
        }
        //WALLY_LOG(AL_DEBUG, "Fully loaded %s: Registering for rows", wt->table_name);
        res = wally_table_register_for_row(NULL,
                                           col,
                                           NULL,
                                           0,
                                           0,
                                           0,
                                           0,
                                           0,
                                           0,
                                           fetch_callback,
                                           wt->table_name);
        if (res) {
            if (res != WALLY_RESULT_ASYNCHRONOUS) {
                WALLY_LOG(AL_ERROR, "Could not register for all rows from %s: %s", wt->table_name, wally_result_string(res));
                return WALLY_RESULT_ERR;
            } else {
                int32_t current_count = __sync_add_and_fetch_4(&(responses_waiting), 1);
                WALLY_LOG(AL_NOTICE, "Enqueued %d tables for asynchronous read.Value of load_tables = %d", (int) current_count, load_tables);
            }
            res = WALLY_RESULT_NO_ERROR;
        }
    }

    /* set min_seq_auto_update */
    init_min_seq_auto_update(table);
    return res;
}

int64_t wally_get_total_max_seq_with_hash(struct wally *wally, uint64_t *ret_hash)
{
    int i;
    int64_t total = 0;
    uint32_t h1 = 0, h2 = 0;
    uint64_t hash = 0;
    int64_t max_seq_seen;

    for (i = 0; i < wally->tables_count; i++) {
        max_seq_seen = wally_table_max_sequence_seen(wally->tables[i]);
        total += max_seq_seen;
        lookup3_hashlittle2(&max_seq_seen, sizeof(max_seq_seen), &h1, &h2);
    }

    hash = h1 + (((uint64_t)h2)<<32);
    *ret_hash = hash;

    return total;
}

int64_t wally_get_last_remote_sync_time(struct wally *wally, struct argo_hash_table *skip_table)
{
    int i;
    int64_t max_last_remote_sync_time = 0;
    int64_t last_remote_sync_time;

    if (wally == NULL) {
        return 0;
    }

    for (i = 0; i < wally->tables_count; i++) {
        if (skip_table) {
            if (argo_hash_lookup(skip_table, wally->tables[i]->name, strlen(wally->tables[i]->name), NULL)) {
                continue;
            }
        }
        last_remote_sync_time = wally_table_get_remote_update_time(wally->tables[i]);
        if (last_remote_sync_time > max_last_remote_sync_time) {
            max_last_remote_sync_time = last_remote_sync_time;
        }
    }

    return max_last_remote_sync_time;
}

void wally_ut_set_last_remote_sync_time(struct wally_table *table, int64_t time)
{
    if (table) {
        table->last_remote_update_time_s = time;
    }
}
/******************* Common Wally Recovery Changes *********************************/
/* Function : wally_retrieve_unsupported_clients
 * Arg      : wally - wally object
 *            buffer - buffer to be filled
 *            bufsize - total buffer size
 * ret      : WALLY_RESULT
 * Desc     : This function iterates the fohh servers and retrieve clients to list
 *              unsupported clients.
 */
int wally_retrieve_unsupported_clients(struct wally *wally, char *buffer, size_t bufsize)
{
    int res = WALLY_RESULT_NO_ERROR;
    int64_t start = 0;
    for (int s_index = 0; s_index < wally->num_fohh_servers; s_index++) {
        res = wally_fohh_retrieve_unsupported_clients(wally->wally_fohh_server_handle[s_index], buffer,
                &start, bufsize);
        if (res != WALLY_RESULT_NO_ERROR) {
            break;
        }
    }
    return res;
}

/* Function : wally_recovery_state_machine
 * Arg      : table - wally table
 * Ret      : None
 * Desc     : This function runs state machine from wally timeout thread
 */
void  wally_recovery_state_machine(struct wally_table *table)
{
    int64_t recovery_elapsed_time;
    if (table->recovery_state.state == recovery_state_noactive)
        return;

    recovery_elapsed_time = epoch_us() - table->recovery_state.start_time;
    if (recovery_elapsed_time > table->recovery_state.timeout)
    {
        WALLY_LOG(AL_ERROR, "State_Machine:%s Recovery timedout and cleaning up",
                   wally_recovery_state_string(table->recovery_state.state));
        wally_table_recovery_state_update(table, recovery_state_timeout);
        wally_reset_recovery_mode(table, RECOVERY_RESET_STATE);
    }

    switch (table->recovery_state.state)
    {
        case recovery_state_rowsyncstart:
        {
            if (table->recovery_state.end_request_received == 1)
            {
                /* Wally till all the rows read by wally layer */
                if (table->recovery_state.received_rows == table->recovery_state.server_sent_rows)
                {
                    wally_table_recovery_state_update(table, recovery_state_rowsyncend);
                    table->recovery_state.status = recovery_status_complete;
                } else {
                    /* Incomplete self recovery */
                    table->recovery_state.status = recovery_status_incomplete;
                }
                /* else wait for rows to be read by wally layer, if rows missed timeout is reported */
            }
            break;
        }
        case recovery_state_rowsyncend:
        {
            /* Even if response callback is called earlier, wait for all rows to be sent to clients */
            if (__atomic_load_n(&(table->recovery_state.pending_send_rows), __ATOMIC_RELAXED) == 0)
            {
                wally_table_recovery_state_update(table, recovery_state_end);
            }
            break;
        }
        case recovery_state_end:
        {
            if (table->recovery_state.pending_responses == 0)
            {
                wally_table_recovery_state_update(table, recovery_state_complete);
            }
        }
        default:
            break;
    }
}

/* Function : wally_recovery_update_stats
 * Arg      : table - wally_table
 * Ret      : None
 * Desc     : This function copies stats for reporting purposes
 */
void wally_table_recovery_state_update(struct wally_table *table, enum recovery_state_t recovery_state)
{
    switch(recovery_state)
    {
        case recovery_state_begin:
        {
            int64_t requests = 0;
            /* Only latest start time, sequence  is updated if multiple recovery initiated in single
             * publish interval
             */
            if (oper_mode_g.is_active_done && oper_mode_g.init_done) {
                wally_oper_event_post(E_RECOVERY, SE_RECOVERY_START, NULL);
            }
            table->recovery_state.state = recovery_state_begin;
            table->recovery_state.start_time = epoch_us();
            table->recovery_state.recovery_requests++;
            table->recovery_state.prev_max_sequence = table->max_sequence_seen;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            table->recovery_stats.state = table->recovery_state.state;
            table->recovery_stats.start_time = table->recovery_state.start_time;
            table->recovery_stats.recovery_requests += table->recovery_state.recovery_requests;
            table->recovery_stats.sequence = table->recovery_state.sequence;
            table->recovery_stats.prev_sequence = table->recovery_state.prev_max_sequence;
            requests = wally_send_recovery_begin_requests(table, table->name, table->recovery_state.sequence, table->recovery_state.timeout,
                                table->recovery_state.sync_missing_rows);
            table->recovery_state.begin_requests += requests;
            table->recovery_state.clients+= requests;
            table->recovery_stats.begin_requests += table->recovery_state.begin_requests;
            table->recovery_stats.clients += table->recovery_state.clients;
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            break;
        }
        case recovery_state_rowsyncstart:
        {
            table->recovery_state.state = recovery_state_rowsyncstart;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            table->recovery_stats.state = table->recovery_state.state;
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            break;
        }
        case recovery_state_rowsyncend:
        {
            table->recovery_state.state = recovery_state_rowsyncend;
            table->recovery_stats.state = table->recovery_state.state;
            table->recovery_stats.received_rows += table->recovery_state.received_rows;
            table->recovery_stats.recovered_rows += table->recovery_state.recovered_rows;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            break;
        }
        case recovery_state_end:
        {
            char *table_name = table->name;
            int64_t received_rows = table->recovery_state.received_rows;
            int total_requests = 0;
            /* UPDATE: */
            table->recovery_state.endreq_sent_time = epoch_us();
            table->recovery_state.state = recovery_state_end;
            table->recovery_stats.state = table->recovery_state.state;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            table->recovery_stats.status = table->recovery_state.status;

            /* ACTION: */
            /* if wally_initiate_recovery_end_requests does not have clients,
             * complete is called and no action required here
             */
            total_requests = wally_initiate_recovery_end_requests(table, table_name, received_rows, 0);
            if (total_requests != 0) {
                table->recovery_state.end_requests += total_requests;
                if (total_requests) {
                    __sync_add_and_fetch_8(&(table->recovery_state.pending_responses), total_requests);
                }
                memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            }
            table->recovery_stats.end_requests += table->recovery_state.end_requests;
            break;
        }
        case recovery_state_timeout:
        {
            int prev_state = table->recovery_state.state;
            if (oper_mode_g.is_active_done && oper_mode_g.init_done) {
                wally_oper_event_post(E_RECOVERY, SE_RECOVERY_TIMEOUT, NULL);
            }
            table->recovery_state.state = recovery_state_timeout;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            table->recovery_state.total_timeout++;
            table->recovery_stats.state = table->recovery_state.state;
            table->recovery_stats.complete_responses += table->recovery_state.complete_responses;
            table->recovery_stats.duration += table->recovery_state.duration;
            table->recovery_stats.incomplete_reports += table->recovery_state.incomplete_reports;
            table->recovery_stats.received_rows += table->recovery_state.received_rows;
            table->recovery_stats.recovered_rows += table->recovery_state.recovered_rows;
            table->recovery_stats.total_timeout += table->recovery_state.total_timeout;
            table->recovery_state.failures++;
            table->recovery_stats.failures += table->recovery_state.failures;

            /* ACTION: */
            if ((wally_is_wallyd(table->wally) && (table->wally->origins_count == 1))) {
                int stop_db_polling = 0;
                wally_xfer_callout_cmd_trigger_f *cmd_trigger_callback = NULL;
                cmd_trigger_callback = table->wally->origins[ORIGIN_WALLY_DB_INDEX]->cmd_trigger;
                if (cmd_trigger_callback) {
                    cmd_trigger_callback(table->wally->origins[ORIGIN_WALLY_DB_INDEX]->callout_handle, table->name, WALLY_SET_DB_TABLE_POLL, &stop_db_polling);
                }
            } else {
                if (prev_state == recovery_state_end) {
                    wally_fohh_send_recovery_complete_response(
                            table->wally->origins[table->wally->origins_count-1]->callout_handle,
                            table->name, table->recovery_state.received_rows);
                }
            }
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            wally_reset_recovery_mode(table, RECOVERY_RESET_STATE);
            break;
        }
        case recovery_state_complete:
        {
            if (oper_mode_g.is_active_done && oper_mode_g.init_done) {
                wally_oper_event_post(E_RECOVERY, SE_RECOVERY_CMPLT, NULL);
            }
            table->recovery_state.state = recovery_state_complete;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            table->recovery_stats.state = table->recovery_state.state;
            table->recovery_stats.complete_responses += table->recovery_state.complete_responses;
            table->recovery_stats.duration += table->recovery_state.duration;
            table->recovery_stats.incomplete_reports += table->recovery_state.incomplete_reports;

            /* ACTION: */
            if ((wally_is_wallyd(table->wally) && (table->wally->origins_count == 1))) {
                int stop_db_polling = 0;
                wally_xfer_callout_cmd_trigger_f *cmd_trigger_callback = NULL;
                cmd_trigger_callback = table->wally->origins[ORIGIN_WALLY_DB_INDEX]->cmd_trigger;
                if (cmd_trigger_callback) {
                    cmd_trigger_callback(table->wally->origins[ORIGIN_WALLY_DB_INDEX]->callout_handle, table->name, WALLY_SET_DB_TABLE_POLL, &stop_db_polling);
                }
            } else {
                wally_fohh_send_recovery_complete_response(
                        table->wally->origins[table->wally->origins_count-1]->callout_handle,
                        table->name, table->recovery_state.received_rows);
            }
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            wally_reset_recovery_mode(table, RECOVERY_RESET_STATE);
            break;
        }
        case recovery_state_failure:
        {
            if (oper_mode_g.is_active_done && oper_mode_g.init_done) {
                wally_oper_event_post(E_RECOVERY, SE_RECOVERY_TIMEOUT, NULL);
            }
            if ((wally_is_wallyd(table->wally) && (table->wally->origins_count == 1))) {
                int stop_db_polling = 0;
                wally_xfer_callout_cmd_trigger_f *cmd_trigger_callback = NULL;
                cmd_trigger_callback = table->wally->origins[ORIGIN_WALLY_DB_INDEX]->cmd_trigger;
                if (cmd_trigger_callback) {
                    cmd_trigger_callback(table->wally->origins[ORIGIN_WALLY_DB_INDEX]->callout_handle, table->name, WALLY_SET_DB_TABLE_POLL, &stop_db_polling);
                }
            }
            table->recovery_state.state = recovery_state_failure;
            table->recovery_stats.state = table->recovery_state.state;
            if (table->recovery_state.start_time != 0) {
                table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
                table->recovery_stats.duration += table->recovery_state.duration;
            }
            table->recovery_state.failures++;
            table->recovery_stats.failures += table->recovery_state.failures;
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            wally_reset_recovery_mode(table, RECOVERY_RESET_STATE);
            break;
        }
        case recovery_state_stopped:
        {
            int prev_state = table->recovery_state.state;
            int total_requests = 0;
            char *table_name = table->name;
            int64_t received_rows = table->recovery_state.received_rows;

            table->recovery_state.state = recovery_state_stopped;
            table->recovery_state.duration = (epoch_us() - table->recovery_state.start_time);
            table->recovery_state.failures++;
            table->recovery_stats.failures += table->recovery_state.failures;
            table->recovery_stats.duration += table->recovery_state.duration;
            if ((wally_is_wallyd(table->wally) && (table->wally->origins_count == 1))) {
                int stop_db_polling = 0;
                wally_xfer_callout_cmd_trigger_f *cmd_trigger_callback = NULL;
                cmd_trigger_callback = table->wally->origins[ORIGIN_WALLY_DB_INDEX]->cmd_trigger;
                if (cmd_trigger_callback) {
                    cmd_trigger_callback(table->wally->origins[ORIGIN_WALLY_DB_INDEX]->callout_handle, table->name, WALLY_SET_DB_TABLE_POLL, &stop_db_polling);
                }
            }
            memcpy(&(table->prev_recovery_state), &(table->recovery_state), sizeof(table->prev_recovery_state));
            if (prev_state < recovery_state_end) {
                /* Best effort to send end requests to clients for cleanup */
                total_requests = wally_initiate_recovery_end_requests(table, table_name, received_rows, 1);
                table->recovery_state.end_requests += total_requests;
                /* Above send end request changes state to end and change it back to stopped */
                table->recovery_stats.state = recovery_state_stopped;
                table->recovery_stats.end_requests += table->recovery_state.end_requests;
            }
            wally_reset_recovery_mode(table, RECOVERY_RESET_STATE);
            break;
        }
        default:
            break;
    }
    return;
}

/* Function : wally_reset_recovery_mode
 * Arg      : table - wally_table
 *            mode = RESET_STATE, RESET_STATS, RESET_ALL
 * Ret      : None
 * Desc     : This function resets recovery parameters based on mode
 */
void wally_reset_recovery_mode(struct wally_table *table, uint8_t mode)
{
    if (mode & RECOVERY_RESET_STATE)
    {
        memset(&(table->recovery_state), 0, sizeof(table->recovery_state));
    }
    if (mode & RECOVERY_RESET_STATS)
    {
        memset(&(table->recovery_stats), 0, sizeof(table->recovery_stats));
    }
}

/* Function: wally_table_server_handle_complete_response
 * Arg     : table_name        - Name of the table to be recovered
 *         : server_sent_rows  - number of rows sent by server
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function forwards the end requests to clients, otherwise sends response
 *              back to server.
 */
int wally_table_server_handle_complete_response(void *cookie, char *table_name, int64_t received_row_count)
{
    int res = WALLY_RESULT_NO_ERROR;
    struct wally_table *table = NULL;
    struct wally *wally = (struct wally *) cookie;

    /* Parameter validation */
    if ((table = wally_table_get(wally, table_name)) == NULL)
    {
        WALLY_LOG(AL_ERROR, "Complete_Response_Receiver table not found for %s", table_name);
        return WALLY_RESULT_NO_ERROR;
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    if (table->recovery_state.state != recovery_state_end) {
        WALLY_LOG(AL_ERROR, "Complete_Response_Receiver: state %s is not recovery_state_end",
                   wally_recovery_state_string(table->recovery_state.state));
        /* Ignore this */
        ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        return res;
    }
    if (received_row_count != table->recovery_state.received_rows)
    {
        table->recovery_state.incomplete_reports++;
    }

    if (__atomic_load_n(&(table->recovery_state.pending_responses), __ATOMIC_RELAXED) > 0 ) {
        __sync_sub_and_fetch_8(&(table->recovery_state.pending_responses), 1);
    }
    table->recovery_state.complete_responses++;
    WALLY_DEBUG_RECOVERY("Complete_Response_Receiver:  Pending responses = %"PRId64" "
            "conplete_responses = %"PRId64" ",
            __atomic_load_n(&(table->recovery_state.pending_responses), __ATOMIC_RELAXED), table->recovery_state.complete_responses);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return res;
}

/* Function : wally_send_recovered_rows_to_all_clients
 * Arg      : table - wally table
 *          : row - wally row object
 * Ret      : WALLY_RESULT_NO_ERROR if no error.
 * Desc     : This function iterates all the clients and sends row objects to them.
 */
int wally_send_recovered_rows_to_all_clients(struct wally_table *table, struct wally_row *row)
{
    int64_t total_requests = 0, server_requests = 0;
    argo_object_hold(row->current_row);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    for (int s_index = 0; s_index < table->wally->num_fohh_servers; s_index++) {
        server_requests = wally_fohh_server_send_row_to_all_clients(table->wally->wally_fohh_server_handle[s_index], table,
                        row->current_row);
        if ( server_requests < 0) {
            continue;
        } else {
            total_requests += server_requests;
        }
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    argo_object_release(row->current_row);
    __sync_fetch_and_add_8(&(table->recovery_state.pending_send_rows),total_requests);
    return WALLY_RESULT_NO_ERROR;
}

/* Function : wally_send_recovery_begin_requests
 * Arg      : table - wally table object
 *            recovery_sequence - recovery sequence
 *            recovery_timeout - recovery timeout
 *            sync_missing_rows - sync only missing rows
 * Ret      : WALLY_RESULT_NO_ERROR if success.
 * Desc     : Wally iterates server object and sends begin request all connected clients with recovery sequence,
 *             timeout and sync_missing_rows flag.
 */
int wally_send_recovery_begin_requests(struct wally_table *table, char *table_name, int64_t recovery_sequence, int64_t recovery_timeout, int sync_missing_rows )
{
    int total_requests = 0, server_requests = 0;
    struct wally *wally = table->wally;

    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    for (int s_index = 0; s_index < wally->num_fohh_servers; s_index++) {
        server_requests = wally_fohh_server_send_recovery_begin_request(wally->wally_fohh_server_handle[s_index], table_name,
                            recovery_sequence, recovery_timeout, sync_missing_rows);
        if (server_requests < 0 ) {
            continue;
        } else {
            total_requests += server_requests;
        }
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    return total_requests;
}
/* Function : wally_send_recovery_end_requests
 * Arg      : table - wally table
 * Ret      : Number of requests sent to client
 * Desc     : This function iterates all the clients and sends end requests and
 *                starts end request timer.
 */
int wally_send_recovery_end_requests(struct wally_table *table, char* table_name, int64_t received_rows)
{
    int server_requests = 0, total_requests = 0;
    struct wally *wally = table->wally;
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    for (int s_index = 0; s_index < wally->num_fohh_servers; s_index++) {
        server_requests = wally_fohh_server_send_recovery_end_request(wally->wally_fohh_server_handle[s_index],
                                table_name, received_rows);
        if (server_requests < 0 ) {
            continue;
        } else {
            total_requests += server_requests;
        }
    }
    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    return total_requests;
}
/***********************************************************************************/

/******************* Wally Client Recovery Changes *********************************/
/* Function: wally_table_recovery_client_validation
 * Arg     : wally             - Wally object for which recovery command is executed
 *         : table             - Name of the table to be recovered
 * Ret     : ERR returned incase of errors, otherwise success.
 * Desc    : check app state and return error.
 */
int wally_table_recovery_client_validation(struct wally *wally, struct wally_table *table, int64_t recovery_sequence)
{
    if (table->recovery_state.state != recovery_state_noactive) {
        return WALLY_RESULT_ERR;
    }

    /* Recovery sequence is greater than current max sequence seen by the table */
    if (table->max_sequence_seen < recovery_sequence)
    {
        WALLY_LOG(AL_ERROR, "Begin_Request_Validation: Table seen less max sequence(%"PRId64") than recovery sequence(%"PRId64") "
                        "Ignoring this recovery for current client.",
                            table->max_sequence_seen, recovery_sequence);
    }
    /* No other error cases as of now for wally clients */
    return WALLY_RESULT_NO_ERROR;
}


/* Function: wally_table_client_recovery_begin_request_handler
 * Arg     : wally             - Wally object for which recovery command is executed
 *         : table             - Name of the table to be recovered
 *         : recovery_sequence - sequence from which rows to be recovered
 *         : sync_missing_rows - 1 if only missing rows to be recovered, otherwise 0.
 *         : recovery_timeout  - recovery timeout in seconds
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function validates wally internal state before starting recovery process.
 *           Internal state is updated, begin message forwarded to clients if present,
 *           start waiting for rows from remote wally
 */
int wally_table_client_recovery_begin_request_handler(void *cookie, char *table_name, int64_t recovery_sequence,
                            int64_t recovery_timeout, int sync_missing_rows)
{
    int res = WALLY_RESULT_NO_ERROR;
    struct wally_table *table = NULL;
    struct wally_origin *origin = (struct wally_origin *) cookie;
    struct wally *wally = origin->wally;

    /* Parameter validation */
    if ((table = wally_table_get(wally, table_name)) == NULL)
    {
        WALLY_LOG(AL_ERROR, "Begin_Request_Handler: table not found for %s", table_name);
        return WALLY_RESULT_NO_ERROR;
    }

    WALLY_DEBUG_RECOVERY("Begin_Request_Handler: Received end request for  %s recovery_sequence = %"PRId64"  "
                    "recover_timeout = %"PRId64" sync_missing_rows = %d",
            table_name, recovery_sequence, recovery_timeout, sync_missing_rows);

    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);

    /* Recovery validations */
    if ( (res = wally_table_recovery_client_validation(table->wally, table, recovery_sequence)) != WALLY_RESULT_NO_ERROR)
    {
        WALLY_LOG(AL_ERROR, "Begin_Request_Handler: recovery validation failed %s",
                wally_result_string(res));
        /* Ignore this */
        ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        return res;
    }

    table->recovery_state.sync_missing_rows = sync_missing_rows;
    table->recovery_state.timeout = recovery_timeout;
    table->recovery_state.sequence = recovery_sequence;
    wally_table_recovery_state_update(table, recovery_state_begin);
    /* If multiple index inconsistency feature enabled + table is not fully loaded +
     *  origins are both DB and fohh, then enqueue write operation to update
     *  recovery sequence in lookup table.
     */
    if ((wally_multiple_index_consistency_support) &&
            (!table->fully_loaded) && (wally->origins_count > 1) ) {
        wally_origin_enqueue_write_recovery_update(table, recovery_sequence, 0);
    }

    wally_table_recovery_state_update(table, recovery_state_rowsyncstart);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return WALLY_RESULT_NO_ERROR;
}

/* Function : wally_forward_recovery_end_requests
 * Arg      : table - wally table
 * Ret      : Number of requests sent to client
 * Desc     : This function iterates all the clients and sends end requests
 */
int wally_initiate_recovery_end_requests(struct wally_table *table, char *table_name, int64_t received_rows, int stop_call)
{
    int total_requests = 0;

    total_requests = wally_send_recovery_end_requests(table, table_name, received_rows);
    if ((total_requests == 0) && (stop_call==0)) {
        WALLY_DEBUG_RECOVERY("End_Reqeust_Sender: Sending end request %s",
                table->name);
        /* No change to state as pending_responses will checked by state machine */
        wally_table_recovery_state_update(table, recovery_state_complete);
    }
    return total_requests;
}


/* Function: wally_table_client_recovery_end_request_handler
 * Arg     : table_name        - Name of the table to be recovered
 *         : server_sent_rows  - number of rows sent by server
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function forwards the end requests to clients, otherwise sends response
 *              back to server.
 */
int wally_table_client_recovery_end_request_handler(void *cookie, char *table_name, int64_t server_sent_rows)
{
    int res = WALLY_RESULT_NO_ERROR;
    struct wally_table *table = NULL;
    struct wally_origin *origin = (struct wally_origin *) cookie;
    struct wally *wally = origin->wally;

    /* Parameter validation */
    if ((table = wally_table_get(wally, table_name)) == NULL)
    {
        WALLY_LOG(AL_ERROR, "End_Request_Handler: table not found for %s", table_name);
        return WALLY_RESULT_NO_ERROR;
    }
    WALLY_DEBUG_RECOVERY("End_Reqeust_Handler: Received end request for  %s server_sent_rows = %"PRId64" ",
            table_name, server_sent_rows);

    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    if (table->recovery_state.state != recovery_state_rowsyncstart) {
        WALLY_LOG(AL_ERROR, "End_Request_Handler: state %s is not recovery_state_rowsyncstart ",
                   wally_recovery_state_string(table->recovery_state.state));
        /* Ignore this */
        ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        return res;
    }
    table->recovery_state.server_sent_rows = server_sent_rows;
    table->recovery_state.end_request_received = 1;
    WALLY_DEBUG_RECOVERY("End_Request_Handler: Number of pending rows in queue = %"PRId64" ",
            __atomic_load_n(&(table->recovery_state.pending_send_rows), __ATOMIC_RELAXED));
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return res;
}
/***********************************************************************************/

/******************* Origin Wally Recovery Changes *********************************/
/* Function : wally_recovery_response_callback
 * Arg      : cookie - wally object
 *            registrant - default registrant
 *            table - wally table
 *            sequence - current max sequence
 *            status - completed
 * Ret      : WALLY_RESULT_NO_ERROR if success
 * Desc     : This function is called after all rows are read from DB but does not ensure rows
 *              are sent to clients.
 */
static int wally_recovery_response_callback(void *cookie, struct wally_registrant *registrant,
                                        struct wally_table *table, int64_t sequence,
                                        int status)
{
    int res = WALLY_RESULT_NO_ERROR;
    /* table lock is already acquired by process registrants */
    if (table->recovery_state.state != recovery_state_rowsyncstart) {
        WALLY_LOG(AL_ERROR, "Db_Row_Response_Callback state %s is not recovery_state_rowsyncstart ",
                   wally_recovery_state_string(table->recovery_state.state));
        wally_table_recovery_state_update(table, recovery_state_failure);
        return res;
    }
    if (table->recovery_state.stop_processing_rows == 1)
    {
        wally_table_recovery_state_update(table, recovery_state_stopped);
        return res;
    }

    /* if more than 0 rows received, then recovery considered success in origin wally */
    if (table->recovery_state.received_rows > 0) {
        table->recovery_state.status = recovery_status_complete;
    } else {
        table->recovery_state.status = recovery_status_incomplete;
    }
    WALLY_DEBUG_RECOVERY("DB_Row_Response_Callback: Status is complete = %d",
                     table->recovery_state.status);
    WALLY_DEBUG_RECOVERY("DB_Row_Response_Callback: Number of pending rows in queue = %"PRId64" ",
            __atomic_load_n(&(table->recovery_state.pending_send_rows), __ATOMIC_RELAXED));
    wally_table_recovery_state_update(table, recovery_state_rowsyncend);
    return res;
}

/* Function : wally_recovery_handle_stop_cmd_handler
 * Arg      : wally - wally object
 *            table_name - table name
 * Ret      : WALLY_RESULT_NO_ERROR if success
 * Desc     : This function tries to stop the recovery in the middle based on current
 *              state.
 */
int wally_recovery_handle_stop_cmd_handler(struct wally *wally,const char *table_name)
{
    struct wally_table *table = NULL;
    int res = WALLY_RESULT_NO_ERROR;

    table = wally_table_get(wally, table_name);
    if (!table) {
        WALLY_LOG(AL_ERROR, "Recover_Cmd_Handler: Table %s not found ", table_name);
        return WALLY_RESULT_ERR;
    }

    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    if ((table->recovery_state.state == recovery_state_rowsyncstart) ||
            (table->recovery_state.state == recovery_state_begin))
    {
        table->recovery_state.stop_processing_rows = 1;
    }
    else if (table->recovery_state.state != recovery_state_noactive)
    {
       wally_table_recovery_state_update(table, recovery_state_stopped);
    }

    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return res;
}

/* Function: wally_table_recovery_cmd_validation
 * Arg     : wally             - Wally object for which recovery command is executed
 *         : table             - Name of the table to be recovered
 * Ret     : ERR returned incase of errors, otherwise success.
 * Desc    : check app state, origin wally for server handling, fully loaded or null column enabled
 */
int wally_table_recovery_cmd_validation(char *buf, size_t size, struct wally *wally, struct wally_table *table)
{
    /* Recovery already in-progress */
    if (table->recovery_state.state != recovery_state_noactive) {
        WALLY_ZDP("Recovery_Cmd_Validation:%s Recovery is already in-progress.",
                            wally_recovery_state_string(table->recovery_state.state));
        return WALLY_RESULT_ERR;
    }
    /* Defect: wallyd state is set to tables_loaded instead of running due to E_ACTIVE event */
    if (!((get_wally_app_state() == wally_state_app_running) || (get_wally_app_state() == wally_state_tables_loaded)))
    {
        WALLY_ZDP("Recovery_Cmd_Validation:%s recovery is supported only when app is running",
                                wally_recovery_state_string(table->recovery_state.state));
        return WALLY_RESULT_ERR;
    }
    /* origin wallyd = wally_is_wallyd + (origins_count == 1) */
    if (!(wally_is_wallyd(wally) && (wally->origins_count ==1))) {
        WALLY_ZDP("Recovery_Cmd_Validation:%s recovery can be initiated only from origin wally",
                               wally_recovery_state_string(table->recovery_state.state));
        return WALLY_RESULT_ERR;
    }
    /* if table is not fully loaded, then recovery is not applicable */
    if (table->fully_loaded == false)
    {
        WALLY_ZDP("Recovery_Cmd_Validation:%s table %s is not fully_loaded",
                wally_recovery_state_string(table->recovery_state.state), table->name);
        return WALLY_RESULT_ERR;
    }
    if (oper_mode_g.origin_disconnect || oper_mode_g.rds_failure || oper_mode_g.poll_failure ||
                 oper_mode_g.offline)
    {
        WALLY_ZDP("Recovery_Cmd_Validation:%s RDS is disconnected. Recovery cannot be done.",
            wally_recovery_state_string(table->recovery_state.state));
        return WALLY_RESULT_ERR;
    }
    if (oper_mode_g.termination)
    {
        WALLY_ZDP("Recovery_Cmd_Validation:%s Wally termination in progress. Recovery cannot be done.",
            wally_recovery_state_string(table->recovery_state.state));
        return WALLY_RESULT_ERR;
    }
    return WALLY_RESULT_NO_ERROR;
}
/* Function: wally_table_recovery_cmd_handler
 * Arg     : wally             - Wally object for which recovery command is executed
 *         : table             - Name of the table to be recovered
 *         : recovery_sequence - sequence from which rows to be recovered
 *         : sync_missing_rows - 1 if only missing rows to be recovered, otherwise 0.
 *         : recovery_timeout  - recovery timeout in seconds
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function validates wally internal state before starting recovery process.
 *           Internal state is updated, begin message sent to clients,
 *           registered to read the rows and DB stopped polling
 */
int wally_table_recovery_cmd_handler(char *buf , size_t size,  struct wally *wally, const char *table_name, int64_t recovery_sequence,
                    int sync_missing_rows, int64_t recovery_timeout)
{
    wally_xfer_callout_cmd_trigger_f *cmd_trigger_callback = NULL;
    struct wally_index_column *null_column = NULL;
    struct wally_table *table = NULL;
    int wally_stop_db_polling = 0, res = WALLY_RESULT_NO_ERROR;
    void *wp_db;

    table = wally_table_get(wally, table_name);
    if (!table) {
        WALLY_ZDP("Recover_Cmd_Handler: Table %s not found ", table_name);
        return WALLY_RESULT_ERR;
    }

    ZPATH_RWLOCK_WRLOCK(&(table->lock), __FILE__, __LINE__);
    /* Recovery validations */
    if ( (res = wally_table_recovery_cmd_validation(buf, size, wally, table)) != WALLY_RESULT_NO_ERROR)
    {
        ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        return res;
    }
    /* If no null column, then recovery cannot be done */
    if ((null_column = get_null_column(table)) == NULL)
    {
        WALLY_ZDP("Recovery_Cmd_Handler:%s NULL column is not present",
                wally_recovery_state_string(table->recovery_state.state));
        ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
        return WALLY_RESULT_ERR;
    }

    table->recovery_state.sync_missing_rows = sync_missing_rows;
    table->recovery_state.timeout = recovery_timeout;
    table->recovery_state.sequence = recovery_sequence;
    wally_table_recovery_state_update(table, recovery_state_begin);

    /* Origin wally has DB as origin[0], Stop polling on the DB for the table */
    cmd_trigger_callback = wally->origins[ORIGIN_WALLY_DB_INDEX]->cmd_trigger;
    if (cmd_trigger_callback) {
        wp_db = wally->origins[ORIGIN_WALLY_DB_INDEX]->callout_handle;
        wally_stop_db_polling = 1;
        cmd_trigger_callback(wp_db, table->name, WALLY_SET_DB_TABLE_POLL, &wally_stop_db_polling);
    }

    /* Register for row - null column with recovery sequence number*/
    wally_table_recovery_state_update(table, recovery_state_rowsyncstart);
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    res = wally_table_register_for_row_recovery( NULL, /*registrant */
                                        null_column, /* column */
                                        NULL, /* key */
                                        0, /* key length */
                                        0, /* request id */
                                        recovery_sequence, /* request sequence */
                                        0, /* request_atleast_one */
                                        0, /* just callback */
                                        0, /* unique registration */
                                        wally_recovery_response_callback, /* response callback */
                                        table /* response callback cookie */
                                     );

    return res;
}
/***********************************************************************************/
