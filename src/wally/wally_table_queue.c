/*
 * wally_table_queue_handler.c Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * Wally table Queue  handler
 */

#include <sys/stat.h>
#include <event2/event.h>
#include <libpq-fe.h>
#include "zthread/zthread.h"
#include "zevent/zevent.h"
#include <math.h>
#include <libpq-fe.h>

#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_db.h"
#include "wally/wally_postgres.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_table_queue.h"
#include "wally/wally_table_queue_compiled.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_oper.h"

/*
 * Array of tables_hash, each bucket will store list of wally_table_queue_node
 */
struct wally_table_thread *wally_table_thread_inst = NULL;

struct zhash_table *table_thread_hash;
int thread_id = 1;

/* argo stats description for queue used by wally threads */
struct argo_structure_description *wally_table_queue_stats_description = NULL;

const char *wally_table_queue_msg_string[] = {
	[WALLY_MSG_DB_XFER_ROW]="WALLY_MSG_DB_XFER_ROW",
	[WALLY_MSG_DB_XFER_RESPONSE]="WALLY_MSG_DB_XFER_RESPONSE",
	[WALLY_MSG_FOHH_XFER_ROW]="WALLY_MSG_FOHH_XFER_ROW",
	[WALLY_MSG_FOHH_XFER_RESPONSE]="WALLY_MSG_FOHH_XFER_RESPONSE",
	[WALLY_MSG_DB_PAUSED_SEQ]="WALLY_MSG_DB_PAUSED_SEQ",
	[WALLY_MSG_DB_TABLE_EXISTS]="WALLY_MSG_DB_UPDATE_TABLE_EXISTS",
	[WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING]="WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING",
	[WALLY_MSG_FOHH_CLIENT_DEREGISTER_ROW_STRING]="WALLY_MSG_FOHH_CLIENT_DEREGISTER_ROW_STRING",
	[WALLY_MSG_DO_WTS]="WALLY_MSG_DO_WTS",
    [WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ]="WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ",
    [WALLY_MSG_FOHH_END_RECOVERY_REQ]="WALLY_MSG_FOHH_END_RECOVERY_REQ",
    [WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP]="WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP",
	[WALLY_MSG_MAX]="WALLY_MSG_MAX",
};

/*
 * This function is called to update thread level stats
 */
void wally_table_thread_update_stats(struct wally_table_queue_node *q, int64_t thread_id, bool enqueue)
{
	struct wally_table_thread *t = &(wally_table_thread_inst[thread_id]);
	int64_t avg_wait_time_us = 0;

	if (enqueue) {
		__sync_add_and_fetch_8(&(t->total_message_pending), 1);
		__sync_add_and_fetch_8(&(t->message_pending[q->msg_type]), 1);
		if (thread_id) {
			q->wait_start_time_slave_us = epoch_us();
		} else {
			//Main thread stats
			q->wait_start_time_main_us = epoch_us();
		}
		return;

	} else {
		if (thread_id) {
			avg_wait_time_us = (epoch_us() - q->wait_start_time_slave_us);
		} else {
			avg_wait_time_us = (epoch_us() - q->wait_start_time_main_us);
		}
		__sync_add_and_fetch_8(&(t->total_message_processed), 1);
		__sync_add_and_fetch_8(&(t->message_processed[q->msg_type]), 1);
		if (t->total_message_processed) {
			t->avg_wait_time_us = t->avg_wait_time_us + (avg_wait_time_us - t->avg_wait_time_us)/t->total_message_processed;
		}
		if (avg_wait_time_us > t->max_wait_time_us)
			t->max_wait_time_us = avg_wait_time_us;
		if (avg_wait_time_us < t->min_wait_time_us)
			t->min_wait_time_us = avg_wait_time_us;
		__sync_sub_and_fetch_8(&(t->total_message_pending), 1);
		__sync_sub_and_fetch_8(&(t->message_pending[q->msg_type]), 1);
	}
}


/* Common enqueue function for all msg types */
void wally_table_queue_enqueue_node_internal(struct wally_table_queue_node *q)
{
	wally_table_thread_update_stats(q, 0, true);
	if (zevent_base_call(wally_table_thread_inst[0].zevent, wally_table_thread_load_distributor, q, 0) != 0) {
		WALLY_LOG(AL_ERROR, "Enqueing failed %d", q->msg_type);
		wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
	}
}

/* Enqueue row objects to wally table queue */
int wally_table_queue_enqueue_row_objects(void *cmd_cookie, char *argo_table_name,
		struct argo_object **objects, int64_t *indexes, int object_count)
{

	struct wally_table_queue_node *q;
	int object_size = wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_DB_XFER_ROW;
	q->msg.xfer_row.cmd_cookie = cmd_cookie;
	q->msg.xfer_row.object_count = object_count;
	q->msg.xfer_row.argo_table_name = argo_table_name;
	if (object_count)
	{
		if (objects)
		{
			q->msg.xfer_row.objects = (struct argo_object **)WALLY_CALLOC(
										sizeof(struct argo_object *) * object_size);
			if (q->msg.xfer_row.objects == NULL)
			{
				WALLY_LOG(AL_ERROR, "%s argo objects malloc failed", argo_table_name);
				WALLY_FREE(q);
				return WALLY_RESULT_NO_MEMORY;
			}
			memcpy(q->msg.xfer_row.objects, objects, sizeof(struct argo_object *) * object_size);
		}
		if (indexes)
		{
			q->msg.xfer_row.indexes = (int64_t *)WALLY_MALLOC(sizeof(int64_t) * object_size);
			if (q->msg.xfer_row.indexes == NULL)
			{
				WALLY_LOG(AL_ERROR, "%s argo indexes malloc failed", argo_table_name);
				WALLY_FREE(q->msg.xfer_row.objects);
				WALLY_FREE(q);
				return WALLY_RESULT_NO_MEMORY;
			}
			memcpy(q->msg.xfer_row.indexes, indexes, sizeof(int64_t) * object_size);
		}

	}

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE("%s:%s is successfully enqueued object_count = %d",
			wally_table_queue_msg_string[q->msg_type], argo_table_name, object_count);

	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue xfer response to wally table queue */
int wally_table_queue_enqueue_xfer_response(void *cmd_cookie, int64_t cmd_id,
											int64_t cmd_row_count, bool table_exists,
											char *argo_table_name)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_DB_XFER_RESPONSE;
	q->msg.xfer_resp.cmd_cookie = cmd_cookie;
	q->msg.xfer_resp.cmd_row_count = cmd_row_count;
	q->msg.xfer_resp.cmd_id = cmd_id;
	q->msg.xfer_resp.table_exists = table_exists;
	q->msg.xfer_resp.argo_table_name = WALLY_STRDUP(argo_table_name, strlen(argo_table_name));

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s:%s is successfully enqueued request_id = %"PRId64" cmd_row_count = %"PRId64,
			wally_table_queue_msg_string[q->msg_type], argo_table_name, cmd_id, cmd_row_count);
	return WALLY_RESULT_NO_ERROR;

}

/* Enqueue FOHH xfer row request recevied from client to wally table queue */
int wally_table_queue_enqueue_fohh_xfer_row(void *cmd_cookie,
											struct argo_object *row)
{
	struct wally_table_queue_node *q;
	struct argo_object *copy;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));

	copy = argo_object_copy(row);

	q->msg_type = WALLY_MSG_FOHH_XFER_ROW;
	q->msg.fohh_xfer_row.cmd_cookie = cmd_cookie;
	q->msg.fohh_xfer_row.object = copy;
	q->msg.fohh_xfer_row.argo_table_name = argo_object_get_type(copy);
	q->msg.fohh_xfer_row.cmd_id = argo_object_read_request_id(copy);;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s:%s is successfully enqueued cmd_id = %"PRId64,
			wally_table_queue_msg_string[q->msg_type], q->msg.fohh_xfer_row.argo_table_name,
			q->msg.fohh_xfer_row.cmd_id);

	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue FOHH xfer response received from clients to wally table queue */
int wally_table_queue_enqueue_fohh_xfer_response(void *cmd_cookie,
												int64_t cmd_id,
												int64_t cmd_row_count,
												bool table_exists,
												char *argo_table_name)
{
	struct wally_table_queue_node *q;

	if (!argo_table_name) {
		WALLY_LOG(AL_NOTICE, "Table name is NULL in FOHH Xfer Response recevied from remote wally(origin/global)");
		WALLY_LOG(AL_NOTICE, "Please upgrade origin & global wally to same version or higher version");
		wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
		return WALLY_RESULT_ERR;
	}

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;


	q->msg_type = WALLY_MSG_FOHH_XFER_RESPONSE;
	q->msg.fohh_xfer_resp.cmd_cookie = cmd_cookie;
	q->msg.fohh_xfer_resp.cmd_row_count = cmd_row_count;
	q->msg.fohh_xfer_resp.cmd_id = cmd_id;
	q->msg.fohh_xfer_resp.table_exists = table_exists;
	q->msg.fohh_xfer_resp.argo_table_name = WALLY_STRDUP(argo_table_name, strlen(argo_table_name));

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s:%s is successfully enqueued request_id = %"PRId64", cmd_row_count = %"PRId64,
			wally_table_queue_msg_string[q->msg_type], argo_table_name, cmd_id, cmd_row_count);

	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue wally paused sequence update from DB to wally table queue */
int wally_table_queue_enqueue_db_paused_seq(void *cmd_cookie,
											char *argo_table_name)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_DB_PAUSED_SEQ;
	q->msg.paused_seq.cmd_cookie = cmd_cookie;
	q->msg.paused_seq.argo_table_name = argo_table_name;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE("%s:%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type],
			argo_table_name);

	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue update table exists msg from db to wally table queue */
int wally_table_queue_enqueue_db_table_exists(void *cmd_cookie,
											  char *argo_table_name,
											  bool table_exists)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_DB_TABLE_EXISTS;
	q->msg.table_exists.cmd_cookie = cmd_cookie;
	q->msg.table_exists.argo_table_name = argo_table_name;
	q->msg.table_exists.table_exists = table_exists;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE("%s:%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type], argo_table_name);

	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue client register row request to wally table queue */
int wally_table_queue_enqueue_fohh_client_register_row_string(void *cookie,
															  void *structure_cookie,
															  struct argo_object *object)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING;
	q->msg.register_row_string.cookie = cookie;
	q->msg.register_row_string.structure_cookie = structure_cookie;
	q->msg.register_row_string.object = argo_object_copy(object);

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type]);
	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue client deregister row request to wally table queue */
int wally_table_queue_enqueue_fohh_client_deregister_row_string(void *cookie,
															  void *structure_cookie,
															  struct argo_object *object)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_FOHH_CLIENT_DEREGISTER_ROW_STRING;
	q->msg.deregister_row_string.cookie = cookie;
	q->msg.deregister_row_string.structure_cookie = structure_cookie;
	q->msg.deregister_row_string.object = argo_object_copy(object);

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type]);
	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue do wts request to wally table queue */
int wally_table_queue_enqueue_do_wts(struct wt *wt,
									struct wally_origin *wallyd_slave_db,
									struct wally_origin *wallyd_remote_db,
									struct wally *wallyd,
									int fully_loaded,
									struct table_cleanup_parameter cleanup_param ,
									int load_tables,
									wally_row_fixup_f *fixup_f,
									int is_gwally,
									wallyd_zpath_table_register_cb_f *zptr_f)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_DO_WTS;
	q->msg.do_wts.wt = wt;
	q->msg.do_wts.wallyd = wallyd;
	q->msg.do_wts.wallyd_remote_db = wallyd_remote_db;
	q->msg.do_wts.wallyd_slave_db = wallyd_slave_db;
	q->msg.do_wts.fully_loaded = fully_loaded;
	q->msg.do_wts.cleanup_param = cleanup_param;
	q->msg.do_wts.load_tables = load_tables;
	q->msg.do_wts.fixup_f = fixup_f;
	q->msg.do_wts.is_gwally = is_gwally;
	q->msg.do_wts.zptr_f = zptr_f;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE("%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type]);

	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue recovery begin request to wally table queue */
int wally_table_queue_enqueue_fohh_recovery_begin_req(void *cmd_cookie,
															  char *table_name, int64_t recovery_sequence,
                                                              int64_t recovery_timeout, int sync_missing_rows)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ;
	q->msg.fohh_begin_recovery_req.cmd_cookie = cmd_cookie;
	q->msg.fohh_begin_recovery_req.table_name = WALLY_STRDUP(table_name, strlen(table_name));
	q->msg.fohh_begin_recovery_req.recovery_sequence = recovery_sequence;
	q->msg.fohh_begin_recovery_req.recovery_timeout = recovery_timeout;
	q->msg.fohh_begin_recovery_req.sync_missing_rows = sync_missing_rows;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type]);
	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue recovery end request to wally table queue */
int wally_table_queue_enqueue_fohh_recovery_end_req(void *cmd_cookie,
											char *table_name, int64_t server_sent_rows)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_FOHH_END_RECOVERY_REQ;
	q->msg.fohh_end_recovery_req.cmd_cookie = cmd_cookie;
	q->msg.fohh_end_recovery_req.table_name = WALLY_STRDUP(table_name, strlen(table_name));
	q->msg.fohh_end_recovery_req.server_sent_rows = server_sent_rows;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type]);
	return WALLY_RESULT_NO_ERROR;
}

/* Enqueue recovery complete resp to wally table queue */
int wally_table_queue_enqueue_fohh_recovery_cmplt_resp(void *cmd_cookie,
															  char *table_name, int64_t received_rows)
{
	struct wally_table_queue_node *q;

	q = (struct wally_table_queue_node *)WALLY_CALLOC(sizeof(*q));
	if (!q) return WALLY_RESULT_NO_MEMORY;

	q->msg_type = WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP;
	q->msg.fohh_recovery_cmplt_resp.cmd_cookie = cmd_cookie;
	q->msg.fohh_recovery_cmplt_resp.table_name = WALLY_STRDUP(table_name, strlen(table_name));
	q->msg.fohh_recovery_cmplt_resp.received_rows = received_rows;

	wally_table_queue_enqueue_node_internal(q);

	WALLY_DEBUG_TABLE_QUEUE( "%s is successfully enqueued", wally_table_queue_msg_string[q->msg_type]);
	return WALLY_RESULT_NO_ERROR;
}

/*
 * Handle xfer row request for DB
 */
int wally_table_queue_handle_row_objects(struct wally_table_queue_node *q)
{
	int res = 0;
	int64_t now_s = 0, prev_s = monotime_s(), delta_s = 0;

	if (q->msg.xfer_row.cmd_cookie == NULL ||
		q->msg.xfer_row.argo_table_name == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}

	if (!res) {
		for (int i = 0; i < q->msg.xfer_row.object_count; i++) {
			if ((i % wally_max_hb_iteration) == 0) {
				now_s = monotime_s();
				delta_s = (now_s - prev_s);
				if (delta_s > wally_max_hb_miss_timeout_s) {
					WALLY_DEBUG_HB_TIMEOUT("Postgres transfer row heartbeat threshold reached");
					zthread_heartbeat(NULL);
				}
			}
			wally_xfer_row(q->msg.xfer_row.cmd_cookie, q->msg.xfer_row.argo_table_name,
						   q->msg.xfer_row.objects[i], q->msg.xfer_row.indexes[i], NULL, NULL);
			argo_object_release(q->msg.xfer_row.objects[i]);
		}
	}
	WALLY_DEBUG_TABLE_QUEUE( "%s:%s handling object_count = %d", wally_table_queue_msg_string[q->msg_type],
			  q->msg.xfer_row.argo_table_name, q->msg.xfer_row.object_count);
	if (q->msg.xfer_row.object_count)
		WALLY_FREE(q->msg.xfer_row.objects);
	if (q->msg.xfer_row.indexes)
		WALLY_FREE(q->msg.xfer_row.indexes);
	return res;
}

/*
 * Handle Xfer response request from DB
 */
int wally_table_queue_handle_xfer_response(struct wally_table_queue_node *q)
{
	int res = 0;
	if(q->msg.xfer_resp.cmd_cookie == NULL ||
			q->msg.xfer_resp.argo_table_name == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}
	if (!res) {
		wally_xfer_response(q->msg.xfer_resp.cmd_cookie,
							q->msg.xfer_resp.cmd_id,
							q->msg.xfer_resp.cmd_row_count,
							q->msg.xfer_resp.table_exists,
							q->msg.xfer_resp.argo_table_name);
	}
	WALLY_DEBUG_TABLE_QUEUE( "%s:%s handling cmd_id= %"PRId64" cmd_row_count = %"PRId64, wally_table_queue_msg_string[q->msg_type],
			  q->msg.xfer_resp.argo_table_name, q->msg.xfer_resp.cmd_id, q->msg.xfer_resp.cmd_row_count);
	WALLY_FREE(q->msg.xfer_resp.argo_table_name);
	return res;
}

/*
 * Handle fohh row mesage from server
 */
int wally_table_queue_handle_fohh_xfer_row(struct wally_table_queue_node *q)
{
	int res = 0;

	if (q->msg.fohh_xfer_row.cmd_cookie == NULL ||
		q->msg.fohh_xfer_row.argo_table_name == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}

    if (!res) {
		wally_xfer_row(q->msg.fohh_xfer_row.cmd_cookie, q->msg.fohh_xfer_row.argo_table_name,
					   q->msg.fohh_xfer_row.object, q->msg.fohh_xfer_row.cmd_id, NULL, NULL);
		argo_object_release(q->msg.fohh_xfer_row.object);
	}

	WALLY_DEBUG_TABLE_QUEUE("%s:%s handling cmd_id = %"PRId64, wally_table_queue_msg_string[q->msg_type],
			  q->msg.fohh_xfer_row.argo_table_name, q->msg.fohh_xfer_row.cmd_id);
	return res;
}

/*
 * Handle fohh xfer response from server
 */
int wally_table_queue_handle_fohh_xfer_response(struct wally_table_queue_node *q)
{
	int res = 0;

	if(q->msg.fohh_xfer_resp.cmd_cookie == NULL ||
		q->msg.fohh_xfer_resp.argo_table_name == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}
	if (!res) {
		wally_xfer_response(q->msg.fohh_xfer_resp.cmd_cookie,
							q->msg.fohh_xfer_resp.cmd_id,
							q->msg.fohh_xfer_resp.cmd_row_count,
							q->msg.fohh_xfer_resp.table_exists,
							q->msg.fohh_xfer_resp.argo_table_name);
	}

	WALLY_DEBUG_TABLE_QUEUE("%s:%s handling cmd_id= %"PRId64" cmd_row_count = %"PRId64, wally_table_queue_msg_string[q->msg_type],
			  q->msg.fohh_xfer_resp.argo_table_name, q->msg.fohh_xfer_resp.cmd_id, q->msg.fohh_xfer_resp.cmd_row_count);

	WALLY_FREE(q->msg.fohh_xfer_resp.argo_table_name);
	return res;
}

/*
 * Handle paused seq message from DB
 */
int wally_table_queue_handle_paused_seq(struct wally_table_queue_node *q)
{
	int res = 0;

	if (q->msg.paused_seq.cmd_cookie == NULL ||
		q->msg.paused_seq.argo_table_name == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}

	if (!res) {
		res = wally_update_paused_seq(q->msg.paused_seq.cmd_cookie,
									  q->msg.paused_seq.argo_table_name);
	}

	WALLY_DEBUG_TABLE_QUEUE("%s:%s handling ", wally_table_queue_msg_string[q->msg_type],
							q->msg.paused_seq.argo_table_name);
	return res;
}

/* Handle begin recovery req message */
int wally_table_queue_handle_begin_recovery_req(struct wally_table_queue_node *q)
{
    int res = WALLY_RESULT_NO_ERROR;

    wally_table_client_recovery_begin_request_handler(
                        q->msg.fohh_begin_recovery_req.cmd_cookie,
                        q->msg.fohh_begin_recovery_req.table_name,
                        q->msg.fohh_begin_recovery_req.recovery_sequence,
                        q->msg.fohh_begin_recovery_req.recovery_timeout,
                        q->msg.fohh_begin_recovery_req.sync_missing_rows);
	WALLY_FREE(q->msg.fohh_begin_recovery_req.table_name);

    return res;
}

/* Handle end recovery req message */
int wally_table_queue_handle_end_recovery_req(struct wally_table_queue_node *q)
{
    int res = WALLY_RESULT_NO_ERROR;

    wally_table_client_recovery_end_request_handler(
                        q->msg.fohh_end_recovery_req.cmd_cookie,
                        q->msg.fohh_end_recovery_req.table_name,
                        q->msg.fohh_end_recovery_req.server_sent_rows);

	WALLY_FREE(q->msg.fohh_end_recovery_req.table_name);
    return res;
}

/* Handle recovery complete response message */
int wally_table_queue_handle_recovery_cmplt_resp(struct wally_table_queue_node *q)
{
    int res = WALLY_RESULT_NO_ERROR;

    wally_table_server_handle_complete_response(
                        q->msg.fohh_recovery_cmplt_resp.cmd_cookie,
                        q->msg.fohh_recovery_cmplt_resp.table_name,
                        q->msg.fohh_recovery_cmplt_resp.received_rows);
	WALLY_FREE(q->msg.fohh_recovery_cmplt_resp.table_name);

    return res;
}

/*
 * Handle update exists message from DB
 */
int wally_table_queue_handle_table_exists(struct wally_table_queue_node *q)
{
	int res = 0;

	if (q->msg.table_exists.cmd_cookie == NULL ||
		q->msg.table_exists.argo_table_name == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}

	if (!res) {
		wally_xfer_update_table_exists(q->msg.table_exists.cmd_cookie,
											  q->msg.table_exists.argo_table_name,
											  q->msg.table_exists.table_exists,
											  true);
	}

	WALLY_DEBUG_TABLE_QUEUE("%s:%s handling ", wally_table_queue_msg_string[q->msg_type],
												q->msg.table_exists.argo_table_name);

	return res;
}

/*
 * Handle client register row request from client
 */
int wally_table_queue_handle_fohh_client_register_row_string(struct wally_table_queue_node *q)
{
	int res = 0;

	if (q->msg.register_row_string.cookie == NULL ||
		q->msg.register_row_string.object == NULL ||
		q->msg.register_row_string.structure_cookie == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		if (q->msg.register_row_string.object) {
			argo_object_release(q->msg.deregister_row_string.object);
		}
		res = WALLY_RESULT_ERR;
	}

	if (!res) {
		res = client_register_row_string_callback_internal(q->msg.register_row_string.cookie,
															q->msg.register_row_string.structure_cookie,
															q->msg.register_row_string.object);
	}

	return res;
}

int wally_table_queue_handle_fohh_client_deregister_row_string(struct wally_table_queue_node *q)
{
	int res = 0;

	if (q->msg.deregister_row_string.cookie == NULL ||
		q->msg.deregister_row_string.object == NULL ||
		q->msg.deregister_row_string.structure_cookie == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);

		if (q->msg.deregister_row_string.object) {
			argo_object_release(q->msg.deregister_row_string.object);
		}
		res = WALLY_RESULT_ERR;
	}

	if (!res) {
		res = client_deregister_row_string_callback_internal(q->msg.deregister_row_string.cookie,
															q->msg.deregister_row_string.structure_cookie,
															q->msg.deregister_row_string.object);
	}

	return res;
}
/*
 * Handle table initialization message
 */
int wally_table_queue_handle_do_wts(struct wally_table_queue_node *q)
{
	int res = 0;

	if (q->msg.do_wts.wt == NULL) {
		WALLY_LOG(AL_ERROR, "Handling %s failed due to invalid parameters",
				wally_table_queue_msg_string[q->msg_type]);
		res = WALLY_RESULT_ERR;
	}

	if (!res) {
		res = do_wts_internal(q->msg.do_wts.wt,
							q->msg.do_wts.wallyd_slave_db,
							q->msg.do_wts.wallyd_remote_db,
							q->msg.do_wts.wallyd,
							q->msg.do_wts.fully_loaded,
							q->msg.do_wts.cleanup_param,
							q->msg.do_wts.load_tables,
							q->msg.do_wts.fixup_f,
							q->msg.do_wts.is_gwally,
							q->msg.do_wts.zptr_f);
	}

	return res;
}


/* Common function to handle queue message */
void wally_table_queue_process_msg_cb(struct zevent_base *base, void *void_cookie, int64_t cookie)
{
	struct wally_table_queue_node *q = void_cookie;
	int res = 0;
	int64_t thread_id = cookie;
	enum wally_msg_type msg_type = q->msg_type;
	struct wally_table_thread *t = &(wally_table_thread_inst[thread_id]);

	wally_table_thread_update_stats(q, thread_id, false);
	q->process_start_time_us = epoch_us();

	switch(q->msg_type) {
		case WALLY_MSG_DB_XFER_ROW:
			res = wally_table_queue_handle_row_objects(q);
			break;
		case WALLY_MSG_DB_XFER_RESPONSE:
			res = wally_table_queue_handle_xfer_response(q);
			break;
		case WALLY_MSG_FOHH_XFER_ROW:
			res = wally_table_queue_handle_fohh_xfer_row(q);
			break;
		case WALLY_MSG_FOHH_XFER_RESPONSE:
			res = wally_table_queue_handle_fohh_xfer_response(q);
			break;
		case WALLY_MSG_DB_PAUSED_SEQ:
			res = wally_table_queue_handle_paused_seq(q);
			break;
		case WALLY_MSG_DB_TABLE_EXISTS:
			res = wally_table_queue_handle_table_exists(q);
			break;
		case WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ:
			res = wally_table_queue_handle_begin_recovery_req(q);
			break;
		case WALLY_MSG_FOHH_END_RECOVERY_REQ:
			res = wally_table_queue_handle_end_recovery_req(q);
			break;
		case WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP:
			res = wally_table_queue_handle_recovery_cmplt_resp(q);
			break;
		case WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING:
			res = wally_table_queue_handle_fohh_client_register_row_string(q);
			break;
		case WALLY_MSG_FOHH_CLIENT_DEREGISTER_ROW_STRING:
			res = wally_table_queue_handle_fohh_client_deregister_row_string(q);
			break;
		case WALLY_MSG_DO_WTS:
			res = wally_table_queue_handle_do_wts(q);
			break;
		default:
			WALLY_LOG(AL_ERROR, "Invalid message type %d", q->msg_type);
	}
	if (res) {
		WALLY_LOG(AL_ERROR, "%s wally_table_queue_process_msg_cb failed", wally_table_queue_msg_string[msg_type]);
	}

	q->total_msg_process_time_us = epoch_us() - q->process_start_time_us;

	if (t->total_message_processed) {
		t->avg_msg_process_time_us = t->avg_msg_process_time_us + (q->total_msg_process_time_us - t->avg_msg_process_time_us)/t->total_message_processed;
	}
	WALLY_FREE(q);

}

/*
 * Threads are woken up in round robin fashion.
 * Fetch the next available thread for execution
 */
void wally_table_thread_next_thread_id()
{
	thread_id = (thread_id + 1) % NO_OF_WALLY_THREADS;
	if (!thread_id) {
		//wally_table_thread ID 0 is the master thread
		thread_id = (thread_id + 1) % NO_OF_WALLY_THREADS;
	}
}

struct wally_table_thread *wally_table_thread_get_thread_inst(char *table_name)
{
	struct wally_table_thread *t = NULL;

	if (table_name == NULL) {
		t = &wally_table_thread_inst[thread_id];
		wally_table_thread_next_thread_id();
	} else {
		t = zhash_table_lookup(table_thread_hash,
							   table_name,
							   strlen(table_name),
							   NULL);
		if (t == NULL) {
			t = &wally_table_thread_inst[thread_id];
			if (zhash_table_store(table_thread_hash,
							 table_name,
							 strlen(table_name),
							 0,
							 t)) {
				WALLY_LOG(AL_ERROR, "No Memory for zhash_table_store msg");
				wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
				return NULL;
			}
			wally_table_thread_next_thread_id();
		}
	}
	return t;
}

/* Wally table thread create handler */
void wally_table_thread_load_distributor(struct zevent_base *base, void *void_cookie, int64_t cookie)
{
	struct wally_table_queue_node *data = void_cookie;
	struct wally_table_thread *thread_s = NULL;
	if (data == NULL) {
		WALLY_LOG(AL_ERROR, "Queue node is NULL");
		wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
		return;
	}

	wally_table_thread_update_stats(data, 0, false);

	switch(data->msg_type) {
		case WALLY_MSG_DB_XFER_ROW:
			thread_s = wally_table_thread_get_thread_inst(data->msg.xfer_row.argo_table_name);
			break;
		case WALLY_MSG_DB_XFER_RESPONSE:
			thread_s = wally_table_thread_get_thread_inst(data->msg.xfer_resp.argo_table_name);
			if (thread_s) {
				if (thread_s->zevent->high_priority_enqueue_count == 0 &&
					thread_s->zevent->low_priority_enqueue_count == 0) {
					zhash_table_remove(table_thread_hash, data->msg.xfer_resp.argo_table_name,
									   strlen(data->msg.xfer_resp.argo_table_name), NULL);
				}
			}
			break;
		case WALLY_MSG_FOHH_XFER_ROW:
			thread_s = wally_table_thread_get_thread_inst(data->msg.fohh_xfer_row.argo_table_name);
			break;
		case WALLY_MSG_FOHH_XFER_RESPONSE:
			thread_s = wally_table_thread_get_thread_inst(data->msg.fohh_xfer_resp.argo_table_name);
			if (thread_s) {
				if (thread_s->zevent->high_priority_enqueue_count == 0 &&
					thread_s->zevent->low_priority_enqueue_count == 0) {
					zhash_table_remove(table_thread_hash, data->msg.fohh_xfer_resp.argo_table_name,
							strlen(data->msg.fohh_xfer_resp.argo_table_name), NULL);
				}
			}
			break;
		case WALLY_MSG_DB_PAUSED_SEQ:
			thread_s = wally_table_thread_get_thread_inst(data->msg.paused_seq.argo_table_name);
			break;
		case WALLY_MSG_DB_TABLE_EXISTS:
			thread_s = wally_table_thread_get_thread_inst(data->msg.table_exists.argo_table_name);
			break;
        case WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ:
            thread_s = wally_table_thread_get_thread_inst(data->msg.fohh_begin_recovery_req.table_name);
            break;
        case WALLY_MSG_FOHH_END_RECOVERY_REQ:
            thread_s = wally_table_thread_get_thread_inst(data->msg.fohh_end_recovery_req.table_name);
            break;
        case WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP:
            thread_s = wally_table_thread_get_thread_inst(data->msg.fohh_recovery_cmplt_resp.table_name);
            break;
		case WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING:
		case WALLY_MSG_FOHH_CLIENT_DEREGISTER_ROW_STRING:
		case WALLY_MSG_DO_WTS:
			thread_s = wally_table_thread_get_thread_inst(NULL);
			break;
		default:
			WALLY_LOG(AL_ERROR, "Invalid wally message type %d", data->msg_type);
			return;
	}

	if (thread_s) {
		wally_table_thread_update_stats(data, thread_id, true);
		if (zevent_base_call(thread_s->zevent, wally_table_queue_process_msg_cb, data, thread_id) != 0) {
			WALLY_LOG(AL_ERROR, "Enqueing failed %d", data->msg_type);
			wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);
		}
	}

	/* Trigger heartbeat for wally master table thread */
	zthread_heartbeat(NULL);
}

/* Fill table thread queue stats */
int fill_wally_table_queue_stats(void *cookie, int counter, void *structure_data)
{
    struct wally_table_thread *t = cookie;
    struct wally_table_queue_stats *queue_stats = structure_data;

    if (!t) {
		WALLY_LOG(AL_ERROR, "Wally Table Queue is NULL");
        return ARGO_RESULT_NO_ERROR;
    }

    if (!queue_stats) {
		WALLY_LOG(AL_ERROR, "wally_table_queue_stats is not initialized");
        return ARGO_RESULT_NO_ERROR;
    }

	queue_stats->total_message_pending = t->total_message_pending;
	queue_stats->total_message_xfer_row_pending = t->message_pending[WALLY_MSG_DB_XFER_ROW];
	queue_stats->total_message_xfer_resp_pending = t->message_pending[WALLY_MSG_DB_XFER_RESPONSE];
	queue_stats->total_message_fohh_xfer_row_pending = t->message_pending[WALLY_MSG_FOHH_XFER_ROW];
	queue_stats->total_message_fohh_xfer_resp_pending = t->message_pending[WALLY_MSG_FOHH_XFER_RESPONSE];
	queue_stats->total_message_fohh_begin_recovery_req_pending = t->message_pending[WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ];
	queue_stats->total_message_fohh_end_recovery_req_pending = t->message_pending[WALLY_MSG_FOHH_END_RECOVERY_REQ];
	queue_stats->total_message_fohh_recovery_cmplt_resp_pending = t->message_pending[WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP];
	queue_stats->total_message_paused_seq_pending = t->message_pending[WALLY_MSG_DB_PAUSED_SEQ];
	queue_stats->total_message_db_table_exists_pending = t->message_pending[WALLY_MSG_DB_TABLE_EXISTS];
	queue_stats->total_message_fohh_client_register_row_pending = t->message_pending[WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING];
	queue_stats->total_message_do_wts_pending = t->message_pending[WALLY_MSG_DO_WTS];

	queue_stats->total_message_processed = t->total_message_processed;
	queue_stats->total_message_xfer_row_processed = t->message_processed[WALLY_MSG_DB_XFER_ROW];
	queue_stats->total_message_xfer_resp_processed = t->message_processed[WALLY_MSG_DB_XFER_RESPONSE];
	queue_stats->total_message_fohh_xfer_row_processed = t->message_processed[WALLY_MSG_FOHH_XFER_ROW];
	queue_stats->total_message_fohh_xfer_resp_processed = t->message_processed[WALLY_MSG_FOHH_XFER_RESPONSE];
	queue_stats->total_message_fohh_begin_recovery_req_processed = t->message_processed[WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ];
	queue_stats->total_message_fohh_end_recovery_req_processed = t->message_processed[WALLY_MSG_FOHH_END_RECOVERY_REQ];
	queue_stats->total_message_fohh_recovery_cmplt_resp_processed = t->message_processed[WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP];
	queue_stats->total_message_paused_seq_processed = t->message_processed[WALLY_MSG_DB_PAUSED_SEQ];
	queue_stats->total_message_db_table_exists_processed = t->message_processed[WALLY_MSG_DB_TABLE_EXISTS];
	queue_stats->total_message_fohh_client_register_row_processed = t->message_processed[WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING];
	queue_stats->total_message_do_wts_processed = t->message_processed[WALLY_MSG_DO_WTS];
	queue_stats->avg_wait_time_us = t->avg_wait_time_us;
	if (t->min_wait_time_us == INT64_MAX) {
		queue_stats->min_wait_time_us = 0;
	} else {
		queue_stats->min_wait_time_us = t->min_wait_time_us;
	}
	queue_stats->max_wait_time_us = t->max_wait_time_us;
	queue_stats->avg_msg_process_time_us = t->avg_msg_process_time_us;

	/* Rest the queue stats for every 1sec interval */
	t->total_message_processed = 0;

	for (int i = 0; i < WALLY_MSG_MAX; i++) {
		t->message_processed[i] = 0;
	}
	t->avg_wait_time_us = 0;
	t->min_wait_time_us = INT64_MAX;
	t->max_wait_time_us = 0;
	t->avg_msg_process_time_us = 0;

	return ARGO_RESULT_NO_ERROR;;
}

/*
 * Initialize the wally table threads
 */
int32_t wally_table_thread_init(void)
{
	int res = 0;
	const char *wally_table_thread_name = NULL;
	if (IS_WALLY_LAYER_ENABLED) {
		wally_table_queue_stats_description = argo_register_global_structure(WALLY_TABLE_QUEUE_STATS_HELPER);
		if (!wally_table_queue_stats_description)
			return WALLY_RESULT_ERR;

		WALLY_LOG(AL_INFO, "wally_table_thread_init");
		wally_table_thread_inst = (struct wally_table_thread *) WALLY_CALLOC(
									sizeof(struct wally_table_thread) * NO_OF_WALLY_THREADS);

		for (int i = 0; i < NO_OF_WALLY_THREADS; i++) {

			snprintf(wally_table_thread_inst[i].wally_table_thread_name,
					WALLY_MAX_THREAD_NAME_LEN,
					"wally_thread_%d", i);
			wally_table_thread_name = wally_table_thread_inst[i].wally_table_thread_name;
			WALLY_LOG(AL_INFO, "Creating wally thread %s", wally_table_thread_name);
			wally_table_thread_inst[i].zevent = zevent_handler_create(wally_table_thread_name,
											16*1024*1024, wally_default_hb_timeout_s);

			if (wally_table_thread_inst[i].zevent == NULL) {
				WALLY_LOG(AL_ERROR, "Wally thread init failed for thread:%s res=%d", wally_table_thread_name, res);
				return WALLY_RESULT_ERR;
			}
			char name[WALLY_MAX_THREAD_NAME_LEN] = {0};
			snprintf(name, WALLY_MAX_THREAD_NAME_LEN, "wally_table_queue_%d", i);
			wally_table_thread_inst[i].queue_stats = WALLY_CALLOC(sizeof(struct wally_table_queue_stats));
			/* Do queue stats registration */
			if (wally_table_thread_inst[i].queue_stats) {
				wally_table_thread_inst[i].wally_table_queue_structure = argo_log_register_structure(wally_stats_log,
																								name,
																								AL_INFO,
																								60*1000*1000,    /* 1 minute */
																								wally_table_queue_stats_description,
																								wally_table_thread_inst[i].queue_stats,
																								0,
																								fill_wally_table_queue_stats,
																								&(wally_table_thread_inst[i]));
			}
			pthread_mutex_init(&(wally_table_thread_inst[i].wally_table_thread_lock), NULL);
			wally_table_thread_inst[i].total_message_pending = 0;
			wally_table_thread_inst[i].total_message_processed = 0;
			for (int j = 0; j < WALLY_MSG_MAX; j++) {
				wally_table_thread_inst[i].message_pending[j] = 0;
				wally_table_thread_inst[i].message_processed[j] = 0;
			}

			wally_table_thread_inst[i].avg_wait_time_us = 0;
			wally_table_thread_inst[i].min_wait_time_us = INT64_MAX;
			wally_table_thread_inst[i].max_wait_time_us = 0;
		}

	}
	table_thread_hash = zhash_table_alloc(&wally_allocator);
	if (!table_thread_hash)
	{
	   return WALLY_RESULT_NO_MEMORY;
	}


	return WALLY_RESULT_NO_ERROR;
}
