/*
 * zpa_cloud_config.h Copyright (C) 2014 Zscaler, Inc. All Rights Reserved
 */

#ifndef __ZPA_CLOUD_CONFIG__H
#define __ZPA_CLOUD_CONFIG__H

#include <event2/buffer.h>
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zcdns/zcdns.h"
#include "zcrypt/zcrypt.h"
#include "parson/parson.h"

#ifdef __cplusplus
extern "C" {
#endif

extern const int ZPATH_LOCAL_MAX_CONFIG;
extern const int ZPA_MAX_CLOUDS;
extern const int ZPA_MAX_CLOUD_CONFIG_ROOT_CERTS;
extern const int ZPA_CLOUD_CONFIG_GET_DEFAULT_CLOUD;
extern const int ZPA_CLOUD_CONFIG_NOT_GET_DEFAULT_CLOUD;
#define MAX_DIST_HOSTNAME_LEN 250
#define MAX_CA_FILE_LEN 500
#define MAX_CLOUD_NAME_LEN 100
#define MAX_DIST_FETCH_PATH_LEN 500
#define MAX_DIST_FETCH_PATH_DEVELOP 100
#define MAX_ENROLLMENT_KEY_BLOCKS 100

// Enrollment Key Tokenization parts
enum zpath_enrollment_key_type {
    SHARD_KEY_INDEX=0,
    API_NAME_KEY_INDEX,
    ENROLLMENT_KEY_INDEX,
    CLOUD_NAME_KEY_INDEX,
    DISTRIBUTION_HOST_KEY_INDEX,
    DISTRIBUTION_PROXY_KEY_INDEX,
    DISTRIBUTION_PATH_KEY_INDEX

};

// OAuth Enrollment Key Tokens
enum zpath_oauth_enrollment_key_type {
    OAUTH_API_NAME_KEY_INDEX=0,
    OAUTH_CLOUD_NAME_KEY_INDEX,
    OAUTH_CUSTOMER_GID_INDEX
};

enum ZPA_COUD_CONFIG_SOURCE{
    ZPA_COUD_CONFIG_SOURCE_DEFAULT=1,
    ZPA_COUD_CONFIG_SOURCE_LOCAL_DISK=2,
    ZPA_COUD_CONFIG_SOURCE_DIST_SERVER=3,
    ZPA_COUD_CONFIG_SOURCE_API_SERVER=4
};

extern const char ZPA_CLOUD_CONFIG_DEV_CLOUD_NAME[];
extern const char ZPA_CLOUD_CONFIG_FILE_FULL_PATH[];
extern const char ZPA_CLOUD_CONFIG_FILE_NAME[];

enum zpath_environment_type {
    zpath_environment_type_invalid,
    zpath_environment_type_dev,
    zpath_environment_type_high_gov,
    zpath_environment_type_moderate_gov,
    zpath_environment_type_high_staging,
    zpath_environment_type_total_count
};

struct zpath_exporter_cloud_config{
    char *cookie_prefix;
    char *auth_domain;
    char *saml_auth_domain;
    char *broker_domain;
    char *broker_ot_domain;
    char *uatu_domain;
    char *google_posture_domain;
    char *portal_hostname;
    char *pra_portal_hostname;
    char *portal_version;
    char *pra_service_portal_hostname;
    char *unified_portal_tld_appln_name;
    char *unified_portal_pra_tld_appln_name;
    char *managed_app_tld_appln_name;
    char *pra_cms_hostname;
    char *pra_djb_service_hostname;
};
struct zpath_sarge_cloud_config{
    char *dist_hostname;
    char *dist_proxyname;
    char *dist_path;
    char *dist_path_develop;
    char *filename_indicator;
    char *stack_upload_host;
    char *stack_upload_path;
    int require_fips;
    int proxy_enabled;
    int develop;
};
struct zpath_cloud_config {
    char *cloud_name;
    char *pbroker_broker_domain;
    enum zpath_environment_type cloud_type;
    struct zpath_sarge_cloud_config *sarge;
    struct zpath_exporter_cloud_config *exporter;
    struct zpn_apimap *zpn_apis;
    size_t  zpn_apis_count;
    struct zpn_samlsp_map *zpn_samlsps;
    size_t  zpn_samlsp_count;
    struct zpn_authsp_map *zpn_authsps;
    size_t  zpn_authsp_count;
    char **saml_audience_list;
    size_t  saml_audience_list_count;
    struct zcrypt_root_cert_map *zpa_cloud_root_certs;
    size_t zpa_cloud_root_certs_count;
    struct zcrypt_root_cert_map *zpa_cloud_trusted_ca_certs;
    size_t zpa_cloud_trusted_ca_certs_count;
    struct zpath_misc_domain_map *zpath_misc;
    size_t  zpath_misc_count;
    struct zpath_authenticated_sni_map *zpath_authenticated_sni;
    size_t  zpath_authenticated_sni_count;
};

struct zpath_all_cloud_config {
    struct zpath_cloud_config **cloud_config;
    size_t  cloud_config_count;
};

struct  zpath_all_cloud_config  *zpath_get_all_cloud_config();
struct zpath_cloud_config * zpath_get_cloud_config_from_name(const char *cloud_name);
struct zpath_exporter_cloud_config * zpath_get_exporter_config(const char *cloud_name);
struct zpath_cloud_config * zpath_get_cloud_config_by_sni(const char *sni );
struct zpath_cloud_config * zpath_get_cloud_config_by_filesystem();
struct zpath_cloud_config * zpath_get_cloud_config_by_filename_argv(const char *cmd);
struct zpath_cloud_config * zpath_get_cloud_config_from_apiname(const char *api_name);

void zpath_init_cloud_config();
int zpath_is_cloud_dev_env(const char *cloud_name);
int zpath_load_zpa_cloud_config(const char*);
int zpath_parse_provision_key(const char *enrollment_key, char *tokens[MAX_ENROLLMENT_KEY_BLOCKS], int *token_cnt);

size_t zpath_get_saml_audience_list(const char *cloud_name,char *** saml_audience_list);
size_t zpath_get_zpath_authenticated_sni_for_cloud(const char *cloud_name, struct zpath_authenticated_sni_map ** zpath_auth_snis);
int zpath_load_zpa_cloud_config(const char* config_file_path) ;
int zpath_load_default_zpa_cloud_config();
int zpath_load_local_zpa_cloud_config(const char* config_file_path);
int zpath_get_provisioning_key_details(const char *cfg_provisioning_key, char *api_name, char *cloud_name);
int zpath_get_oauth_cloud_details(const char *cfg_provisioning_key, char *api_name, char *cloud_name, int64_t *customer_gid);
int zpath_load_cloud_config_for_customer_apps(struct zcrypt_key *enc_key, char *api_name,
                                              struct evbuffer *evbuffer);
int zpath_check_if_local_cloud_config_file_exists();
int zpath_app_cloud_config_callback(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *cookie);
int zpath_parse_zpa_cloud_config_file(const JSON_Object *root_object);
#ifdef __cplusplus
} // extern "C"
#endif

#endif //__ZPA_CLOUD_CONFIG__H
