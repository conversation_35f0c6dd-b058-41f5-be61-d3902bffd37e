/*
 * zpath_simple_app.c. Copyright (C) 2013-2016 Zscaler, Inc. All Rights Reserved.
 *
 * simple app initialization with fewer external dependencies.
 */

#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <stdatomic.h>

#include "argo/argo_log.h"
#include "fohh/fohh.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_fohh_client.h"
#include "avl/avl.h"
#include "zthread/zthread.h"
#include "fohh/fohh_log.h"
#include "zevent/zevent.h"
#include "fohh/fohh_http.h"

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_entity.h"
#include "zpath_lib/zpath_rule.h"
#include "zpath_lib/zpath_customer_log_config.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_domainlist.h"
#include "zpath_lib/zpath_customer_notification.h"
#include "zpath_lib/zpath_category.h"
#include "zpath_lib/zpath_ip_table.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_ip_location.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_tag_entity.h"
#include "zpath_lib/zpath_customer_logo.h"
#include "zpath_lib/zpath_location.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_config.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/sanitizer_commands.h"

#include "zpath_lib/zpath_app_compiled.h"
#include "zthread/zthread_compiled.h"
#include "zpath_lib/zpath_cidr_lookup.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_misc/zpath_platform.h"
#include "zhw/zhw_os.h"

#define ZPATH_CUSTOMER_TABLE        "zpath_customer"
#define ZPATH_RULE_TABLE            "zpath_rule"
#define ZPATH_SERVICE_TABLE         "zpath_service"

struct wally *zpath_global_wally;
struct wally_origin *zpath_global_slave_db;
struct wally_origin *zpath_global_remote_db;
const char *zpath_app_db_hostname = NULL;

int (*zpath_lib_exit_handler_cb)(enum zpath_termination_code tc, void *cookie, int self_thread_num) = NULL;

pthread_mutex_t zpath_shard_create_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t shutdown_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

/* shard wally's are not used for simple apps. But this makes building
 * a little easier */
struct wally *zpath_shard_wally[ZPATH_MAX_SHARDS];

struct argo_log_collection *zpath_event_collection;
struct argo_log_collection *zpath_stats_collection;

const char *fohh_debug_names[] = FOHH_DEBUG_NAMES;
const char *wally_debug_names[] = WALLY_DEBUG_NAMES;

int64_t zpath_service_init_complete_us = 0;
int zpath_service_init_complete = 0;
int zpath_service_shutdown = 0;
int zpath_app_instance_id = 0;
int zpath_config_override_init_complete = 0;

static enum zpath_app_shutdown_state shutdown_state = zpath_app_shutdown_can_start;

int64_t zpath_system_start_time;
extern _Atomic int64_t zthread_curr_time;

/* Indicates if role-based access control is enabled or not. */
int64_t zpath_rbac_enabled = 0;

/* indicates if role-based access control(rbac) is supported by the component or not. */
int64_t zpath_rbac_supported = 0;

int zpath_app_is_endpoint = 0;

static struct zthread_rusage zpath_app_stats;
static struct zpath_inittime g_inittime;

struct argo_structure_description *zthread_rusage_desc;
struct argo_structure_description *zpath_inittime_desc;


struct shutdown_registrant {
    struct shutdown_registrant *next;
    zpath_app_shutdown_verify_callback *callback;
    void *cookie;
    enum zpath_app_shutdown_state state;
};

static struct shutdown_registrant *shutdown_registrants = NULL;

/*
 * If set, controls max logging mb. Must be set BEFORE call to
 * zpath_simple_app_init. If not set, the system automatically chooses.
 */
static int zpath_app_specific_max_logging_mb = 0;

static int request_atleast_once() {
    if (!strncmp(zpath_app_get_role_name(), ZPATH_APP_SITEC_INSTANCE_NAME, strlen(ZPATH_APP_SITEC_INSTANCE_NAME))) {
        return 0;
    }
    return 1;
}

void zpath_registration_started(int64_t time)
{
    zpath_system_start_time = time ? time : epoch_us();
}

void zpath_registration_completed(void)
{
    zpath_service_init_complete = 1;
    zpath_service_init_complete_us = epoch_us();

    int64_t delta = (zpath_service_init_complete_us - zpath_system_start_time)/US_PER_SEC;

    ZPATH_LOG(AL_NOTICE, "Process initialization complete! Total time taken is %"PRId64"s", delta);

    /* Log init details along with platform and build tag info once every day */
    if (zpath_inittime_desc) {
        char buf[1024] = {'\0'};
        struct zpath_inittime *inittime = &g_inittime;
        if (inittime) {
            inittime->inittime_s  = delta;
            inittime->major_ver   = ZPATH_VERSION_MAJOR;
            inittime->minor_ver   = ZPATH_VERSION_MINOR;
            inittime->patch_ver   = ZPATH_VERSION_PATCH;
            snprintf(buf, sizeof(buf), "%"PRIu64"00%"PRIu64"00%"PRIu64, inittime->major_ver, inittime->minor_ver, inittime->patch_ver);
            inittime->version_tag = strtoul(buf, NULL, 10);
            inittime->build_tag   = strtoul(ZPATH_VERSION_CHASH, NULL, 16);
            inittime->arch        = strstr(ZPATH_PLATFORM_ARCH, "x86") ? 86 : 0;
            zhw_get_runtime_platform(inittime->os_version_str, (int)sizeof(inittime->os_version_str), &inittime->os_version_id);
            argo_log_register_structure(zpath_stats_collection,
                                        "zpath_inittime",
                                        AL_INFO,
                                        DAY_TO_US(1),
                                        zpath_inittime_desc,
                                        inittime,
                                        1,
                                        NULL,
                                        NULL);
        }
    }
}

struct wally_index_column *zpath_app_get_global_index(const char *table_name,
                                                      const char *field_name)
{
    ZPATH_LOG(AL_ERROR, "global index is not supported for simple apps");
    return NULL;
}

int zpath_app_add_sharded_table(struct argo_structure_description *argo_description,
                                wally_row_callback_f *all_rows_callback,
                                void *all_rows_callback_cookie,
                                int multiple_index_consistency,
                                wally_row_fixup_f *fixup_f)
{
    ZPATH_LOG(AL_ERROR, "sharded tables not supported for simple apps");
    return ZPATH_RESULT_ERR;
}

int zpath_app_add_np_sharded_table(struct argo_structure_description *argo_description,
                                   wally_row_callback_f *all_rows_callback,
                                   void *all_rows_callback_cookie,
                                   int multiple_index_consistency,
                                   wally_row_fixup_f *fixup_f)
{
    ZPATH_LOG(AL_ERROR, "np sharded tables not supported for simple apps");
    return ZPATH_RESULT_ERR;
}

int zpath_app_fully_loaded_global_table(struct argo_structure_description *argo_description,
                                        wally_row_callback_f *all_rows_callback,
                                        void *all_rows_callback_cookie,
                                        wally_row_fixup_f *fixup_f,
                                        int sync_pause_register)
{
    ZPATH_LOG(AL_ERROR, "global tables not supported for simple apps");
    return ZPATH_RESULT_ERR;
}

int zpath_app_fully_loaded_sharded_table(struct argo_structure_description *argo_description,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         wally_row_fixup_f *fixup_f)
{
    ZPATH_LOG(AL_ERROR, "sharded tables not supported for simple apps");
    return ZPATH_RESULT_ERR;
}

static int fully_loaded_callback(void *response_callback_cookie,
                                 struct wally_registrant *registrant,
                                 struct wally_table *table,
                                 int64_t request_id,
                                 int row_count)
{
    int32_t *count = response_callback_cookie;

    __sync_sub_and_fetch_4(count, 1);
    ZPATH_LOG(AL_NOTICE, "%s: table %s: Fully loaded: Read %d rows", wally_table_wally_name(table), wally_table_name(table), row_count);
    return WALLY_RESULT_NO_ERROR;
}

int zpath_app_fully_loaded_customer_table(struct wally_table **table_result,
                                          int64_t customer_gid,
                                          struct wally *single_tenant_wally,
                                          struct argo_structure_description *argo_description,
                                          wally_row_callback_f *all_rows_callback,
                                          void *all_rows_callback_cookie,
                                          wally_row_fixup_f *fixup_f,
                                          int register_with_zpath_table)
{
    int res;
    int32_t wait_int = 0;

    struct wally_table *table;
    struct wally_index_column *col;

    table = wally_table_create_named_db(single_tenant_wally,
                                        0,
                                        argo_description_get_type(argo_description),
                                        argo_description,
                                        all_rows_callback,
                                        all_rows_callback_cookie,
                                        1, // Use all origins.
                                        1, /* Multiple index
                                            * consistency is
                                            * needed to force a
                                            * load from scratch to
                                            * get going- otherwise
                                            * old partial tables
                                            * will be there, and
                                            * we won't read
                                            * correctly.  */
                                        1 /* Fully loaded */,
                                        fixup_f);
    if (!table) {
        ZPATH_LOG(AL_ERROR, "Could not create table <%s>", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }

    /* Register for all rows... */
    if (strncmp(table->name, ZPATH_CUSTOMER_TABLE, strlen(ZPATH_CUSTOMER_TABLE)) == 0) {
        col = wally_table_get_index(table, "gid");
    } else if (strncmp(table->name, ZPATH_RULE_TABLE, strlen(ZPATH_RULE_TABLE)) == 0)  {
        col = wally_table_get_index(table, "and_entity");
    } else if (strncmp(table->name, ZPATH_SERVICE_TABLE, strlen(ZPATH_SERVICE_TABLE)) == 0)  {
        col = wally_table_get_index(table, "customer_id");
    } else {
        col = wally_table_get_index(table, "customer_gid");
    }

    if (!col) {
        ZPATH_LOG(AL_ERROR, "Could not get table index for %s, column customer_gid", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }
    res = wally_table_register_for_row_through_fully_loaded(NULL,           // registrant
                                                            col,            // column
                                                            &customer_gid,  // key
                                                            sizeof(customer_gid),
                                                            0,              // request_id
                                                            0,              // request_sequence
                                                            request_atleast_once(), // request_atleast_one
                                                            0,              // just_callback
                                                            0,              // unique_registration
                                                            fully_loaded_callback, // response_callback
                                                            &wait_int);     // response_callback_cookie
    if (res) {
        if (res != WALLY_RESULT_ASYNCHRONOUS) {
            ZPATH_LOG(AL_ERROR, "Could not register for all rows for customer from %s: %s",  argo_description_get_type(argo_description), wally_error_strings[res]);
            return WALLY_RESULT_ERR;
        } else {
            /* We will wait for this value to return to 0 indicating that config has been loaded */
            __sync_add_and_fetch_4(&(wait_int), 1);
            ZPATH_LOG(AL_NOTICE, "%s: Waiting for data to be read...", argo_description_get_type(argo_description));
        }
        res = WALLY_RESULT_NO_ERROR;
    }

    if (register_with_zpath_table) {
        /* Register with zpath_table... */
        res = zpath_table_register(single_tenant_wally,
                                   argo_description_get_type(argo_description),
                                   zpath_table_default_callback,
                                   table);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Could not register table <%s> with zpath_table", argo_description_get_type(argo_description));
            return res;
        }
    }

    int i = 0;
    int wrote_one = 0;
    while (wait_int != 0) {
        if ((i % 100) == 0) {
            wrote_one = 1;
            ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s", argo_description_get_type(argo_description));
        }
        usleep(10000);
        i++;
    }
    if (wrote_one) {
        ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s -- COMPLETE", argo_description_get_type(argo_description));
    }

    *table_result = table;

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_add_writable_sharded_table(struct argo_structure_description *argo_description,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         int multiple_index_consistency,
                                         wally_row_fixup_f *fixup_f)
{
    ZPATH_LOG(AL_ERROR, "sharded tables not supported for simple apps");
    return ZPATH_RESULT_ERR;
}

static void zthread_log(const char *msg)
{
    ZPATH_LOG(AL_WARNING, "%s", msg);
}

struct wally_index_column **zpath_app_get_sharded_index(const char *table_name,
                                                        const char *field_name)
{
    ZPATH_LOG(AL_ERROR, "sharded tables not supported for simple apps");
    return NULL;
}

struct wally_index_column **zpath_app_get_np_sharded_index(const char *table_name, const char *field_name)
{
    ZPATH_LOG(AL_ERROR, "np sharded tables not supported for simple apps");
    return NULL;
}



const char *zpath_app_shutdown_name(enum zpath_app_shutdown_state state)
{
    if (state == zpath_app_shutdown_can_start) return "can start";
    if (state == zpath_app_shutdown_start) return "start";
    if (state == zpath_app_shutdown_can_complete) return "can complete";
    if (state == zpath_app_shutdown_complete) return "complete";
    return "invalid";
}

int zpath_app_shutdown_notice(zpath_app_shutdown_verify_callback *callback,
                              void *cookie)
{
    struct shutdown_registrant *reg;
    reg = ZLIB_CALLOC(sizeof(*reg));
    if (!reg) return ZPATH_RESULT_NO_MEMORY;
    pthread_mutex_lock(&shutdown_lock);
    reg->callback = callback;
    reg->cookie = cookie;
    reg->next = shutdown_registrants;
    shutdown_registrants = reg;
    pthread_mutex_unlock(&shutdown_lock);
    return ZPATH_RESULT_NO_ERROR;
}


static int collection_shutdown(void *cookie,
                               enum zpath_app_shutdown_state state)
{
    int64_t res;

    ZPATH_LOG(AL_NOTICE, "Collection snapshot: %s", zpath_app_shutdown_name(state));
    switch (state) {

    case zpath_app_shutdown_can_start:
        return ZPATH_RESULT_NO_ERROR;

    case zpath_app_shutdown_start:
        res = argo_collections_snapshot();
        if (res) {
            ZPATH_LOG(AL_ERROR, "Collection snapshotting returned %s", zpath_result_string(res));
        }
        return res;

    case zpath_app_shutdown_can_complete:
        res = argo_collections_verify_snapshot();
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Collection snapshotting returned %ld objects still to log", (long) res);
            return ZPATH_RESULT_NOT_READY;
        }
        return ZPATH_RESULT_NO_ERROR;

    case zpath_app_shutdown_complete:
        return ZPATH_RESULT_NO_ERROR;

    default:
        ZPATH_LOG(AL_ERROR, "Bad state");
        return ZPATH_RESULT_ERR;
    }
}


int zpath_app_shutdown(int64_t timeout_s, int exit_proc)
{
    struct shutdown_registrant *reg;
    int waiting = 0;
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t end_s = timeout_s > 0 ? (epoch_s() + timeout_s) : -1;

    zpath_service_shutdown = 1;

    zthread_curr_time = epoch_us();

    pthread_mutex_lock(&shutdown_lock);

    zthread_set_abort(0);

    while (1) {
        for (; shutdown_state < zpath_app_shutdown_complete; shutdown_state++) {
            fprintf(stderr, "Service shutdown: %s\n", zpath_app_shutdown_name(shutdown_state));
            waiting = 0;
            for (reg = shutdown_registrants; reg; reg = reg->next) {
                if (reg->state == shutdown_state) {
                    res = (reg->callback)(reg->cookie, reg->state);
                    if (res) {
                        waiting = 1;
                    } else {
                        reg->state++;
                    }
                }
            }
            if (waiting) break;
        }
        if (shutdown_state == zpath_app_shutdown_complete) break;
        if (end_s > 0 && epoch_s() > end_s) {
            res = ZPATH_RESULT_INCOMPLETE;
            break;
        }
        usleep(100000);
    }

    zthread_curr_time = epoch_us();

    pthread_mutex_unlock(&shutdown_lock);
    if (exit_proc) {
        fprintf(stdout,"ERROR: Service shutdown: %s\n", zpath_app_shutdown_name(shutdown_state));
        fflush(stdout);
        sleep(1);
        exit(0);
    }
    zpath_service_shutdown = 0;

    return res;
}


/*
 * Shut down the system- debug command.
 */
static int service_shutdown_callback(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    zpath_debug_cb_printf_response(request_state, "Shutting down\n");
    zpath_app_shutdown(-1, 1);
    return ZPATH_RESULT_NO_ERROR;
}

static int simple_app_status_callback(struct zpath_debug_state *request_state,
                          const char **query_values,
                          int query_value_count,
                          void *cookie)
{
    struct fohh_http_response* req = (struct fohh_http_response*) request_state;
    if (zpath_service_init_complete) {
        req->status = 200;
        zpath_debug_cb_printf_response(request_state, "running\n");
    } else if (zpath_service_shutdown) {
        req->status = 500;
        zpath_debug_cb_printf_response(request_state, "stopping\n");
    } else {
        req->status = 500;
        zpath_debug_cb_printf_response(request_state, "initializing\n");
    }
    return ZPATH_RESULT_NO_ERROR;
}



/*
 * Shut down the system- debug command.
 */
static int zpn_system_ungraceful_shutdown(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    zpath_debug_cb_printf_response(request_state, "ERROR: Shutting down per user request\n");
    sleep(1);
    exit(2);
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_crash_abort(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    abort();
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_crash_heartbeat(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    sleep(100500);
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_crash_fortify(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie)
{
    char buf[5];
    // coverity[overrun-buffer-arg]
    memset(&buf, 0, (rand() % 128) + 128);
    return buf[0];
}

__attribute__((noinline)) static int debug_crash_stack_smash(struct zpath_debug_state *request_state,
                                                             const char **query_values,
                                                             int query_value_count,
                                                             void *cookie)
{
    char a;
    // coverity[overrun-buffer-arg]
    memset(&a, 0, (rand() % 128) + 128);
    return a;
}

static int debug_crash_segfault(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    int *p = 0;
    // coverity[var_def_op]
    return *p;
}

static int zpath_debug_app_stats_fill(void *cookie, int counter,
                                      void *structure_data)
{
    (void) structure_data;
    (void) cookie;
    zthread_fill_rusage(RUSAGE_SELF, &zpath_app_stats);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_app_stats(void)
{
    struct argo_log_registered_structure *s;

    zpath_app_stats.ru_startime = zpath_system_start_time;

    // Log both once per second and once per minute.
    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "rusage_thread_all_1s",
                                    AL_INFO,
                                    1*1000*1000,
                                    zthread_rusage_desc,
                                    &zpath_app_stats,
                                    1,
                                    zpath_debug_app_stats_fill,
                                    NULL);

    if (!s) {
        ZPATH_LOG(AL_ERROR, "Could not register zpath_app stats");
        return ZPATH_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "rusage_thread_all",
                                    AL_INFO,
                                    60*1000*1000,
                                    zthread_rusage_desc,
                                    &zpath_app_stats,
                                    1,
                                    zpath_debug_app_stats_fill,
                                    NULL);

    if (!s) {
        ZPATH_LOG(AL_ERROR, "Could not register zpath_app stats");
        return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_app_version(struct zpath_debug_state *request_state,
                      const char **query_values,
                      int query_value_count,
                      void *cookie)
{
    char *version_str = cookie;
    zpath_debug_cb_printf_response(request_state, "%s\n", version_str);
    return ZPATH_RESULT_NO_ERROR;
}

static int inittime_callback(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    if (zpath_system_start_time && zpath_service_init_complete_us > zpath_system_start_time)
        zpath_debug_cb_printf_response(request_state, "Time taken for init: %"PRId64"s\n",
                                (zpath_service_init_complete_us - zpath_system_start_time)/US_PER_SEC);
    else
        zpath_debug_cb_printf_response(request_state, "Process is still initializing\n");


    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_uptime(struct zpath_debug_state *request_state,
                     const char **query_values,
                     int query_value_count,
                     void *cookie)
{
    int64_t now = epoch_us();
    int64_t diff = now - zpath_system_start_time;
    diff /= 1000000l; /* To seconds */
    if (query_values[0]) {
        /* Asked for seconds only. */
        zpath_debug_cb_printf_response(request_state, "%ld\n", (long) diff);
    } else {
        int64_t days = diff / (24l*60l*60l);
        int64_t hours = (diff % (24l*60l*60l)) / (60l*60l);
        int64_t minutes =  (diff % (60l*60l)) / (60l);
        int64_t seconds = (diff % 60l);
        if (days) {
            if (days == 1) {
                zpath_debug_cb_printf_response(request_state, "%ld day %02ld:%02ld:%02ld\n", (long) days, (long) hours, (long) minutes, (long) seconds);
            } else {
                zpath_debug_cb_printf_response(request_state, "%ld days %02ld:%02ld:%02ld\n", (long) days, (long) hours, (long) minutes, (long) seconds);
            }
        } else if (hours) {
            zpath_debug_cb_printf_response(request_state, "%02ld:%02ld:%02ld\n", (long) hours, (long) minutes, (long) seconds);
        } else {
            zpath_debug_cb_printf_response(request_state, "%02ld:%02ld\n", (long) minutes, (long) seconds);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_starttime(struct zpath_debug_state *request_state,
                        const char **query_values,
                        int query_value_count,
                        void *cookie)
{
    int64_t when = zpath_system_start_time;
    if (query_values[0]) {
        /* Asked for epoch. */
        zpath_debug_cb_printf_response(request_state, "%ld\n", (long) when / (1000l*1000l));
    } else {
        char out_str[100];
        out_str[0] = 0;
        argo_log_gen_time(when, out_str, sizeof(out_str), 0, 0);
        zpath_debug_cb_printf_response(request_state, "%s\n", out_str);
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* Logging Function used for zevent logging */
static void zevent_logger(int priority, const char *file, const char *func, int line, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    ZPATH_LOG_TEXT(priority, file, func, line, "%s", dump);
}

int zpath_simple_app_init(const struct zpath_simple_app_init_params *params)
{
    if (!params || !params->role_version_string) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    const int event_log_mask_stderr = params->log_stderr ? (params->debuglog ? 0xff : 0x7f) : 0x0;
    const int event_log_mask_syslog = params->log_syslog ? (params->debuglog ? 0xff : 0x7f) : 0x0;

    zpath_registration_started(epoch_us());

    int result = argo_library_init(ZPATH_ARGO_RPC_REGISTRATIONS_MAX);
    if (result) {
        fprintf(stderr, "%s:%s:%d: argo_library_init failed: %d\n", __FILE__, __FUNCTION__, __LINE__, result);
        return result;
    }

    zthread_rusage_desc = argo_register_global_structure(ZTHREAD_RUSAGE_HELPER);
    zpath_inittime_desc = argo_register_global_structure(ZPATH_INITTIME_HELPER);

    /* We initialize logging infrastructure almost immediately- even
     * though we don't know our instance name yet. */
    if (argo_log_init_with_maxmem("unknown", 0, params->role_name, params->role_version_string, 1, zpath_app_specific_max_logging_mb)) {
        fprintf(stderr, "Err: could not initialize argo_log\n");
        return ZPATH_RESULT_ERR;
    }

    /* Note: We use the stats log for logging the stats for all the
     * others, so we create it first, even if it doesn't have readers,
     * etc. */
    zpath_stats_collection = argo_log_create("statistics_log", NULL, NULL);
    if (!zpath_stats_collection) {
        ZPATH_LOG(AL_ERROR, "Could not initialize stats_log argo_log collection");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Initialize event and stats logs.
     */
    zpath_event_collection = argo_log_create("event_log", NULL, NULL);
    if (!zpath_event_collection) {
        fprintf(stderr, "Err: could not initialize event_log argo_log collection\n");
        return ZPATH_RESULT_ERR;
    }
    argo_library_log("event_log");

    ZPATH_LOG(AL_NOTICE, "%s: starting, version %s", params->role_name ? : "unknown", params->role_version_string);
    //ZPATH_LOG(AL_NOTICE, "Logging system initialized.");

    result = zpath_app_logging_init(params->role_name,
                                    event_log_mask_stderr,      /* stderr */
                                    event_log_mask_syslog,      /* syslog */
                                    1,                          /* abbreviated format */
                                    params->debug_port);        /* add debug commands */
    if (result) {
        return result;
    }

    if (params->log_filename) {
        struct argo_log_file *event_file;
        struct argo_log_reader *event_file_reader;

        event_file = argo_log_file_create(zpath_event_collection,
                                          params->log_filename,
                                          NULL,
                                          1024*1024*1024,
                                          argo_serialize_binary);

        if (!event_file) {
            fprintf(stderr, "Error: Cannot create log file for %s\n", params->log_filename);
            return ZPATH_RESULT_ERR;
        }

        event_file_reader = argo_log_read(zpath_event_collection, "event_log_file", 0, 1, argo_log_file_callback, NULL, event_file, zpath_stats_collection, 60*1000*1000);
        if (!event_file_reader) {
            fprintf(stderr, "Error: Cannot create log file writer for %s\n", params->log_filename);
            return ZPATH_RESULT_ERR;
        }
    }

    zthread_set_log(zthread_log);

    /* Don't give it an event log so it doesn't log the initialization */
    result = fohh_ssl_init(params->dump_lib_versions ? zpath_event_collection : NULL, params->fips_mode);
    if (result) {
        ZPATH_LOG(AL_ERROR, "fohh_ssl_init returned %d", result);
        return ZPATH_RESULT_ERR;
    }

    zevent_set_logger(zevent_logger);
    zevent_init();

    /* Don't give it an event log so it doesn't log the initialization */
    result = fohh_libevent_init(params->dump_lib_versions ? zpath_event_collection : NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "fohh_libevent_init failed: %d", result);
        return result;
    }

    result = zpath_debug_pre_init(params->debug_port != 0);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize zpath_debug");
        return ZPATH_RESULT_ERR;
    }

    wally_init();

    if (params->debug_port) {
        result = zpath_debug_add_flag(&fohh_debug,
                                      fohh_debug_catch,
                                      "fohh",
                                      fohh_debug_names);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize wally debug flags");
            return result;
        }

        result = zpath_debug_add_flag(&wally_debug,
                                      wally_debug_catch,
                                      "wally",
                                      wally_debug_names);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize wally debug flags");
            return result;
        }

        result = zpath_debug_add_collection_mem_view();
		if (result) {
			ZPATH_LOG(AL_ERROR, "Could not setup argo log collection mem view");
			return result;
		}

        result = zpath_debug_add_collection(zpath_event_collection);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add debugging for collection %s: %s",
                      argo_log_get_name(zpath_event_collection),
                      zpath_result_string(result));
            return result;
        }

        result = zpath_debug_add_collection(zpath_stats_collection);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add debugging for collection %s: %s",
                      argo_log_get_name(zpath_stats_collection),
                      zpath_result_string(result));
            return result;
        }

        result = zpath_debug_add_read_command("Get software version",
                                         "/version",
                                         zpath_app_version,
                                         ZLIB_STRDUP(params->role_version_string, strlen(params->role_version_string)),
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
            return result;
        }

        result = zpath_debug_add_read_command("Get software uptime",
                                         "/uptime",
                                         zpath_app_uptime,
                                         NULL,
                                         "seconds", "Specify as anything to return seconds",
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
            return result;
        }

        result = zpath_debug_add_read_command("Check time taken for this instance to fully init",
                                         "/inittime",
                                         inittime_callback,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software init time");
            return result;
        }

        result = zpath_debug_add_read_command("Get software start time",
                                         "/starttime",
                                         zpath_app_starttime,
                                         NULL,
                                         "epoch", "Specify as anything to return start epoch",
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
            return result;
        }

        result = zpath_debug_app_stats();
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize app stats");
            return ZPATH_RESULT_ERR;
        }
    }

    result = zpath_app_shutdown_notice(collection_shutdown, NULL);
    if (result) {
        fprintf(stderr, "Err: Could not initialize shutdown procedure\n");
        return ZPATH_RESULT_ERR;
    }

    /* For posterity, log the versions of our libraries. */
    //ZPATH_LOG(AL_NOTICE, "AVL version: %s", avl_version());
    //ZPATH_LOG(AL_NOTICE, "ARGO version: %s", argo_version());
    //ZPATH_LOG(AL_NOTICE, "FOHH version: %s", fohh_version());
    //ZPATH_LOG(AL_NOTICE, "WALLY version: %s", wally_version());
    //ZPATH_LOG(AL_NOTICE, "ZPATH_LIB version: %s", zpath_lib_version());

    fohh_set_debugcmd_pool_support(params->debugcmd_pool_support);

    /* Get FOHH initialized and running. */
    result = fohh_init(params->fohh_thread_count,
                       (!params->root_cert_file &&
                        !params->cert_chain_file &&
                        !params->private_key_file),
                       NULL,
                       params->root_cert_file,
                       params->cert_chain_file,
                       params->private_key_file,
                       NULL,
                       1,
                       params->fohh_watchdog_s,
                       0);
    if (result) {
        if (result == FOHH_RESULT_BAD_ARGUMENT) {
            ZPATH_LOG(AL_ERROR, "fohh_init returned %s: Perhaps bad path/filename for certificates or keys?(%s, %s, %s)",
                      zpath_result_string(result),
                      params->root_cert_file,
                      params->cert_chain_file,
                      params->private_key_file);
        } else {
            ZPATH_LOG(AL_ERROR, "fohh_init returned %s", zpath_result_string(result));
        }
        return result;
    }

    //ZPATH_LOG(AL_NOTICE, "Initialized with %d worker threads.", params->fohh_thread_count);

    result = zpath_debug_init("event_log", "stats_log", params->debug_port);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize zpath_debug");
        return ZPATH_RESULT_ERR;
    }

    if (params->debug_port) {
        result = fohh_install_ip_trigger_callback(zpath_debug_catch_ip,
                                                  zpath_debug_uncatch_ip);
        if (result) {
            ZPATH_LOG(AL_ERROR, "fohh_init could not install IP trigger callback");
            return result;
        }

        result = zpath_debug_add_read_command("Check if application is initialized and running",
                                         "/app/status",
                                         simple_app_status_callback,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add status debug command");
            return result;
        }


        result = zpath_debug_add_admin_command("Tell application to shutdown. Much like SIG_TERM",
                                         "/app/shutdown",
                                         service_shutdown_callback,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add shutdown command");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with abort",
                                         "/app/crash/abort",
                                         debug_crash_abort,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/abort");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with heartbeat",
                                         "/app/crash/heartbeat",
                                         debug_crash_heartbeat,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/heartbeat");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with fortify source",
                                         "/app/crash/fortify",
                                         debug_crash_fortify,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/fortify");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with stack smash",
                                         "/app/crash/stack_smash",
                                         debug_crash_stack_smash,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/stack_smash");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with segfault",
                                         "/app/crash/segfault",
                                         debug_crash_segfault,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/segfault");
            return result;
        }

        result = zpath_debug_add_admin_command("System exit",
                                         "/system/ungraceful_shutdown",
                                         zpn_system_ungraceful_shutdown,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /system/ungraceful_shutdown");
            return result;
        }

        result = sanitizer_add_debug_commands();
        if (result)
            return result;
    }

    if (params->load_zpa_cloud_config){
        zpath_init_cloud_config();
        result = zpath_load_zpa_cloud_config(ZPA_CLOUD_CONFIG_FILE_FULL_PATH);
        if (ZPATH_RESULT_NO_ERROR != result) {
            ZPATH_LOG(AL_ERROR, "Could not load zpa_cloud_config");
            return result;
        } else {
            ZPATH_LOG(AL_NOTICE,"Successfully loaded ZPA Cloud Config");
        }
    }

    //ZPATH_LOG(AL_NOTICE, "Simple app init complete.");

    return ZPATH_RESULT_NO_ERROR;
}

void zpath_app_set_specific_max_logging_mb(int max_logging_mb)
{
    if (max_logging_mb > 0)
        zpath_app_specific_max_logging_mb = max_logging_mb;
}

void zpath_simple_app_init_params_default(struct zpath_simple_app_init_params *params)
{
    if (params) {
        memset(params, 0, sizeof(*params));
        params->role_version_string = ZPATH_VERSION;
        params->fohh_thread_count = 4;
        params->fohh_watchdog_s = 20;
        params->log_stderr = 1;
        params->log_syslog = 1;
        params->dump_lib_versions = 1;
        params->fips_mode = 1;
        params->load_zpa_cloud_config = 1;
        params->rbac_support = 0;   /* no RBAC support unless configured */
    }
}

void zpath_app_init_params_default(struct zpath_app_init_params *params)
{
    if (params) {
        memset(params, 0, sizeof(*params));
        params->local_db_hostname = "localhost";
        params->local_db_name = "local_db";
        params->role_version_string = ZPATH_VERSION;
        params->fohh_thread_count = 4;
        params->fohh_watchdog_s = 20;
        params->system_start_us = epoch_us();
        params->is_endpoint = 1;
        params->fips_mode = 1;
    }
}
