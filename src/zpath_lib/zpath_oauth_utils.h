/*
 * zpath_oauth_utils.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#define OAUTH_ENROLL_DISABLE_FLAG ".oauth_enroll_disable"
#define OAUTH_SHA256_HASH_SIZE 32
#define OAUTH_RESPONSE_SUCCESS 200
#define OAUTH_RESPONSE_PENDING 206
#define OAUTH_RESPONSE_TOKEN_EXPIRED 401
#define OAUTH_RESPONSE_SUCCESS 200
#define OAUTH_MAX_DEVICE_AUTH_REQUESTS 5
#define OAUTH_MAX_DEVICE_TOKEN_REQUESTS 5
#define OAUTH_DEFAULT_TOKEN_EXPIRY 3600
#define OAUTH_DEFAULT_RETRY_INTERVAL 5

int is_oauth_enrollment_disabled();

int oauth_get_challenge(char *oauth_challenge, size_t size);

/*
 * Compute hash of string and return hex value
 */
int oauth_get_challenge_hash(const char *input, size_t input_len, char *output, size_t output_len);
