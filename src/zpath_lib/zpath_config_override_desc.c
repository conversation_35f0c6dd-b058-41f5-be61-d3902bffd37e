/*
 * zpath_config_override_desc.c. Copyright (C) 2023 Zscaler Inc, All Rights Reserved
 */
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_misc/zpath_misc.h"

#define zpath_config_override_desc_read_lock() ZPATH_RWLOCK_RDLOCK(&(zpath_config_override_desc_state.rwlock), __FILE__, __LINE__)
#define zpath_config_override_desc_write_lock() ZPATH_RWLOCK_WRLOCK(&(zpath_config_override_desc_state.rwlock), __FILE__, __LINE__)
#define zpath_config_override_desc_unlock() ZPATH_RWLOCK_UNLOCK(&(zpath_config_override_desc_state.rwlock), __FILE__, __LINE__)

//Based the longest value type "string\0"
#define zpath_config_override_max_value_type_str 7


//Based on combining all component_types "wally,broker,pbroker,appc,exporter,slogger,cdispatcher,ipars,l4proxy,uatu,ot-broker,ot-exporter,\0"
//extra space for trailing comma simplifies formatting
#define zpath_config_override_max_component_types_str 113

//Based on combining all target_gid_types "global,zsroot,instance,instance-group,customer,partition,domain,\0"
//extra space for trailing comma simplifies formatting
#define zpath_config_override_max_target_gid_types_str 100

static struct {
    zpath_rwlock_t rwlock;
    struct zhash_table *map_key_to_desc;
    size_t registration_count;
    enum zpath_config_override_component_type sys_component_type;
} zpath_config_override_desc_state;

enum debug_filter_type {
    debug_filter_type_all,
    debug_filter_type_component_type,
};

//@formatter:off
static const char *const trait_strs[] = {
    [config_value_traits_invalid] = "INVALID",
    [config_value_traits_feature_enablement] = "FEATURE_ENABLEMENT",
    [config_value_traits_systemwide] = "SYSTEMWIDE",
    [config_value_traits_normal] = "NORMAL"
};
//@formatter:on

//region prototypes
void
zpath_config_override_desc_val_type_decode(struct zpath_config_override_desc *desc, char *output, size_t output_len);

void zpath_config_override_val_type_decode(enum config_value_type value_type, char *output, size_t output_len);

const char *zpath_config_override_desc_traits_decode(enum config_value_traits trait);
//endregion prototypes

int zpath_config_override_desc_matches_sys_type(struct zpath_config_override_desc *desc) {
    return ((desc->component_types & zpath_config_override_desc_state.sys_component_type) != 0);
}

/*
 * Pre: locking must be handled before calling to ensure consistent locking behavior regardless across multiple access types
 */
struct zpath_config_override_desc *zpath_config_override_desc_get_internal(const char *key) {
    if (zpath_config_override_desc_state.map_key_to_desc) {
        return zhash_table_lookup(zpath_config_override_desc_state.map_key_to_desc, key, strlen(key), NULL);
    } else {
        ZPATH_LOG(AL_CRITICAL, "Requested key %s before config override registrations were initialized", key);
        return NULL;
    }
}

struct zpath_config_override_desc *
zpath_config_override_desc_get(const char *key, enum config_value_type request_val_type, int enforce_type) {
    zpath_config_override_desc_read_lock();
    struct zpath_config_override_desc *res = zpath_config_override_desc_get_internal(key);
    zpath_config_override_desc_unlock();

    if (enforce_type && res && res->val_type != request_val_type) {
        char res_value_type_str[zpath_config_override_max_value_type_str];
        zpath_config_override_desc_val_type_decode(res, res_value_type_str, sizeof(res_value_type_str));

        char req_value_type_str[zpath_config_override_max_value_type_str];
        zpath_config_override_val_type_decode(request_val_type, req_value_type_str, sizeof(req_value_type_str));

        ZPATH_LOG(AL_CRITICAL,
                  "%s: requested as %s but is %s invalid request returning NULL",
                  key,
                  res_value_type_str,
                  req_value_type_str);
        return NULL;
    }
    return res;
}

struct zpath_config_override_desc *
zpath_config_override_desc_get_int_description(const char *key) {
    return zpath_config_override_desc_get(key, config_type_int, 1);
}

struct zpath_config_override_desc *
zpath_config_override_desc_get_str_description(const char *key) {
    return zpath_config_override_desc_get(key, config_type_str, 1);
}

enum config_value_type zpath_config_override_desc_get_description_type(const char *key) {
    struct zpath_config_override_desc *desc = zpath_config_override_desc_get(key, config_type_missing, 0);
    if (!desc) {
        CONFIG_OVERRIDE_MISCONFIG_LOG("No description for %s so cannot get its type", key);
        return config_type_missing;
    }

    return desc->val_type;
}

int zpath_config_override_desc_int_description_valid(struct zpath_config_override_desc *desc) {
    if ((desc->int_range_hi < desc->int_range_lo) ||
        (desc->int_range_lo == desc->int_range_hi)) {
        CONFIG_OVERRIDE_MISCONFIG_LOG("%s has invalid range [%"PRId64"-%"PRId64"]",
                                      desc->key,
                                      desc->int_range_lo,
                                      desc->int_range_hi);
        return 0;
    }

    if ((desc->int_default < desc->int_range_lo) ||
        (desc->int_range_hi < desc->int_default)) {
        CONFIG_OVERRIDE_MISCONFIG_LOG("%s Default (%"PRId64") is not contained in range defined range [%"PRId64"-%"PRId64"]",
                                      desc->key,
                                      desc->int_default,
                                      desc->int_range_lo,
                                      desc->int_range_hi);
        return 0;
    }

    switch (desc->value_traits) {
        case config_value_traits_feature_enablement:
        case config_value_traits_systemwide:
            if (desc->int_range_lo != 0 || desc->int_range_hi != 1) {
                CONFIG_OVERRIDE_MISCONFIG_LOG("%s: %s overrides must be binary with range [0,1] this key has range [%"PRId64", %"PRId64"]",
                                              desc->key,
                                              zpath_config_override_desc_traits_decode(desc->value_traits),
                                              desc->int_range_lo,
                                              desc->int_range_hi);
            }
        case config_value_traits_normal:
            break;
        default:
            CONFIG_OVERRIDE_MISCONFIG_LOG("%s value_traits were not configured properly for an integer key - this must be configured",
                                          desc->key);
    }

    return 1;
}

int zpath_config_override_desc_str_description_valid(struct zpath_config_override_desc *desc) {
    if (desc->str_valid_cb && (!desc->str_valid_cb_name || desc->str_valid_cb_name[0] == '\0')) {
        CONFIG_OVERRIDE_MISCONFIG_LOG(
                "%s string description with a validator cb must also set the name please use ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB macro",
                desc->key);
        return 0;
    }

    return 1;
}

/*
 * Pre: key and desc must already be validated before coming in this method is for inertion of good values
 *      Locking should be setup ahead of time to ensure that consistent unlocking is done regardless of hashtable state
 */
int zpath_config_override_desc_register_internal(struct zpath_config_override_desc *desc) {
    struct zpath_config_override_desc *existing_entry = zpath_config_override_desc_get_internal(desc->key);
    if (existing_entry) {
        CONFIG_OVERRIDE_MISCONFIG_LOG("%s was already registered with config override", desc->key);
        return ZPATH_RESULT_ERR;
    }

    if (zpath_config_override_desc_state.map_key_to_desc) {
        zhash_table_store(zpath_config_override_desc_state.map_key_to_desc,
                          (void *) desc->key,
                          strlen(desc->key),
                          1,
                          desc);
        zpath_config_override_desc_state.registration_count++;
    } else {
        ZPATH_LOG(AL_CRITICAL,
                  "Requested to register %s before the config override registrations were initialized",
                  desc->key);
        return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;

}

int zpath_config_override_desc_register(struct zpath_config_override_desc *desc) {
    if (!desc) {
        ZPATH_LOG(AL_ERROR, "No config override description provided to registration function");
        return ZPATH_RESULT_ERR;
    }

    if (!zpath_config_override_desc_state.map_key_to_desc) {
        ZPATH_LOG(AL_ERROR, "Config override registration called before override was initialized...");
        return ZPATH_RESULT_ERR;
    }

    if (!desc->key || desc->key[0] == '\0') {
        ZPATH_LOG(AL_ERROR, "No key provided to config override description registration... ignoring");
        return ZPATH_RESULT_ERR;
    }

    if (!zpath_config_override_desc_matches_sys_type(desc)) {
        CONFIG_OVERRIDE_MISCONFIG_LOG("%s Config override registered but is not marked as used by current system type",
                                      desc->key);
        //we will let it register for now
    }

    if(!desc->feature_group || desc->feature_group[0] == '\0') {
        CONFIG_OVERRIDE_MISCONFIG_LOG("%s Config override registered but does not contain a feature group",
                                      desc->key);
    }

    switch (desc->val_type) {
        case config_type_int:
            if (!zpath_config_override_desc_int_description_valid(desc)) return ZPATH_RESULT_BAD_ARGUMENT;
            break;
        case config_type_str:
            if (!zpath_config_override_desc_str_description_valid(desc)) return ZPATH_RESULT_BAD_ARGUMENT;
            break;
        default:
            ZPATH_LOG(AL_CRITICAL, "%s tried to register with invalid type %u", desc->key, desc->val_type);
            return ZPATH_RESULT_BAD_ARGUMENT;
    }

    zpath_config_override_desc_write_lock();
    int res = zpath_config_override_desc_register_internal(desc);
    zpath_config_override_desc_unlock();

    return res;
}

// Will manually escape control characters to avoid dumping bad output
static inline void zpath_config_override_debug_dump_string_safe_slow(struct zpath_debug_state *request_state, const char *str, size_t strlen){
    //Print the char 1 at a time but if it is a control character grab the escaped version
    for (int i = 0; i < strlen; i++) {
        switch (str[i]) {
            // So far these are the only control characters we really care about right now
            case '\n':
                ZDP("%s", "\\n");
                break;
            case '\t':
                ZDP("%s", "\\t");
                break;
            default:
                ZDP("%c", str[i]);
        }
    }
}

static inline void zpath_config_override_debug_dump_string_safe_fast(struct zpath_debug_state *request_state, const char *str, size_t strlen){
    ZDP("%s", str);
}

static void zpath_config_override_debug_dump_string_safe(struct zpath_debug_state *request_state, const char *str, size_t strlen){
    int has_control_chars = 0;

    //Let's scan for a control character
    //This is meant to be optimal for integration with ZDP
    //However, it's possible that it's better to just *always* iterate and escape...
    //This isn't time-critical code though, so I will just run with this assumption for now
    for (int i = 0; i < strlen; i++) {
        if(iscntrl(str[i])){
            has_control_chars = 1;
            break;
        }
    }

    if (has_control_chars) {
        zpath_config_override_debug_dump_string_safe_slow(request_state, str, strlen);
    } else {
        zpath_config_override_debug_dump_string_safe_fast(request_state, str, strlen);
    }
}


//region Debugging Decoders
void zpath_config_override_val_type_decode(enum config_value_type value_type, char *output, size_t output_len) {
    switch (value_type) {
        case config_type_int:
            sxprintf(output, output + output_len, "int");
            break;
        case config_type_str:
            sxprintf(output, output + output_len, "string");
            break;
        default:
            sxprintf(output, output + output_len, "other");
            break;
    }
}


void
zpath_config_override_desc_val_type_decode(struct zpath_config_override_desc *desc, char *output, size_t output_len) {
    zpath_config_override_val_type_decode(desc->val_type, output, output_len);
}

const char *zpath_config_override_desc_traits_decode(enum config_value_traits trait) {
    if (trait < config_value_traits_invalid || trait >= config_value_traits_max) {
        return trait_strs[config_value_traits_invalid];
    }

    return trait_strs[trait];
}

void zpath_config_override_desc_component_type_decode(struct zpath_config_override_desc *desc,
                                                      char *output,
                                                      size_t output_len) {
    char *s = output;
    char *e = output + output_len;

    if (desc->component_types & config_component_wally) {
        s += sxprintf(s, e, "wally,");
    }

    if (desc->component_types & config_component_broker) {
        s += sxprintf(s, e, "broker,");
    }

    if (desc->component_types & config_component_pbroker) {
        s += sxprintf(s, e, "pbroker,");
    }

    if (desc->component_types & config_component_appc) {
        s += sxprintf(s, e, "appc,");
    }

    if (desc->component_types & config_component_exporter) {
        s += sxprintf(s, e, "exporter,");
    }

    if (desc->component_types & config_component_slogger) {
        s += sxprintf(s, e, "slogger,");
    }

    if (desc->component_types & config_component_cdispatcher) {
        s += sxprintf(s, e, "cdispatcher,");
    }

    if (desc->component_types & config_component_ipars) {
        s += sxprintf(s, e, "ipars,");
    }

    if (desc->component_types & config_component_l4proxy) {
        s += sxprintf(s, e, "l4proxy,");
    }

    if (desc->component_types & config_component_uatu) {
        s += sxprintf(s, e, "uatu,");
    }

    if (desc->component_types & config_component_ot_broker) {
        s += sxprintf(s, e, "ot-broker,");
    }

    if (desc->component_types & config_component_ot_exporter) {
        s += sxprintf(s, e, "ot-exporter,");
    }

    if (desc->component_types & config_component_sitec) {
        s += sxprintf(s, e, "site-controller,");
    }

    if (desc->component_types & config_component_np_gateway) {
        s += sxprintf(s, e, "np-gateway,");
    }

    if (s == output) {
        //no component was found...
        sxprintf(s, e, "other");
    } else {
        //remove trailing comma
        *(s - 1) = '\0';
    }
}

void zpath_config_override_desc_target_types_decode(struct zpath_config_override_desc *desc,
                                                    char *output,
                                                    size_t output_len) {
    char *s = output;
    char *e = output + output_len;

    if (desc->target_gid_types & config_target_gid_type_global) {
        s += sxprintf(s, e, "global,");
    }

    if (desc->target_gid_types & config_target_gid_type_zsroot) {
        s += sxprintf(s, e, "zsroot,");
    }

    if (desc->target_gid_types & config_target_gid_type_inst) {
        s += sxprintf(s, e, "instance,");
    }

    if (desc->target_gid_types & config_target_gid_type_inst_grp) {
        s += sxprintf(s, e, "instance-group,");
    }

    if (desc->target_gid_types & config_target_gid_type_cust) {
        s += sxprintf(s, e, "customer,");
    }

    if (desc->target_gid_types & config_target_gid_type_partition_cust) {
        s += sxprintf(s, e, "customer-partition,");
    }

    if (desc->target_gid_types & config_target_gid_type_partition_inst) {
        s += sxprintf(s, e, "instance-partition,");
    }

    if (desc->target_gid_types & config_target_gid_type_domain) {
        s += sxprintf(s, e, "domain,");
    }

    if (s == output) {
        //no component was found...
        sxprintf(s, e, "other");
    } else {
        //remove trailing comma
        *(s - 1) = '\0';
    }
}
//endregion Decoders

static void zpath_config_override_debug_dump_description(struct zpath_debug_state *request_state,
                                                         struct zpath_config_override_desc *desc) {
    char value_type[zpath_config_override_max_value_type_str] = {'\0'};
    zpath_config_override_desc_val_type_decode(desc, value_type, sizeof(value_type));

    char component_types[zpath_config_override_max_component_types_str] = {'\0'};
    zpath_config_override_desc_component_type_decode(desc, component_types, sizeof(component_types));

    char target_types[zpath_config_override_max_target_gid_types_str] = {'\0'};
    zpath_config_override_desc_target_types_decode(desc, target_types, sizeof(target_types));

    ZDP("%s\n"
        "Project Name: \"%s\""
        "%s\n"
        "\n%s\n\n"
        "value type:%s\n"
        "component_types: %s\n"
        "target types: %s\n",
        desc->key,
        (desc->feature_group && desc->feature_group[0] != '\0' ? desc->feature_group
                                                               : "MISCONFIGURED"),
        desc->desc,
        desc->details,
        value_type,
        component_types,
        target_types);

    switch (desc->val_type) {
        case config_type_int:
            ZDP("range: [%"PRId64",%"PRId64"] (default: %"PRId64")\n",
                desc->int_range_lo,
                desc->int_range_hi,
                desc->int_default);
            ZDP("Value Traits: %d\n", desc->value_traits);
            break;
        case config_type_str:
            ZDP("Default: ");
            if (desc->str_default && desc->str_default[0] != '\0') {
                zpath_config_override_debug_dump_string_safe_slow(request_state,
                                                                  desc->str_default,
                                                                  strlen(desc->str_default));
            }  else {
                ZDP("None");
            }
            ZDP("\nValidated By: %s\n", (desc->str_valid_cb ? desc->str_valid_cb_name : "None"));
            ZDP("Value Traits: %d\n", desc->value_traits);
            break;
        default:
            ZDP("No type to validate\n");
            break;

    }
    ZDP("---\n");
}

static void zpath_config_override_debug_dump_small_postamble(struct zpath_debug_state *request_state,
                                                             struct zpath_config_override_desc *desc) {
    //print the feature group
    ZDP("\t");
    if (desc->feature_group && desc->feature_group[0] != '\0') {
        ZDP("%s", desc->feature_group);
    } else {
        ZDP("%s", "NO_FEATURE_GROUP");
    }

    //Print Description with escapes when necessary
    ZDP("\t");
    if (desc->desc && desc->desc[0] != '\0') {
        zpath_config_override_debug_dump_string_safe(request_state, desc->desc, strlen(desc->desc));
    }

    ZDP("\n");
}

static void zpath_config_override_debug_dump_small(struct zpath_debug_state *request_state,
                                                   struct zpath_config_override_desc *desc) {

    //key\ttype\tint_min\tint_max\tint_default\tvalue_traits\tstr_default\tstr_validator\tfeature_group\tdescription
    switch (desc->val_type) {
        case config_type_int:
            ZDP("%s\tint\t%"PRId64"\t%"PRId64"\t%"PRId64"\t%d\t\t",
                desc->key,
                desc->int_range_lo,
                desc->int_range_hi,
                desc->int_default,
                desc->value_traits);
            zpath_config_override_debug_dump_small_postamble(request_state, desc);
            break;
        case config_type_str:
            ZDP("%s\tstr\t\t\t\t%d", desc->key, desc->value_traits);

            //Print Str Default with escapes when necessary
            ZDP("\t");
            if (desc->str_default && desc->str_default[0] != '\0') {
                zpath_config_override_debug_dump_string_safe(request_state,
                                                             desc->str_default,
                                                             strlen(desc->str_default));
            } else {
                ZDP("None");
            }

            ZDP("\t%s",
                (desc->str_valid_cb_name && desc->str_valid_cb_name[0] != '\0' ? desc->str_valid_cb_name : "None"));

            zpath_config_override_debug_dump_small_postamble(request_state, desc);
            break;
        default:
            break;
    }
}

int dump_verbose_description_walk_f(void *cookie, void *object, void *key, size_t key_len) {
    if (object) {
        zpath_config_override_debug_dump_description(cookie, object);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int dump_terse_description_walk_f(void *cookie, void *object, void *key, size_t key_len) {
    if (object) {
        zpath_config_override_debug_dump_small(cookie, object);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int dump_verbose_description_filtered_walk_f(void *cookie, void *object, void *key, size_t key_len) {
    if (object) {
        if (zpath_config_override_desc_matches_sys_type(object)) {
            zpath_config_override_debug_dump_description(cookie, object);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int dump_terse_description_filtered_walk_f(void *cookie, void *object, void *key, size_t key_len) {
    if (object) {
        if (zpath_config_override_desc_matches_sys_type(object)) {
            zpath_config_override_debug_dump_small(cookie, object);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

zhash_table_walk_f *
zpath_config_override_desc_prepare_and_get_terse_walk_function(struct zpath_debug_state *request_state,
                                                               enum debug_filter_type filter_type) {
    ZDP("key\ttype\tint_min\tint_max\tint_default\tvalue_traits\tstr_default\tstr_validator\tfeature_group\tdescription\n");
    switch (filter_type) {
        case debug_filter_type_component_type:
            return dump_terse_description_filtered_walk_f;
        case debug_filter_type_all:
        default:
            return dump_terse_description_walk_f;
    }
}

static int
zpath_config_override_desc_dump_all(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *object) {
    int64_t key = 0;

    if (!zpath_config_override_desc_state.map_key_to_desc) {
        ZDP("Library is not initialized yet so nothing to dump\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    zhash_table_walk_f *walk_function = dump_verbose_description_walk_f;

    if (query_values[0]) {
        walk_function = zpath_config_override_desc_prepare_and_get_terse_walk_function(request_state,
                                                                                       debug_filter_type_all);
    }

    zpath_config_override_desc_read_lock();
    zhash_table_walk(zpath_config_override_desc_state.map_key_to_desc,
                     &key,
                     walk_function,
                     request_state);
    zpath_config_override_desc_unlock();

    ZDP("---\nTotal Registered Config Overrides: %zu\n", zpath_config_override_desc_state.registration_count);

    return ZPATH_RESULT_NO_ERROR;
}

static int
zpath_config_override_desc_dump_filtered(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *object) {
    int64_t key = 0;

    if (!zpath_config_override_desc_state.map_key_to_desc) {
        ZDP("Library is not initialized yet so nothing to dump\n");
        return ZPATH_RESULT_NO_ERROR;
    }


    zhash_table_walk_f *walk_function = dump_verbose_description_filtered_walk_f;

    if (query_values[0]) {
        walk_function = zpath_config_override_desc_prepare_and_get_terse_walk_function(request_state,
                                                                                       debug_filter_type_component_type);
    }

    zpath_config_override_desc_read_lock();
    zhash_table_walk(zpath_config_override_desc_state.map_key_to_desc,
                     &key,
                     walk_function,
                     request_state);
    zpath_config_override_desc_unlock();

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_config_override_desc_search(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *object) {
    if (query_values[0]) {
        struct zpath_config_override_desc *desc = zpath_config_override_desc_get(query_values[0],
                                                                                 config_type_missing,
                                                                                 0);
        if (desc) {
            zpath_config_override_debug_dump_description(request_state, desc);
        } else {
            ZDP("%s is not registered\n", query_values[0]);
        }
    } else {
        ZDP("Please provide a 'key' value\n");
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_config_override_desc_init(enum zpath_config_override_component_type sys_type) {
    ZPATH_COMPONENT_ONETIME_INIT_CHECK;

    int res = ZPATH_RESULT_NO_ERROR;

    zpath_config_override_desc_state.map_key_to_desc = zhash_table_alloc(&zpath_lib_allocator);
    zpath_config_override_desc_state.registration_count = 0;
    zpath_config_override_desc_state.rwlock = ZPATH_RWLOCK_INIT;
    zpath_config_override_desc_state.sys_component_type = sys_type;

    res = zpath_debug_add_read_command("Search for an override registration",
                                  "/zpath_config_override_descriptions/search",
                                  zpath_config_override_desc_search,
                                  NULL,
                                  "key", "The full config override key being searched",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "could not register debug command for override search");
        return res;
    }


    res = zpath_debug_add_read_command("Dump the config override registrations on this system",
                                  "/zpath_config_override_descriptions/dump/all",
                                  zpath_config_override_desc_dump_all,
                                  NULL,
                                  "small", "tsv output",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "could not register debug command for override dump all");
        return res;
    }

    res = zpath_debug_add_read_command("Dump the config override registrations marked for this system type",
                                  "/zpath_config_override_descriptions/dump",
                                  zpath_config_override_desc_dump_filtered,
                                  NULL,
                                  "small", "tsv output",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "could not register debug command for override dump");
        return res;
    }

    return res;
}
