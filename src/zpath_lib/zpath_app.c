/*
 * zpath_app.c. Copyright (C) 2013-2025 Zscaler, Inc. All Rights Reserved.
 */

#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <stdatomic.h>

#include "argo/argo_log.h"
#include "fohh/fohh.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_db.h"
#include "wally/wally_fohh_client.h"
#include "avl/avl.h"
#include "zthread/zthread.h"
#include "fohh/fohh_log.h"
#include "zevent/zevent.h"

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_sync_pause.h"
#include "zpath_lib/zpath_entity.h"
#include "zpath_lib/zpath_rule.h"
#include "zpath_lib/zpath_customer_log_config.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_domainlist.h"
#include "zpath_lib/zpath_customer_notification.h"
#include "zpath_lib/zpath_category.h"
#include "zpath_lib/zpath_ip_table.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_ip_location.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_tag_entity.h"
#include "zpath_lib/zpath_customer_logo.h"
#include "zpath_lib/zpath_location.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_config.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/sanitizer_commands.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "zpath_lib/zpath_app_compiled.h"
#include "zthread/zthread_compiled.h"
#include "zpath_lib/zpath_cidr_lookup.h"
#include "zpath_misc/zpath_misc.h"
#include "argo/argo.h"
#include "zpath_lib/zpath_app_debug.h"
#include "zpath_misc/zpath_platform.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_assert.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zhw/zhw_os.h"

#define ZPATH_CUSTOMER_TABLE        "zpath_customer"
#define ZPATH_RULE_TABLE            "zpath_rule"
#define ZPATH_SERVICE_TABLE         "zpath_service"

struct wally *zpath_local_wally;
struct wally *zpath_global_wally;
struct wally *zpath_np_global_wally;
struct wally_origin *zpath_np_global_slave_db;
struct wally_origin *zpath_np_global_remote_db;
struct wally_origin *zpath_global_slave_db;
struct wally_origin *zpath_global_remote_db;
const char *zpath_app_db_hostname = NULL;


struct wally *zpath_shard_wally[ZPATH_MAX_SHARDS];
struct wally *zpath_shard_wally_static[ZPATH_MAX_SHARDS];
static struct wally_origin *zpath_shard_slave_db[ZPATH_MAX_SHARDS];
static struct wally_origin *zpath_shard_remote_db[ZPATH_MAX_SHARDS];
static struct wally_origin *zpath_shard_slave_db_static[ZPATH_MAX_SHARDS];
static struct wally_origin *zpath_shard_remote_db_static[ZPATH_MAX_SHARDS];
struct wally *zpath_np_shard_wally[ZPATH_MAX_SHARDS];
static struct wally_origin *zpath_np_shard_slave_db[ZPATH_MAX_SHARDS];
static struct wally_origin *zpath_np_shard_remote_db[ZPATH_MAX_SHARDS];

pthread_mutex_t zpath_shard_create_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t zpath_np_shard_create_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t shutdown_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

struct argo_log_collection *zpath_event_collection;
struct argo_log_collection *zpath_stats_collection;

const char *fohh_debug_names[] = FOHH_DEBUG_NAMES;
const char *wally_debug_names[] = WALLY_DEBUG_NAMES;

int64_t zpath_service_init_complete_us = 0;
int zpath_service_init_complete = 0;
int zpath_service_shutdown = 0;
int zpath_service_shutdown_with_exit = 0;
int zpath_app_instance_id = 0;
int zpath_config_override_init_complete = 0;

/*
 * If set, controls max logging mb. Must be set BEFORE call to
 * zpath_app_init. If not set, the system automatically chooses.
 */
static int zpath_app_specific_max_logging_mb = 0;
static int zpath_app_specific_argo_mem_threshold = 0;

static enum zpath_app_shutdown_state shutdown_state = zpath_app_shutdown_can_start;

int64_t zpath_system_start_time;
extern _Atomic int64_t zthread_curr_time;

/* Indicates if role-based access control is enabled or not. */
int64_t zpath_rbac_enabled = 0;

/* indicates if role-based access control(rbac) is supported by the component or not. */
int64_t zpath_rbac_supported = 0;


/* whether or not to use ports 444, 445, 446 for shards 0, 1, 2,
 * etc */
static int incremental_shard_ports = 0;
/* whether to replicate state in a local postgres DB at all. */
static int use_slave_db = 1;

int zpath_app_is_endpoint = 0;

static int itasca_logging_port_he = 0;

static struct zthread_rusage zpath_app_stats;
static struct zpath_inittime g_inittime;

struct argo_structure_description *zthread_rusage_desc;
struct argo_structure_description *zpath_inittime_desc;

/* exit handler for broker */
int (*zpath_lib_exit_handler_cb)(enum zpath_termination_code tc, void *cookie, int self_thread_num) = NULL;

struct event *app_shutdown_timer = NULL;

struct shutdown_registrant {
    struct shutdown_registrant *next;
    zpath_app_shutdown_verify_callback *callback;
    void *cookie;
    enum zpath_app_shutdown_state state;
};

/* if the domian is valid ipv4 or IPv6 address */
int is_valid_ip(char *domain)
{
    struct argo_inet inet;
    return (domain && argo_string_to_inet(domain, &inet) == ARGO_RESULT_NO_ERROR) ? 1 : 0;
}

void zpath_registration_started(int64_t time)
{
    zpath_system_start_time = time ? time : epoch_us();
}

void zpath_registration_completed(void)
{
    zpath_service_init_complete = 1;
    zpath_service_init_complete_us = epoch_us();

    int64_t delta = (zpath_service_init_complete_us - zpath_system_start_time)/US_PER_SEC;

    ZPATH_LOG(AL_NOTICE, "Process initialization complete! Total time taken is %"PRId64"s", delta);

    /* Log init details along with platform and build tag info once every day */
    if (zpath_inittime_desc) {
        char buf[1024] = {'\0'};
        struct zpath_inittime *inittime = &g_inittime;
        if (inittime) {
            inittime->inittime_s  = delta;
            inittime->major_ver   = ZPATH_VERSION_MAJOR;
            inittime->minor_ver   = ZPATH_VERSION_MINOR;
            inittime->patch_ver   = ZPATH_VERSION_PATCH;
            snprintf(buf, sizeof(buf), "%"PRIu64"00%"PRIu64"00%"PRIu64, inittime->major_ver, inittime->minor_ver, inittime->patch_ver);
            inittime->version_tag = strtoul(buf, NULL, 10);
            inittime->build_tag   = strtoul(ZPATH_VERSION_CHASH, NULL, 16);
            inittime->arch        = strstr(ZPATH_PLATFORM_ARCH, "x86") ? 86 : 0;
            zhw_get_runtime_platform(inittime->os_version_str, (int)sizeof(inittime->os_version_str), &inittime->os_version_id);
            argo_log_register_structure(zpath_stats_collection,
                                        "zpath_inittime",
                                        AL_INFO,
                                        DAY_TO_US(1),
                                        zpath_inittime_desc,
                                        inittime,
                                        1,
                                        NULL,
                                        NULL);
        }
    }
}

static struct shutdown_registrant *shutdown_registrants = NULL;

static void zthread_log(const char *msg)
{
    ZPATH_LOG(AL_WARNING, "%s", msg);
}

static int request_atleast_once() {
    const char *role_name = zpath_app_get_role_name();
    if (role_name && !strncmp(role_name, ZPATH_APP_SITEC_INSTANCE_NAME, strlen(ZPATH_APP_SITEC_INSTANCE_NAME))) {
        return 0;
    }
    return 1;
}

const char *zpath_app_shutdown_name(enum zpath_app_shutdown_state state)
{
    if (state == zpath_app_shutdown_can_start) return "can start";
    if (state == zpath_app_shutdown_start) return "start";
    if (state == zpath_app_shutdown_can_complete) return "can complete";
    if (state == zpath_app_shutdown_complete) return "complete";
    return "invalid";
}

int zpath_app_shutdown_notice(zpath_app_shutdown_verify_callback *callback,
                              void *cookie)
{
    struct shutdown_registrant *reg;
    reg = ZLIB_CALLOC(sizeof(*reg));
    if (!reg) return ZPATH_RESULT_NO_MEMORY;
    pthread_mutex_lock(&shutdown_lock);
    reg->callback = callback;
    reg->cookie = cookie;
    reg->next = shutdown_registrants;
    shutdown_registrants = reg;
    pthread_mutex_unlock(&shutdown_lock);
    return ZPATH_RESULT_NO_ERROR;
}


static int collection_shutdown(void *cookie,
                               enum zpath_app_shutdown_state state)
{
    int64_t res;
    int64_t time_now;
    static int64_t time_end = 0;
    (void)cookie; //unused

    ZPATH_LOG(AL_NOTICE, "Collection snapshot: %s", zpath_app_shutdown_name(state));
    switch (state) {

    case zpath_app_shutdown_can_start:
        return ZPATH_RESULT_NO_ERROR;

    case zpath_app_shutdown_start:
        res = argo_collections_snapshot();
        if (res) {
            ZPATH_LOG(AL_ERROR, "Collection snapshotting returned %s", zpath_result_string(res));
        }
        return res;

    case zpath_app_shutdown_can_complete:
        time_now = epoch_us();
        if (!time_end) time_end = time_now + (5*1000*1000); //5s wait
        res = argo_collections_verify_snapshot();
        if (res) {
            if (time_now < time_end) {
                ZPATH_LOG(AL_NOTICE, "Collection snapshotting returned %ld objects still to log", (long) res);
                return ZPATH_RESULT_NOT_READY;
            } else {
                ZPATH_LOG(AL_NOTICE, "Collection shutdown purge timer triggered. Purging all collections");
                (void )argo_log_purge_collections(1, NULL, 0);
                return ZPATH_RESULT_NO_ERROR;
            }
        }
        return ZPATH_RESULT_NO_ERROR;

    case zpath_app_shutdown_complete:
        return ZPATH_RESULT_NO_ERROR;

    default:
        ZPATH_LOG(AL_ERROR, "Bad state");
        return ZPATH_RESULT_ERR;
    }
}


/*
 * Get the status of the running app.
 */
static int status_callback(struct zpath_debug_state *request_state,
                          const char **query_values,
                          int query_value_count,
                          void *cookie)
{
    if (zpath_service_shutdown_with_exit) {
        zpath_debug_cb_printf_response(request_state, "stopping\n");
    } else if (zpath_service_init_complete) {
        if (zpath_instance_global_state.current_config->active &&
            zpath_instance_global_state.current_config->run) {
            zpath_debug_cb_printf_response(request_state, "running\n");
        } else {
            zpath_debug_cb_printf_response(request_state, "standby\n");
        }
    } else {
        zpath_debug_cb_printf_response(request_state, "initializing\n");
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int simple_app_status_callback(struct zpath_debug_state *request_state,
                          const char **query_values,
                          int query_value_count,
                          void *cookie)
{
    if (zpath_service_init_complete) {
        zpath_debug_cb_printf_response(request_state, "running\n");
    } else if (zpath_service_shutdown) {
        zpath_debug_cb_printf_response(request_state, "stopping\n");
    } else {
        zpath_debug_cb_printf_response(request_state, "initializing\n");
    }
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * zpath_app_shutdown_delayed_callback
 *  Pass this fn as callback when we only need shutdown to be done
 */
void zpath_app_shutdown_delayed_callback(int sock, short flags, void *cookie)
{
    if (app_shutdown_timer) {
        event_free(app_shutdown_timer);
        app_shutdown_timer = NULL;
    }
    ZPATH_LOG(AL_NOTICE, "Shutdown timer expired...Invoking app_shutdown");
    zpath_app_shutdown(-1, 1);
}

int zpath_app_shutdown_delayed_internal(uint16_t delay)
{
    struct timeval tv;
    int res = ZPATH_RESULT_NO_ERROR;

    /* Create a timer that fires once */
    app_shutdown_timer = event_new(zpath_debug_get_base(),
                         -1,
                         0,
                         zpath_app_shutdown_delayed_callback,
                         NULL);

    if (!app_shutdown_timer) {
        ZPATH_LOG(AL_ERROR, "Could not create shutdown timer");
        return ZPATH_RESULT_ERR;
    }

    tv.tv_sec = delay;
    tv.tv_usec = 0;
    if (event_add(app_shutdown_timer, &tv)) {
        ZPATH_LOG(AL_ERROR, "Could not activate shutdown timer");
        return ZPATH_RESULT_ERR;
    }
    return res;
}

static inline int zpath_app_shutdown_calculate_delay(uint16_t min_delay, uint16_t max_delay)
{
    return ((rand() % (max_delay - min_delay + 1)) + min_delay);
}

int zpath_app_shutdown(int64_t timeout_s, int exit_proc)
{
    struct shutdown_registrant *reg;
    int waiting = 0;
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t end_s = timeout_s > 0 ? (epoch_s() + timeout_s) : -1;

    zpath_service_shutdown = 1;

    zthread_curr_time = epoch_us();

    /* set this flag to indicate we are exiting after app/shutdown */
    if (exit_proc)
        zpath_service_shutdown_with_exit = 1;

    pthread_mutex_lock(&shutdown_lock);

    zthread_set_abort(0);

    while (1) {
        for (; shutdown_state < zpath_app_shutdown_complete; shutdown_state++) {
            fprintf(stderr, "Service shutdown: %s\n", zpath_app_shutdown_name(shutdown_state));
            waiting = 0;
            for (reg = shutdown_registrants; reg; reg = reg->next) {
                if (reg->state == shutdown_state) {
                    res = (reg->callback)(reg->cookie, reg->state);
                    if (res) {
                        waiting = 1;
                    } else {
                        reg->state++;
                    }
                }
            }
            if (waiting) break;
        }
        if (shutdown_state == zpath_app_shutdown_complete) break;
        if (end_s > 0 && epoch_s() > end_s) {
            res = ZPATH_RESULT_INCOMPLETE;
            break;
        }
        usleep(100000);
    }
    pthread_mutex_unlock(&shutdown_lock);

    zthread_curr_time = epoch_us();

    if (exit_proc) {
        fprintf(stdout,"ERROR: Service shutdown: %s\n", zpath_app_shutdown_name(shutdown_state));

        /*
         * if no cb configured, exit immediately; else invoke callback, if needed.
         *  If exit path is scheduled one of the threads marked as 'dont kill on terminate'; we follow normal exit path
         *  This is because, in the func. ptr, we will be doing further processing on this thread.
         *      This can result in this thread stuck or stacks getting modified; which we want to avoid.
         */
        if (zpath_lib_exit_handler_cb) {
            int self_thread_id = zthread_get_self_thread_id();
            if (zthread_get_dont_kill_on_terminate_flag(self_thread_id)) {
                exit(0);
            } else {
                zpath_lib_exit_handler_cb(zpath_tc_lib_app_shutdown, NULL, self_thread_id);
            }
        } else {
            exit(0);
        }
    }
    zpath_service_shutdown = 0;
    zpath_service_shutdown_with_exit = 0;
    return res;
}

static int timeout_safe_guard(struct zpath_debug_state *request_state, const char* value, int low, int high)
{
    if (!value)
        return ZPATH_RESULT_NO_ERROR; /* will take default */
    int v = atoi(value);
    if (v < low || v > high) {
        ZDP("Invalid value %s, allowed [%d - %d]\n", value, low, high);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    return ZPATH_RESULT_NO_ERROR;
}
/*
 * Shut down the system- debug command.
 */
static int service_shutdown_callback(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    uint16_t min_delay = 0, max_delay = 0;
    int delay_configured = 0;
    uint16_t delay = 0;

    /* Parameter validation */
    if (timeout_safe_guard(request_state, query_values[0], 1, 14400) ||
        timeout_safe_guard(request_state, query_values[1], 1, 14400)) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    /* Both need to be configured or none configured */
    if ((query_values[0] && !query_values[1]) ||
        (!query_values[0] && query_values[1])) {
        ZDP("Both min and max delays need to be specified!\n");
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    if (query_values[0] && query_values[1]) {
        min_delay = atoi(query_values[0]);
        max_delay = atoi(query_values[1]);

        /* validate min <= max */
        if (min_delay > max_delay) {
            ZDP("Expected Min delay(%u) <= Max delay(%u)!\n", min_delay, max_delay);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        delay_configured = 1;
    }

    /* See if a shutdown is already scheduled */
    if (app_shutdown_timer) {
        zpath_debug_cb_printf_response(request_state, "Shutdown has already been scheduled!\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (delay_configured) {
        delay = zpath_app_shutdown_calculate_delay(min_delay, max_delay);
        ZPATH_LOG(AL_NOTICE, "Triggering app/shutdown in %u sec range: (%u/%u)",
                                delay, min_delay, max_delay);
        zpath_debug_cb_printf_response(request_state, "Shutting down in %d seconds\n", delay);
        zpath_app_shutdown_delayed_internal(delay);
    } else {
        zpath_debug_cb_printf_response(request_state, "Shutting down\n");
        zpath_app_shutdown(-1, 1);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static inline int zpath_crash_ungraceful_shutdown()
{
    exit(2);
    return ZPATH_RESULT_NO_ERROR;
}
/*
 * Shut down the system- debug command.
 */
static int zpn_system_ungraceful_shutdown(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    zpath_debug_cb_printf_response(request_state, "ERROR: Shutting down per user request\n");
    sleep(1);

    /*
     * if no cb configured, exit immediately; else invoke callback, if needed.
     *  If exit path is scheduled one of the threads marked as 'dont kill on terminate'; we follow normal exit path
     *  This is because, in the func. ptr, we will be doing further processing on this thread.
     *      This can result in this thread stuck or stacks getting modified; which we want to avoid.
     */
    if (zpath_lib_exit_handler_cb) {
        int self_thread_id = zthread_get_self_thread_id();
        if (zthread_get_dont_kill_on_terminate_flag(self_thread_id)) {
            return zpath_crash_ungraceful_shutdown();
        } else {
            return zpath_lib_exit_handler_cb(zpath_tc_lib_ungraceful_shutdown, NULL, self_thread_id);
        }
    } else {
        return zpath_crash_ungraceful_shutdown();
    }
}

/*
 *  Return service IPs
 */
static int service_ip_callback(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie)
{
    struct zpath_instance *inst = zpath_instance_global_state.current_config;
    char ip_str[ARGO_INET_ADDRSTRLEN];
    int i;

    zpath_debug_cb_printf_response(request_state, "[");
    if (inst) {
        for (i = 0; i < inst->ips_count; i++) {
            argo_inet_generate(ip_str, &(inst->ips[i]));
            if (i) {
                zpath_debug_cb_printf_response(request_state, ",\"%s\"", &(ip_str[0]));
            } else {
                zpath_debug_cb_printf_response(request_state, "\"%s\"", &(ip_str[0]));
            }
        }
    }
    zpath_debug_cb_printf_response(request_state, "]\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int service_ip_callback_v4(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    struct zpath_instance *inst = zpath_instance_global_state.current_config;
    char ip_str[ARGO_INET_ADDRSTRLEN];
    int i;
    int wrote_one = 0;

    zpath_debug_cb_printf_response(request_state, "[");
    if (inst) {
        for (i = 0; i < inst->ips_count; i++) {
            if (inst->ips[i].length == 4) {
                argo_inet_generate(ip_str, &(inst->ips[i]));
                if (wrote_one) {
                    zpath_debug_cb_printf_response(request_state, ",\"%s\"", &(ip_str[0]));
                } else {
                    zpath_debug_cb_printf_response(request_state, "\"%s\"", &(ip_str[0]));
                }
                wrote_one = 1;
            }
        }
    }
    zpath_debug_cb_printf_response(request_state, "]\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int service_ip_callback_v6(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    struct zpath_instance *inst = zpath_instance_global_state.current_config;
    char ip_str[ARGO_INET_ADDRSTRLEN];
    int i;
    int wrote_one = 0;

    zpath_debug_cb_printf_response(request_state, "[");
    if (inst) {
        for (i = 0; i < inst->ips_count; i++) {
            if (inst->ips[i].length == 16) {
                argo_inet_generate(ip_str, &(inst->ips[i]));
                if (wrote_one) {
                    zpath_debug_cb_printf_response(request_state, ",\"%s\"", &(ip_str[0]));
                } else {
                    zpath_debug_cb_printf_response(request_state, "\"%s\"", &(ip_str[0]));
                }
                wrote_one = 1;
            }
        }
    }
    zpath_debug_cb_printf_response(request_state, "]\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_argo_structure_dump_dynamic_fields(struct zpath_debug_state *request_state,
                                                    const char **query_values,
                                                    int query_value_count,
                                                    void *cookie)
{
    char *buf = ZLIB_MALLOC(1024*1024);
    argo_structure_dump_dynamic_fields(buf, 1024*1024);
    zpath_debug_cb_printf_response(request_state, "%s", buf);
    ZLIB_FREE(buf);
    return ZPATH_RESULT_NO_ERROR;
}

static inline int zpath_crash_exit()
{
    exit(0);
    return ZPATH_RESULT_NO_ERROR;

}
static inline int zpath_crash_abort()
{
    abort();
    return ZPATH_RESULT_NO_ERROR;
}

static inline int zpath_crash_assert(char *msg)
{
    ZPATH_ASSERT_HARD(0, "zpn_lib", zpath_is_dev_environment(), "zpath_app" , msg);
    return ZPATH_RESULT_NO_ERROR;
}

static int debug_crash_assert(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    return zpath_crash_assert("This is a test assert");
}

static int debug_crash_abort(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    /*
     * if no cb configured, exit immediately; else invoke callback, if needed.
     *  If exit path is scheduled one of the threads marked as 'dont kill on terminate'; we follow normal exit path
     *  This is because, in the func. ptr, we will be doing further processing on this thread.
     *      This can result in this thread stuck or stacks getting modified; which we want to avoid.
     */
    if (zpath_lib_exit_handler_cb) {
        int self_thread_id = zthread_get_self_thread_id();
        if (zthread_get_dont_kill_on_terminate_flag(self_thread_id)) {
            return zpath_crash_abort();
        } else {
            return zpath_lib_exit_handler_cb(zpath_tc_lib_abort, NULL, self_thread_id);
        }
    } else {
        return zpath_crash_abort();
    }
}

static int debug_crash_heartbeat(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    sleep(100500);
    return ZPATH_RESULT_NO_ERROR;
}

static inline int zpath_crash_fortify()
{
    char buf[5];
    // coverity[overrun-buffer-arg]
    memset(&buf, 0, (rand() % 128) + 128);
    return buf[0];
}

static int debug_crash_fortify(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie)
{
    /*
     * if no cb configured, exit immediately; else invoke callback, if needed.
     *  If exit path is scheduled one of the threads marked as 'dont kill on terminate'; we follow normal exit path
     *  This is because, in the func. ptr, we will be doing further processing on this thread.
     *      This can result in this thread stuck or stacks getting modified; which we want to avoid.
     */
    if (zpath_lib_exit_handler_cb) {
        int self_thread_id = zthread_get_self_thread_id();
        if (zthread_get_dont_kill_on_terminate_flag(self_thread_id)) {
            return zpath_crash_fortify();
        } else {
            return zpath_lib_exit_handler_cb(zpath_tc_lib_fortify, NULL, self_thread_id);
        }
    } else {
        return zpath_crash_fortify();
    }
}

static inline int zpath_crash_stack_smash()
{
    char a;
    // coverity[overrun-buffer-arg]
    memset(&a, 0, (rand() % 128) + 128);
    return a;
}

__attribute__((noinline)) static int debug_crash_stack_smash(struct zpath_debug_state *request_state,
                                                             const char **query_values,
                                                             int query_value_count,
                                                             void *cookie)
{
    /*
     * if no cb configured, exit immediately; else invoke callback, if needed.
     *  If exit path is scheduled one of the threads marked as 'dont kill on terminate'; we follow normal exit path
     *  This is because, in the func. ptr, we will be doing further processing on this thread.
     *      This can result in this thread stuck or stacks getting modified; which we want to avoid.
     */
    if (zpath_lib_exit_handler_cb) {
        int self_thread_id = zthread_get_self_thread_id();
        if (zthread_get_dont_kill_on_terminate_flag(self_thread_id)) {
            return zpath_crash_stack_smash();
        } else {
            return zpath_lib_exit_handler_cb(zpath_tc_lib_stack_smash, NULL, self_thread_id);
        }
    } else {
        return zpath_crash_stack_smash();
    }
}

static inline int zpath_crash_segfault()
{

    int *p = 0;
    // coverity[var_deref_op]
    return *p;
}

static int debug_crash_segfault(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    /*
     * if no cb configured, exit immediately; else invoke callback, if needed.
     *  If exit path is scheduled one of the threads marked as 'dont kill on terminate'; we follow normal exit path
     *  This is because, in the func. ptr, we will be doing further processing on this thread.
     *      This can result in this thread stuck or stacks getting modified; which we want to avoid.
     */
    if (zpath_lib_exit_handler_cb) {
        int self_thread_id = zthread_get_self_thread_id();
        if (zthread_get_dont_kill_on_terminate_flag(self_thread_id)) {
            return zpath_crash_segfault();
        } else {
            return zpath_lib_exit_handler_cb(zpath_tc_lib_segfault, NULL, self_thread_id);
        }
    } else {
        return zpath_crash_segfault();
    }
}

/* Array of handler functions for debug crash exit code */
int (*zpath_termination_handler[])(void) = {
    [zpath_tc_heartbeat_exceeded] = NULL,
    [zpath_tc_exit] = NULL,
    [zpath_tc_lib_assert] = NULL,
    [zpath_tc_lib_app_shutdown] = &zpath_crash_exit,
    [zpath_tc_lib_abort] = &zpath_crash_abort,
    [zpath_tc_lib_fortify] =  &zpath_crash_fortify,
    [zpath_tc_lib_stack_smash] = &zpath_crash_stack_smash,
    [zpath_tc_lib_segfault] = &zpath_crash_segfault,
    [zpath_tc_lib_ungraceful_shutdown] = &zpath_crash_ungraceful_shutdown,
    [zpath_tc_invalid] = NULL
};

static int zpath_debug_app_stats_fill(void *cookie, int counter,
                                      void *structure_data)
{
    (void) structure_data;
    (void) cookie;
    zthread_fill_rusage(RUSAGE_SELF, &zpath_app_stats);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_app_stats(void)
{
    struct argo_log_registered_structure *s;

    zpath_app_stats.ru_startime = zpath_system_start_time;

    // Log both once per second and once per minute.
    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "rusage_thread_all_1s",
                                    AL_INFO,
                                    1*1000*1000,
                                    zthread_rusage_desc,
                                    &zpath_app_stats,
                                    1,
                                    zpath_debug_app_stats_fill,
                                    NULL);

    if (!s) {
        ZPATH_LOG(AL_ERROR, "Could not register zpath_app stats");
        return ZPATH_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "rusage_thread_all",
                                    AL_INFO,
                                    60*1000*1000,
                                    zthread_rusage_desc,
                                    &zpath_app_stats,
                                    1,
                                    zpath_debug_app_stats_fill,
                                    NULL);

    if (!s) {
        ZPATH_LOG(AL_ERROR, "Could not register zpath_app stats");
        return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_app_attach_global_remote(char *hostname, int host_port_he, int *zthread_num)
{
    struct wally_fohh_client *wally_fohh_client_handle = NULL;

    wally_fohh_client_handle = wally_fohh_client_create(zpath_global_wally,
                                                        NULL,
                                                        hostname,
                                                        hostname, // SNI
                                                        NULL,
                                                        htons(host_port_he),
                                                        NULL);
    if (!wally_fohh_client_handle) {
        ZPATH_LOG(AL_CRITICAL, "Could not create wally client for accessing %s", hostname);
        return ZPATH_RESULT_ERR;
    }

    zpath_global_remote_db = wally_add_origin(zpath_global_wally,
                                              hostname,
                                              wally_fohh_client_handle,
                                              wally_fohh_register_for_index,
                                              wally_fohh_deregister_for_index,
                                              wally_fohh_set_cookie,
                                              wally_fohh_get_status,
                                              wally_fohh_add_table,
                                              NULL, // set_sequence
                                              wally_fohh_dump_state,
                                              1);
    if (!zpath_global_remote_db) {
        ZPATH_LOG(AL_CRITICAL, "Could not attach wally client origin for %s to wally.", hostname);
        return ZPATH_RESULT_ERR;
    }

    /* set the zthread number on which this conn. is established */
    if (zthread_num) {
        *zthread_num = fohh_connection_get_zthread_num(wally_fohh_client_get_f_conn(wally_fohh_client_handle));
    }

    return ZPATH_RESULT_NO_ERROR;
}



/*
 * Create shard that otherwise doesn't exist.
 */
int zpath_app_create_shard(int shard_index, int use_sqlt, int static_wally)
{
    struct wally *wally = NULL;
    void *db_handle = NULL;
    struct wally_origin *slave_db = NULL;
    struct wally_origin *remote_db = NULL;
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    char str[ARGO_MAX_NAME_LENGTH];
    char str2[ARGO_MAX_NAME_LENGTH];
    char hostname[ARGO_MAX_NAME_LENGTH];
    char dbname[ARGO_MAX_NAME_LENGTH];
    int result = 0;
    struct wally *shard_wally = NULL;
    const char *table_type = (static_wally) ? "static tables" : "regular tables";

    if (shard_index >= ZPATH_MAX_SHARDS) return ZPATH_RESULT_BAD_ARGUMENT;
    if (shard_index < 0) return ZPATH_RESULT_BAD_ARGUMENT;

    pthread_mutex_lock(&zpath_shard_create_lock);

    ZPATH_LOG(AL_NOTICE, "Initializing Shard %d for %s wally", shard_index, table_type);
    if (static_wally) {
        shard_wally = zpath_shard_wally_static[shard_index];
    } else {
        shard_wally = zpath_shard_wally[shard_index];
    }

    /* Might be a small race, so recheck shard existence */
    if (!shard_wally) {

        if (static_wally) {
            /* Create static wally */
            snprintf(str, sizeof(str), "shard-static-%d_wally", shard_index);
            if (!use_sqlt) {
                snprintf(str2, sizeof(str2), "shard-static-%d_postgres", shard_index);
            } else {
                snprintf(str2, sizeof(str2), "shard-static-%d_sqlt", shard_index);
            }
        } else {
            /* Create wally */
            snprintf(str, sizeof(str), "shard-%d_wally", shard_index);
            if (!use_sqlt) {
                snprintf(str2, sizeof(str2), "shard-%d_postgres", shard_index);
            } else {
                snprintf(str2, sizeof(str2), "shard-%d_sqlt", shard_index);
            }
        }
        wally = wally_create(str, zpath_app_is_endpoint, zpath_debug_wally_endpoints_init, NULL, NULL, NULL);
        if (!wally) {
            pthread_mutex_unlock(&zpath_shard_create_lock);
            return ZPATH_RESULT_NO_MEMORY;
        }

        if (static_wally) {
            snprintf(hostname, sizeof(hostname), "r%d-wally.%s", shard_index, ZPATH_LOCAL_CLOUD_NAME);
            snprintf(dbname, sizeof(dbname), "%s.%dr", ZPATH_LOCAL_SLAVE_DB_NAME, shard_index);
        } else {
            snprintf(hostname, sizeof(hostname), "s%d-wally.%s", shard_index, ZPATH_LOCAL_CLOUD_NAME);
            snprintf(dbname, sizeof(dbname), "%s.%d", ZPATH_LOCAL_SLAVE_DB_NAME, shard_index);
        }

        /* Please note: the order of adding origins is critical. We can attach remote db only after slave db. */

        /* Attach local slave db. */
        if (use_slave_db) {
            ZPATH_LOG(AL_NOTICE, "Creating wally DB for %s for shard %d, DB %s, via %s", table_type, shard_index, dbname, hostname);
            /* Attach slave DB. Note- we end up attaching to DB
             * multiple times, once per shard, and once for the global
             * db */
            if (!use_sqlt) {

                db_handle =
                    wally_postgres_create(NULL,           /* Cookie for calling back into wally. */
                                          zpath_app_db_hostname,    /* Host to connect to for database.    */
                                          "postgres",     /* User to connect as for database.    */
                                          dbname,         /* Database name to use to connect.    */
                                          str2,           /* Name of thread to use. */
                                          NULL,           /* No password- it's local */
                                          4,              /* Number of connections
                                                           * to use to connect to
                                                           * the DB. If making
                                                           * massive access to
                                                           * postgres, might want
                                                           * to make this
                                                           * larger. Larger values
                                                           * are only really
                                                           * needed for reading-
                                                           * writing is very slow
                                                           * and synchronous.  */
                                          1,              /* Row writable */
                                          1,              /* table alterable */
                                          0,              /* Not a true origin. */
                                          wally_is_endpoint(wally),
                                          0);             /* Do not poll slave DB's */
            } else {
                db_handle = wally_sqlt_create(dbname, /* dbname */
                                              str2, /* thread_name */
                                              4,    /* nconns */
                                              1,    /* is_row_writable */
                                              0,    /* is_true_origin */
                                              wally_is_endpoint(wally),
                                              0);   /* polling_interval_us */

            }
            if (!db_handle) {
                pthread_mutex_unlock(&zpath_shard_create_lock);
                ZPATH_LOG(AL_CRITICAL, "wally_db_create failed for database %s (It might not exist?) for %s", ZPATH_LOCAL_SLAVE_DB_NAME, table_type);
                return ZPATH_RESULT_ERR;
            }

            if (use_sqlt) {
                snprintf_nowarn(str, sizeof(str), "sqlt:%s", dbname);
            } else {
                snprintf_nowarn(str, sizeof(str), "postgres:%s", dbname);
            }

            slave_db = wally_add_origin(wally,
                                        str,
                                        db_handle,
                                        wally_db_register_for_index,
                                        wally_db_deregister_for_index,
                                        wally_db_set_cookie,
                                        wally_db_get_status,
                                        NULL,
                                        wally_db_set_min_sequence,
                                        wally_db_dump_state,
                                        1);
            if (!slave_db) {
                ZPATH_LOG(AL_ERROR, "wally_add_origin failed for %s", table_type);
                pthread_mutex_unlock(&zpath_shard_create_lock);
                return ZPATH_RESULT_ERR;
            }
        }

    /* Attach remote db. */
        wally_fohh_client_handle =
            wally_fohh_client_create(wally,
                                     NULL,
                                     hostname,
                                     hostname, // SNI
                                     NULL,
                                     incremental_shard_ports ? htons(444 + shard_index) : htons(ZPATH_LOCAL_FOHH_PORT),
                                     NULL);
        if (!wally_fohh_client_handle) {
            ZPATH_LOG(AL_CRITICAL, "Could not create wally client for %s for accessing %s", table_type, hostname);
            pthread_mutex_unlock(&zpath_shard_create_lock);
            return ZPATH_RESULT_ERR;
        }

        remote_db = wally_add_origin(wally,
                                     hostname,
                                     wally_fohh_client_handle,
                                     wally_fohh_register_for_index,
                                     wally_fohh_deregister_for_index,
                                     wally_fohh_set_cookie,
                                     wally_fohh_get_status,
                                     wally_fohh_add_table,
                                     NULL, // set_sequence
                                     wally_fohh_dump_state,
                                     1);
        if (!remote_db) {
            ZPATH_LOG(AL_CRITICAL, "Could not attach wally client origin for %s for %s to wally.", table_type, hostname);
            pthread_mutex_unlock(&zpath_shard_create_lock);
            return ZPATH_RESULT_ERR;
        }

        if (static_wally) {
            zpath_shard_slave_db_static[shard_index] = slave_db;
            zpath_shard_remote_db_static[shard_index] = remote_db;
        } else {
            zpath_shard_slave_db[shard_index] = slave_db;
            zpath_shard_remote_db[shard_index] = remote_db;
        }

        result = zpath_table_init(wally,
                                  slave_db,
                                  remote_db,
                                  1);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_table_init failed for shard %d", shard_index);
            pthread_mutex_unlock(&zpath_shard_create_lock);
            return result;
        }

        if (static_wally) {
            zpath_shard_wally_static[shard_index] = wally;
        } else {
            zpath_shard_wally[shard_index] = wally;
       }
    }

    ZPATH_LOG(AL_NOTICE, "Initializing Shard %d- Complete for %s", shard_index, table_type);

    pthread_mutex_unlock(&zpath_shard_create_lock);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_create_np_global(int use_sqlt)
{
    void *db_handle = NULL;
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    char str[ARGO_MAX_NAME_LENGTH];
    char hostname[ARGO_MAX_NAME_LENGTH];
    char dbname[ARGO_MAX_NAME_LENGTH];
    const char *postgres_hostname;
    char *postgres_username;


    ZPATH_LOG(AL_NOTICE, "Initializing NP global wally");

    if (!zpath_np_global_wally) {
        /* Create wally */
        zpath_np_global_wally = wally_create("np-global-wally", zpath_app_is_endpoint, zpath_debug_wally_endpoints_init, NULL, NULL, NULL);
        if (!zpath_np_global_wally) {
            ZPATH_LOG(AL_ERROR, "np-global wally_create failed");
            return ZPATH_RESULT_ERR;
        }

        postgres_hostname = "localhost";
        postgres_username = "postgres";

	snprintf(hostname, sizeof(hostname), "np-g-wally.%s", ZPATH_LOCAL_CLOUD_NAME);
        snprintf(dbname, sizeof(dbname), "%s.np", ZPATH_LOCAL_SLAVE_DB_NAME);

        /* Please note: the order of adding origins is critical. We can attach remote db only after slave db. */

        /* Attach local slave db. */
        if (use_slave_db) {
            ZPATH_LOG(AL_NOTICE, "Creating global wally DB for NP %s, via %s", dbname, postgres_hostname);
            /* Attach slave DB. Note- we end up attaching to DB
             * multiple times, once per shard, and once for the global
             * db */
            if (!use_sqlt) {

                db_handle =
                    wally_postgres_create(NULL,                    /* Cookie for calling back into wally. */
                                          postgres_hostname,       /* Host to connect to for database.    */
                                          postgres_username,       /* User to connect as for database.    */
                                          dbname,                  /* Database name to use to connect.    */
                                          "np_global_postgres",       /* Name of thread to use. */
                                          NULL,                    /* No password- it's local */
                                          4,              /* Number of connections
                                                           * to use to connect to
                                                           * the DB. If making
                                                           * massive access to
                                                           * postgres, might want
                                                           * to make this
                                                           * larger. Larger values
                                                           * are only really
                                                           * needed for reading-
                                                           * writing is very slow
                                                           * and synchronous.  */
                                          1,              /* Row writable */
                                          1,              /* table alterable */
                                          0,              /* Not a true origin. */
                                          wally_is_endpoint(zpath_np_global_wally),
                                          0);             /* Do not poll slave DB's */
            } else {
                db_handle = wally_sqlt_create(dbname, /* dbname */
                                              "local_sqlt", /* thread_name */
                                              4,    /* nconns */
                                              1,    /* is_row_writable */
                                              0,    /* is_true_origin */
                                              wally_is_endpoint(zpath_np_global_wally),
                                              0);   /* polling_interval_us */

            }
            if (!db_handle) {
                ZPATH_LOG(AL_CRITICAL, "NP wally create failed for database %s (It might not exist?)", dbname);
                return ZPATH_RESULT_ERR;
            }

            if (use_sqlt) {
                snprintf_nowarn(str, sizeof(str), "sqlt:%s", dbname);
            } else {
                snprintf_nowarn(str, sizeof(str), "postgres:%s", dbname);
            }

            zpath_np_global_slave_db = wally_add_origin(zpath_np_global_wally,
                                        str,
                                        db_handle,
                                        wally_db_register_for_index,
                                        wally_db_deregister_for_index,
                                        wally_db_set_cookie,
                                        wally_db_get_status,
                                        NULL,
                                        wally_db_set_min_sequence,
                                        wally_db_dump_state,
                                        1);
            if (!zpath_np_global_slave_db) {
                ZPATH_LOG(AL_ERROR, "wally_add_origin failed for Global NP");
                return ZPATH_RESULT_ERR;
            }
        }

        /* Attach remote db. */
        wally_fohh_client_handle =
            wally_fohh_client_create(zpath_np_global_wally,
                                     NULL,
                                     hostname,
                                     hostname, // SNI
                                     NULL,
                                     htons(ZPATH_LOCAL_FOHH_PORT),
                                     NULL);
        if (!wally_fohh_client_handle) {
            ZPATH_LOG(AL_CRITICAL, "Could not create NP wally client for accessing %s", hostname);
            return ZPATH_RESULT_ERR;
        }

        zpath_np_global_remote_db = wally_add_origin(zpath_np_global_wally,
                                     hostname,
                                     wally_fohh_client_handle,
                                     wally_fohh_register_for_index,
                                     wally_fohh_deregister_for_index,
                                     wally_fohh_set_cookie,
                                     wally_fohh_get_status,
                                     wally_fohh_add_table,
                                     NULL, // set_sequence
                                     wally_fohh_dump_state,
                                     1);
        if (!zpath_np_global_remote_db) {
            ZPATH_LOG(AL_CRITICAL, "Could not attach NP wally client origin for %s to wally.", hostname);
            return ZPATH_RESULT_ERR;
        }
        int zthread_num = -1;
        zthread_num = fohh_connection_get_zthread_num(wally_fohh_client_get_f_conn(wally_fohh_client_handle));
        ZPATH_LOG(AL_NOTICE, "Setting np-g-wally thread num: %d(%s) as don't kill on terminate",
                                zthread_num, zthread_get_thread_name(zthread_num));
        zthread_set_dont_kill_on_terminate_flag(zthread_num);

        ZPATH_LOG(AL_NOTICE, "Attaching remote instance DB... COMPLETE");

    }
    ZPATH_LOG(AL_NOTICE, "Initializing np master shard - Complete");

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Create NP shard if doesn't exist yet
 */
int zpath_app_create_np_shard(int shard_index, int use_sqlt, int use_zpath_table)
{
    struct wally *wally = NULL;
    void *db_handle = NULL;
    struct wally_origin *slave_db = NULL;
    struct wally_origin *remote_db = NULL;
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    char str[ARGO_MAX_NAME_LENGTH];
    char str2[ARGO_MAX_NAME_LENGTH];
    char hostname[ARGO_MAX_NAME_LENGTH];
    char dbname[ARGO_MAX_NAME_LENGTH];
    int result = 0;
    struct wally *np_shard_wally = NULL;

    if (shard_index >= ZPATH_MAX_SHARDS) return ZPATH_RESULT_BAD_ARGUMENT;
    if (shard_index < 0) return ZPATH_RESULT_BAD_ARGUMENT;

    pthread_mutex_lock(&zpath_np_shard_create_lock);

    ZPATH_LOG(AL_NOTICE, "Initializing NP Shard %d for wally", shard_index);
    np_shard_wally = zpath_np_shard_wally[shard_index];

    /* Might be a small race, so recheck shard existence */
    if (!np_shard_wally) {
        /* Create wally */
        snprintf(str, sizeof(str), "np-shard-%d_wally", shard_index);
        if (!use_sqlt) {
            snprintf(str2, sizeof(str2), "np-shard-%d_postgres", shard_index);
        } else {
            snprintf(str2, sizeof(str2), "np-shard-%d_sqlt", shard_index);
        }
        wally = wally_create(str, zpath_app_is_endpoint, zpath_debug_wally_endpoints_init, NULL, NULL, NULL);
        if (!wally) {
            pthread_mutex_unlock(&zpath_np_shard_create_lock);
            return ZPATH_RESULT_NO_MEMORY;
        }

        snprintf(hostname, sizeof(hostname), "np-s%d-wally.%s", shard_index, ZPATH_LOCAL_CLOUD_NAME);
        snprintf(dbname, sizeof(dbname), "%s.np.%d", ZPATH_LOCAL_SLAVE_DB_NAME, shard_index);

        /* Please note: the order of adding origins is critical. We can attach remote db only after slave db. */

        /* Attach local slave db. */
        if (use_slave_db) {
            ZPATH_LOG(AL_NOTICE, "Creating wally DB for NP shard %d, DB %s, via %s", shard_index, dbname, hostname);
            /* Attach slave DB. Note- we end up attaching to DB
             * multiple times, once per shard, and once for the global
             * db */
            if (!use_sqlt) {

                db_handle =
                    wally_postgres_create(NULL,           /* Cookie for calling back into wally. */
                                          zpath_app_db_hostname,    /* Host to connect to for database.    */
                                          "postgres",     /* User to connect as for database.    */
                                          dbname,         /* Database name to use to connect.    */
                                          str2,           /* Name of thread to use. */
                                          NULL,           /* No password- it's local */
                                          4,              /* Number of connections
                                                           * to use to connect to
                                                           * the DB. If making
                                                           * massive access to
                                                           * postgres, might want
                                                           * to make this
                                                           * larger. Larger values
                                                           * are only really
                                                           * needed for reading-
                                                           * writing is very slow
                                                           * and synchronous.  */
                                          1,              /* Row writable */
                                          1,              /* table alterable */
                                          0,              /* Not a true origin. */
                                          wally_is_endpoint(wally),
                                          0);             /* Do not poll slave DB's */
            } else {
                db_handle = wally_sqlt_create(dbname, /* dbname */
                                              str2, /* thread_name */
                                              4,    /* nconns */
                                              1,    /* is_row_writable */
                                              0,    /* is_true_origin */
                                              wally_is_endpoint(wally),
                                              0);   /* polling_interval_us */

            }
            if (!db_handle) {
                pthread_mutex_unlock(&zpath_np_shard_create_lock);
                ZPATH_LOG(AL_CRITICAL, "NP wally create failed for database %s (It might not exist?)", dbname);
                return ZPATH_RESULT_ERR;
            }

            if (use_sqlt) {
                snprintf_nowarn(str, sizeof(str), "sqlt:%s", dbname);
            } else {
                snprintf_nowarn(str, sizeof(str), "postgres:%s", dbname);
            }

            slave_db = wally_add_origin(wally,
                                        str,
                                        db_handle,
                                        wally_db_register_for_index,
                                        wally_db_deregister_for_index,
                                        wally_db_set_cookie,
                                        wally_db_get_status,
                                        NULL,
                                        wally_db_set_min_sequence,
                                        wally_db_dump_state,
                                        1);
            if (!slave_db) {
                ZPATH_LOG(AL_ERROR, "wally_add_origin failed for NP shard %d", shard_index);
                pthread_mutex_unlock(&zpath_np_shard_create_lock);
                return ZPATH_RESULT_ERR;
            }
        }

    /* Attach remote db. */
        wally_fohh_client_handle =
            wally_fohh_client_create(wally,
                                     NULL,
                                     hostname,
                                     hostname, // SNI
                                     NULL,
                                     incremental_shard_ports ? htons(444 + shard_index) : htons(ZPATH_LOCAL_FOHH_PORT),
                                     NULL);
        if (!wally_fohh_client_handle) {
            ZPATH_LOG(AL_CRITICAL, "Could not create NP wally client for accessing %s", hostname);
            pthread_mutex_unlock(&zpath_np_shard_create_lock);
            return ZPATH_RESULT_ERR;
        }

        remote_db = wally_add_origin(wally,
                                     hostname,
                                     wally_fohh_client_handle,
                                     wally_fohh_register_for_index,
                                     wally_fohh_deregister_for_index,
                                     wally_fohh_set_cookie,
                                     wally_fohh_get_status,
                                     wally_fohh_add_table,
                                     NULL, // set_sequence
                                     wally_fohh_dump_state,
                                     1);
        if (!remote_db) {
            ZPATH_LOG(AL_CRITICAL, "Could not attach NP wally client origin for %s to wally.", hostname);
            pthread_mutex_unlock(&zpath_np_shard_create_lock);
            return ZPATH_RESULT_ERR;
        }

        zpath_np_shard_slave_db[shard_index] = slave_db;
        zpath_np_shard_remote_db[shard_index] = remote_db;

        result = zpath_table_init(wally,
                                  slave_db,
                                  remote_db,
                                  use_zpath_table);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_table_init failed for shard %d", shard_index);
            pthread_mutex_unlock(&zpath_np_shard_create_lock);
            return result;
        }

        zpath_np_shard_wally[shard_index] = wally;
    }

    ZPATH_LOG(AL_NOTICE, "Initializing NP Shard %d - Complete", shard_index);

    pthread_mutex_unlock(&zpath_np_shard_create_lock);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Get wally to use for accessing specified shard.
 */
int zpath_app_shard_init(int shard_index, int use_sqlt)
{
    return zpath_app_shard_init_static(shard_index, use_sqlt, 0 /*regular tables*/);
}

/*
 * Get wally to use for accessing specified shard for static tables.
 */
int zpath_app_shard_init_static(int shard_index, int use_sqlt, int static_wally)
{
    int result;
    const char *table_type = (static_wally) ? "static tables" : "regular tables";
    struct wally *wally = NULL;

    if ((!shard_index) || (shard_index > ZPATH_CLOUD_SHARD_COUNT)) {
        ZPATH_LOG(AL_CRITICAL, "Attempting to create shard %d, for %s out of range", shard_index, table_type);
        return ZPATH_RESULT_ERR;
    }

    if (static_wally) {
        wally = zpath_shard_wally_static[shard_index];
    } else {
        wally = zpath_shard_wally[shard_index];
    }

    if (!wally) {
        result = zpath_app_create_shard(shard_index, use_sqlt, static_wally);
        if (result) {
            ZPATH_LOG(AL_CRITICAL, "Could not create shard %d for %s", shard_index, table_type);
            return result;
        }
        if (static_wally) {
            zpath_debug_wally_add(zpath_shard_wally_static[shard_index], ZPATH_WALLY_STATS_INTERVAL_US);
        } else {
            zpath_debug_wally_add(zpath_shard_wally[shard_index], ZPATH_WALLY_STATS_INTERVAL_US);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Init the tables that shard access uses.
 */
int zpath_app_shard_pre_init(void)
{
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * NP shard does not support static wally yet
 */
int zpath_app_np_shard_init(int shard_index, int use_sqlt, int use_zpath_table)
{
    int result;
    struct wally *np_wally = NULL;

    if ((!shard_index) || (shard_index > ZPATH_CLOUD_SHARD_COUNT)) {
        ZPATH_LOG(AL_CRITICAL, "Attempting to create NP shard %d, out of range", shard_index);
        return ZPATH_RESULT_ERR;
    }

    np_wally = zpath_np_shard_wally[shard_index];

    if (!np_wally) {
        result = zpath_app_create_np_shard(shard_index, use_sqlt, use_zpath_table);
        if (result) {
            ZPATH_LOG(AL_CRITICAL, "Could not create NP shard %d", shard_index);
            return result;
        }
        zpath_debug_wally_add(zpath_np_shard_wally[shard_index], ZPATH_WALLY_STATS_INTERVAL_US);
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_version(struct zpath_debug_state *request_state,
                      const char **query_values,
                      int query_value_count,
                      void *cookie)
{
    char *version_str = cookie;
    zpath_debug_cb_printf_response(request_state, "%s\n", version_str);
    return ZPATH_RESULT_NO_ERROR;
}

static int inittime_callback(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *cookie)
{
    if (zpath_system_start_time && zpath_service_init_complete_us > zpath_system_start_time)
        zpath_debug_cb_printf_response(request_state, "Time taken for init: %"PRId64"s\n",
                                (zpath_service_init_complete_us - zpath_system_start_time)/US_PER_SEC);
    else
        zpath_debug_cb_printf_response(request_state, "Process is still initializing\n");

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_uptime(struct zpath_debug_state *request_state,
                     const char **query_values,
                     int query_value_count,
                     void *cookie)
{
    int64_t now = epoch_us();
    int64_t diff = now - zpath_system_start_time;
    diff /= 1000000l; /* To seconds */
    if (query_values[0]) {
        /* Asked for seconds only. */
        zpath_debug_cb_printf_response(request_state, "%ld\n", (long) diff);
    } else {
        int64_t days = diff / (24l*60l*60l);
        int64_t hours = (diff % (24l*60l*60l)) / (60l*60l);
        int64_t minutes =  (diff % (60l*60l)) / (60l);
        int64_t seconds = (diff % 60l);
        if (days) {
            if (days == 1) {
                zpath_debug_cb_printf_response(request_state, "%ld day %02ld:%02ld:%02ld\n", (long) days, (long) hours, (long) minutes, (long) seconds);
            } else {
                zpath_debug_cb_printf_response(request_state, "%ld days %02ld:%02ld:%02ld\n", (long) days, (long) hours, (long) minutes, (long) seconds);
            }
        } else if (hours) {
            zpath_debug_cb_printf_response(request_state, "%02ld:%02ld:%02ld\n", (long) hours, (long) minutes, (long) seconds);
        } else {
            zpath_debug_cb_printf_response(request_state, "%02ld:%02ld\n", (long) minutes, (long) seconds);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_starttime(struct zpath_debug_state *request_state,
                        const char **query_values,
                        int query_value_count,
                        void *cookie)
{
    int64_t when = zpath_system_start_time;
    if (query_values[0]) {
        /* Asked for epoch. */
        zpath_debug_cb_printf_response(request_state, "%ld\n", (long) when / (1000l*1000l));
    } else {
        char out_str[ARGO_LOG_GEN_TIME_STR_LEN];
        out_str[0] = 0;
        argo_log_gen_time(when, out_str, sizeof(out_str), 0, 0);
        zpath_debug_cb_printf_response(request_state, "%s\n", out_str);
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* Logging Function used for zevent logging */
static void zevent_logger(int priority, const char *file, const char *func, int line, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    ZPATH_LOG_TEXT(priority, file, func, line, "%s", dump);
}

int zpath_simple_app_init(const struct zpath_simple_app_init_params *params)
{
    if (!params || !params->role_version_string) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    const int event_log_mask_stderr = params->log_stderr ? (params->debuglog ? 0xff : 0x7f) : 0x0;
    const int event_log_mask_syslog = params->log_syslog ? (params->debuglog ? 0xff : 0x7f) : 0x0;

    zpath_registration_started(epoch_us());

    ZPATH_LOCAL_DEBUG_PORT = params->debug_port;

    int result = argo_library_init(ZPATH_ARGO_RPC_REGISTRATIONS_MAX);
    if (result) {
        fprintf(stderr, "%s:%s:%d: argo_library_init failed: %d\n", __FILE__, __FUNCTION__, __LINE__, result);
        return result;
    }

    zthread_rusage_desc = argo_register_global_structure(ZTHREAD_RUSAGE_HELPER);
    zpath_inittime_desc = argo_register_global_structure(ZPATH_INITTIME_HELPER);

    /* We initialize logging infrastructure almost immediately- even
     * though we don't know our instance name yet. */
    if (argo_log_init_with_maxmem("unknown", 0, params->role_name, params->role_version_string, 1, zpath_app_specific_max_logging_mb)) {
        fprintf(stderr, "Err: could not initialize argo_log\n");
        return ZPATH_RESULT_ERR;
    }

    if (zpath_app_specific_argo_mem_threshold) {
        argo_log_global_set_mem_threshold(0, zpath_app_specific_argo_mem_threshold);
    }

    /* Note: We use the stats log for logging the stats for all the
     * others, so we create it first, even if it doesn't have readers,
     * etc. */
    zpath_stats_collection = argo_log_create("statistics_log", NULL, NULL);
    if (!zpath_stats_collection) {
        ZPATH_LOG(AL_ERROR, "Could not initialize stats_log argo_log collection");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Initialize event and stats logs.
     */
    zpath_event_collection = argo_log_create("event_log", NULL, NULL);
    if (!zpath_event_collection) {
        fprintf(stderr, "Err: could not initialize event_log argo_log collection\n");
        return ZPATH_RESULT_ERR;
    }
    argo_library_log("event_log");

    if (params->rbac_support) {
        zpath_debug_update_rbac_enablement(params->role_name);
    }

    ZPATH_LOG(AL_NOTICE, "%s starting", params->role_name ? : "unknown");
    //ZPATH_LOG(AL_NOTICE, "Logging system initialized.");

    result = zpath_app_logging_init(params->role_name,
                                    event_log_mask_stderr,  /* stderr */
                                    event_log_mask_syslog,  /* syslog */
                                    1,                      /* abbreviated format */
                                    params->debug_port);    /* add debug commands */
    if (result) {
        return result;
    }

    if (params->log_filename) {
        struct argo_log_file *event_file;
        struct argo_log_reader *event_file_reader;

        event_file = argo_log_file_create(zpath_event_collection,
                                          params->log_filename,
                                          NULL,
                                          1024*1024*1024,
                                          argo_serialize_binary);

        if (!event_file) {
            fprintf(stderr, "Error: Cannot create log file for %s\n", params->log_filename);
            return ZPATH_RESULT_ERR;
        }

        event_file_reader = argo_log_read(zpath_event_collection, "event_log_file", 0, 1, argo_log_file_callback,
                                          NULL, event_file, zpath_stats_collection, 60*1000*1000);
        if (!event_file_reader) {
            fprintf(stderr, "Error: Cannot create log file writer for %s\n", params->log_filename);
            return ZPATH_RESULT_ERR;
        }
    }

    zthread_set_log(zthread_log);

    result = fohh_ssl_init(zpath_event_collection, params->fips_mode);
    if (result) {
        ZPATH_LOG(AL_ERROR, "fohh_ssl_init returned %d", result);
        return ZPATH_RESULT_ERR;
    }

    zevent_set_logger(zevent_logger);
    zevent_init();

    result = fohh_libevent_init(zpath_event_collection);
    if (result) {
        ZPATH_LOG(AL_ERROR, "fohh_libevent_init failed: %d", result);
        return result;
    }


#if defined(__linux__)
    //create listener thread only if rbac support is enabled
    if (params->rbac_support) {
        /* create listener thread for command handling */
        result = zpath_debug_command_listener_init(params->role_name);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not create command listener socket for role: %s", params->role_name);
            return result;
        }
    }
#endif

    result = zpath_debug_pre_init(params->debug_port != 0);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize zpath_debug");
        return ZPATH_RESULT_ERR;
    }

    wally_init();

    if (params->debug_port) {
        result = zpath_debug_add_flag(&fohh_debug,
                                      fohh_debug_catch,
                                      "fohh",
                                      fohh_debug_names);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize wally debug flags");
            return result;
        }

        result = zpath_debug_add_flag(&wally_debug,
                                      wally_debug_catch,
                                      "wally",
                                      wally_debug_names);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize wally debug flags");
            return result;
        }

		result = zpath_debug_add_collection_mem_view();
		if (result) {
			ZPATH_LOG(AL_ERROR, "Could not setup argo log collection mem view");
			return result;
		}

        result = zpath_debug_add_collection(zpath_event_collection);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add debugging for collection %s: %s",
                      argo_log_get_name(zpath_event_collection),
                      zpath_result_string(result));
            return result;
        }

        result = zpath_debug_add_collection(zpath_stats_collection);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add debugging for collection %s: %s",
                      argo_log_get_name(zpath_stats_collection),
                      zpath_result_string(result));
            return result;
        }

        result = zpath_debug_add_read_command("Get software version",
                                         "/version",
                                         zpath_app_version,
                                         ZLIB_STRDUP(params->role_version_string, strlen(params->role_version_string)),
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
            return result;
        }

        result = zpath_debug_add_read_command("Get software uptime",
                                         "/uptime",
                                         zpath_app_uptime,
                                         NULL,
                                         "seconds", "Specify as anything to return seconds",
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
            return result;
        }

        result = zpath_debug_add_read_command("Check time taken for this instance to fully init",
                                         "/inittime",
                                         inittime_callback,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software init time");
            return result;
        }


        result = zpath_debug_add_read_command("Get software start time",
                                         "/starttime",
                                         zpath_app_starttime,
                                         NULL,
                                         "epoch", "Specify as anything to return start epoch",
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
            return result;
        }

        result = zpath_debug_app_stats();
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize app stats");
            return ZPATH_RESULT_ERR;
        }
    }

    result = zpath_app_shutdown_notice(collection_shutdown, NULL);
    if (result) {
        fprintf(stderr, "Err: Could not initialize shutdown procedure\n");
        return ZPATH_RESULT_ERR;
    }

    /* For posterity, log the versions of our libraries. */
    //ZPATH_LOG(AL_NOTICE, "AVL version: %s", avl_version());
    //ZPATH_LOG(AL_NOTICE, "ARGO version: %s", argo_version());
    //ZPATH_LOG(AL_NOTICE, "FOHH version: %s", fohh_version());
    //ZPATH_LOG(AL_NOTICE, "WALLY version: %s", wally_version());
    //ZPATH_LOG(AL_NOTICE, "ZPATH_LIB version: %s", zpath_lib_version());

    fohh_set_debugcmd_pool_support(params->debugcmd_pool_support);

    /* Get FOHH initialized and running. */
    result = fohh_init(params->fohh_thread_count,
                       (!params->root_cert_file &&
                        !params->cert_chain_file &&
                        !params->private_key_file),
                       NULL,
                       params->root_cert_file,
                       params->cert_chain_file,
                       params->private_key_file,
                       NULL,
                       1,
                       params->fohh_watchdog_s,
                       0);
    if (result) {
        if (result == FOHH_RESULT_BAD_ARGUMENT) {
            ZPATH_LOG(AL_ERROR, "fohh_init returned %s: Perhaps bad path/filename for certificates or keys?(%s, %s, %s)",
                      zpath_result_string(result),
                      params->root_cert_file,
                      params->cert_chain_file,
                      params->private_key_file);
        } else {
            ZPATH_LOG(AL_ERROR, "fohh_init returned %s", zpath_result_string(result));
        }
        return result;
    }

    //ZPATH_LOG(AL_NOTICE, "Initialized with %d worker threads.", params->fohh_thread_count);

    result = zpath_debug_init("event_log", "stats_log", params->debug_port);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize zpath_debug");
        return ZPATH_RESULT_ERR;
    }

    if (params->debug_port) {
        result = fohh_install_ip_trigger_callback(zpath_debug_catch_ip,
                                                  zpath_debug_uncatch_ip);
        if (result) {
            ZPATH_LOG(AL_ERROR, "fohh_init could not install IP trigger callback");
            return result;
        }

        result = zpath_debug_add_read_command("Check if application is initialized and running",
                                         "/app/status",
                                         simple_app_status_callback,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add status debug command");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to shutdown. Much like SIG_TERM",
                                         "/app/shutdown",
                                         service_shutdown_callback,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add shutdown command");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with assert",
                                         "/app/crash/assert",
                                         debug_crash_assert,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/assert");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with abort",
                                         "/app/crash/abort",
                                         debug_crash_abort,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/abort");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with heartbeat",
                                         "/app/crash/heartbeat",
                                         debug_crash_heartbeat,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/heartbeat");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with fortify source",
                                         "/app/crash/fortify",
                                         debug_crash_fortify,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/fortify");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with stack smash",
                                         "/app/crash/stack_smash",
                                         debug_crash_stack_smash,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/stack_smash");
            return result;
        }

        result = zpath_debug_add_admin_command("Tell application to crash with segfault",
                                         "/app/crash/segfault",
                                         debug_crash_segfault,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /app/crash/segfault");
            return result;
        }

        result = zpath_debug_add_admin_command("System exit",
                                         "/system/ungraceful_shutdown",
                                         zpn_system_ungraceful_shutdown,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /system/ungraceful_shutdown");
            return result;
        }

        result = zpath_debug_add_read_command("Dump dynamically added argo structure fields",
                                         "/argo/structure/dump_dynamic_fields",
                                         debug_argo_structure_dump_dynamic_fields,
                                         NULL,
                                         NULL);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not add /argo/structure/dump_dynamic_fields");
            return result;
        }

        result = sanitizer_add_debug_commands();
        if (result)
            return result;
    }

    if (params->load_zpa_cloud_config){
        zpath_init_cloud_config();
        result = zpath_load_zpa_cloud_config(ZPA_CLOUD_CONFIG_FILE_FULL_PATH);
        if (ZPATH_RESULT_NO_ERROR != result) {
            ZPATH_LOG(AL_ERROR, "Could not load zpa_cloud_config");
            return result;
        } else {
            ZPATH_LOG(AL_NOTICE,"Successfully loaded ZPA Cloud Config");
        }
    }

    //ZPATH_LOG(AL_NOTICE, "Simple app init complete.");

    return ZPATH_RESULT_NO_ERROR;
}

void update_itasca_logs_port(int itasca_log_port)
{
    itasca_logging_port_he = itasca_log_port;
}

void zpath_lib_termination_handler_init(int (*exit_handler_fn)(enum zpath_termination_code tc, void *cookie, int self_thread_num))
{
    zpath_lib_exit_handler_cb = exit_handler_fn;
}

int zpath_app_init(const struct zpath_app_init_params *params)
{
    if (!params || !params->role_version_string) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    int result;
    void *db_handle;
    char *db_name;
    const char *postgres_hostname;
    char *postgres_password;
    int postgres_writable;
    int slave_db_only = 0;
    char *postgres_username;
    char *postgres_schema = NULL;

    char str[ARGO_MAX_NAME_LENGTH];

    struct argo_log_reader *event_file_reader;
    struct argo_log_file *event_file;

    struct argo_log_reader *stats_file_reader;
    struct argo_log_file *stats_file;

    zpath_app_db_hostname = params->local_db_hostname;

    zpath_registration_started(params->system_start_us);

    zpath_app_is_endpoint = params->is_endpoint;

    incremental_shard_ports = params->incremental_shard_ports_arg;

    zpath_app_instance_id = params->instance_id;

    zpath_app_reached_milestone(zpath_app_milestone_created);

    result = argo_library_init(ZPATH_ARGO_RPC_REGISTRATIONS_MAX);
    if (result) {
        fprintf(stderr, "%s:%s:%d: argo_library_init failed: %d\n", __FILE__, __FUNCTION__, __LINE__, result);
        return result;
    }

    zthread_rusage_desc = argo_register_global_structure(ZTHREAD_RUSAGE_HELPER);
    zpath_inittime_desc = argo_register_global_structure(ZPATH_INITTIME_HELPER);

    /* We initialize logging infrastructure almost immediately- even
     * though we don't know our instance name yet. */
    if (argo_log_init_with_maxmem("unknown", 0, params->role_name, params->role_version_string, 0, zpath_app_specific_max_logging_mb)) {
        fprintf(stderr, "Err: could not initialize argo_log\n");
        return ZPATH_RESULT_ERR;
    }

    if (zpath_app_specific_argo_mem_threshold) {
        argo_log_global_set_mem_threshold(0, zpath_app_specific_argo_mem_threshold);
    }

    /* Note: We use the stats log for logging the stats for all the
     * others, so we create it first, even if it doesn't have readers,
     * etc. */
    zpath_stats_collection = argo_log_create("statistics_log", NULL, NULL);
    if (!zpath_stats_collection) {
        ZPATH_LOG(AL_ERROR, "Could not initialize stats_log argo_log collection");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Initialize event and stats logs.
     */
    zpath_event_collection = argo_log_create("event_log", NULL, NULL);
    if (!zpath_event_collection) {
        fprintf(stderr, "Err: could not initialize event_log argo_log collection\n");
        return ZPATH_RESULT_ERR;
    }
    argo_library_log("event_log");

    zpath_debug_update_rbac_enablement(params->role_name);

    ZPATH_LOG(AL_NOTICE, "%s starting", params->role_name ? : "unknown");
    //ZPATH_LOG(AL_NOTICE, "Logging system initialized.");

    ZPATH_LOG(AL_NOTICE, "zpath_app_init- Instance = %d, local_db = %s, role = %s, role_version = %s",
              params->instance_id,
              params->local_db_name,
              params->role_name,
              params->role_version_string);

    result = zpath_app_logging_init(params->role_name,
                                    params->debuglog ? 0xff : 0x7f, /* stderr */
                                    0,                              /* syslog */
                                    0,                              /* abbreviated format */
                                    1);                             /* add debug commands */
    if (result) {
        return result;
    }

    if (!params->completely_disable_ssl) {
        result = fohh_ssl_init(zpath_event_collection, params->fips_mode);
        if (result) {
            ZPATH_LOG(AL_ERROR, "fohh_ssl_init returned %d", result);
            return ZPATH_RESULT_ERR;
        }
    }

    zevent_set_logger(zevent_logger);
    zevent_init();

    zthread_set_log(zthread_log);

    result = fohh_libevent_init(zpath_event_collection);
    if (result) {
        ZPATH_LOG(AL_ERROR, "fohh_libevent_init failed: %d", result);
        return result;
    }

#if defined(__linux__)
    /* create listener thread for command handling */
    result = zpath_debug_command_listener_init(params->role_name);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not create command listener socket for role: %s", params->role_name);
        return result;
    }
#endif

    result = zpath_debug_pre_init(1);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize zpath_debug");
        return ZPATH_RESULT_ERR;
    }

    wally_init();

    /* important: this needs to be called before creation of wallies and tables */
    result = zpath_sync_pause_init(params->role_name);
    if (result) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug:Could not initialize zpath_sync_pause_init: %s",
                                zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&fohh_debug,
                                  fohh_debug_catch,
                                  "fohh",
                                  fohh_debug_names);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize wally debug flags");
        return result;
    }

    result = zpath_debug_add_flag(&wally_debug,
                                  wally_debug_catch,
                                  "wally",
                                  wally_debug_names);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize wally debug flags");
        return result;
    }

	result = zpath_debug_add_collection_mem_view();
	if (result) {
		ZPATH_LOG(AL_ERROR, "Could not setup argo log collection mem view");
		return result;
	}

    result = zpath_debug_add_collection(zpath_event_collection);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add debugging for collection %s: %s",
                  argo_log_get_name(zpath_event_collection),
                  zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_collection(zpath_stats_collection);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add debugging for collection %s: %s",
                  argo_log_get_name(zpath_stats_collection),
                  zpath_result_string(result));
        return result;
    }

    zpath_global_wally = wally_create("global_wally", zpath_app_is_endpoint,
                                        zpath_debug_wally_endpoints_init, NULL, NULL,
                                        zpath_wally_post_resume_callback);
    if (!zpath_global_wally) {
        ZPATH_LOG(AL_ERROR, "wally_create failed");
        return ZPATH_RESULT_ERR;
    }

    zpath_local_wally = wally_create("local_wally", zpath_app_is_endpoint, zpath_debug_wally_endpoints_init, NULL, NULL, NULL);
    if (!zpath_local_wally) {
        ZPATH_LOG(AL_ERROR, "wally_create failed");
        return ZPATH_RESULT_ERR;
    }

    if (params->zpath_local_config_file) {
        ZPATH_LOG(AL_NOTICE, "Retrieving local configuration from FILE %s...", params->zpath_local_config_file);
        result = zpath_local_init_with_file(params->zpath_local_config_file);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_local_init failed: Could not read local configuration for instance %d from 'local_db'", params->instance_id);
            return ZPATH_RESULT_ERR;
        }
    } else {
        ZPATH_LOG(AL_NOTICE, "Retrieving local configuration from %s as instance %d...", params->local_db_name, params->instance_id);
        result = zpath_local_init(zpath_local_wally, zpath_app_db_hostname, params->local_db_name, params->instance_id);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_local_init failed: Could not read local configuration for instance %d from 'local_db'", params->instance_id);
            return ZPATH_RESULT_ERR;
        }
    }

    if (strcmp(zpath_local_global_state.current_config->instance_name, "default") == 0) {
        ZPATH_LOG(AL_ERROR, "Must have a system name other than 'default' for instance %d", params->instance_id);
        return ZPATH_RESULT_ERR;
    }

    /* Yes, this looks like a memory leak. But the way we implement
     * version checking, this must be relatively constant. */
    result = zpath_debug_add_read_command("Get software version",
                                     "/version",
                                     zpath_app_version,
                                     ZLIB_STRDUP(params->role_version_string, strlen(params->role_version_string)),
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
        return result;
    }

    result = zpath_debug_add_read_command("Get software uptime",
                                     "/uptime",
                                     zpath_app_uptime,
                                     NULL,
                                     "seconds", "Specify as anything to return seconds",
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
        return result;
    }

    result = zpath_debug_add_read_command("Get software start time",
                                     "/starttime",
                                     zpath_app_starttime,
                                     NULL,
                                     "epoch", "Specify as anything to return start epoch",
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not set up software version handler");
        return result;
    }

    result = zpath_debug_add_read_command("Check time taken for this instance to fully init",
                                     "/inittime",
                                     inittime_callback,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not set up software init time");
        return result;
    }

    result = zpath_debug_app_stats();
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize app stats");
        return ZPATH_RESULT_ERR;
    }

    if (!zpath_local_global_state.current_config->role ||
        strcmp(zpath_local_global_state.current_config->role, params->role_name) != 0) {
        ZPATH_LOG(AL_ERROR, "Attempting to execute on %s(%d) configured for role %s using software for %s.",
                  params->local_db_name,
                  params->instance_id,
                  zpath_local_global_state.current_config->role ?
                  zpath_local_global_state.current_config->role : "NULL",
                  params->role_name);
        return ZPATH_RESULT_ERR;
    }

    /* We now have our instance name, and can log using our version */
    if (argo_log_init_with_maxmem(ZPATH_LOCAL_FULL_NAME, 0,
                                  params->role_name,
                                  params->role_version_string,
                                  0,
                                  zpath_app_specific_max_logging_mb)) {
        fprintf(stderr, "Err: could not initialize argo_log\n");
        return ZPATH_RESULT_ERR;
    }

    result = zpath_app_shutdown_notice(collection_shutdown, NULL);
    if (result) {
        fprintf(stderr, "Err: Could not initialize shutdown procedure\n");
        return ZPATH_RESULT_ERR;
    }

    /* For posterity, log the versions of our libraries. */
    //ZPATH_LOG(AL_NOTICE, "AVL version: %s", avl_version());
    //ZPATH_LOG(AL_NOTICE, "ARGO version: %s", argo_version());
    //ZPATH_LOG(AL_NOTICE, "FOHH version: %s", fohh_version());
    //ZPATH_LOG(AL_NOTICE, "WALLY version: %s", wally_version());
    //ZPATH_LOG(AL_NOTICE, "ZPATH_LIB version: %s", zpath_lib_version());

    /* Okay, we should have all the state we need for creating file
     * readers for our log collections */
    if (params->personality & ZPATH_APP_PERSONALITY_NO_FILE_LOGGING) {
        /* No file logging! */
    } else {
        /* File logging */
        event_file = argo_log_file_create(zpath_event_collection,
                                          ZPATH_LOCAL_EVENT_LOG_FILE,
                                          ZPATH_LOCAL_EVENT_LOG_FILE_SHORT,
                                          1024*1024*1024,
                                          argo_serialize_binary);
        if (!event_file) {
            ZPATH_LOG(AL_WARNING, "Could not create event log file named %s", ZPATH_LOCAL_EVENT_LOG_FILE);
        } else {
            event_file_reader = argo_log_read(zpath_event_collection, "event_log_file", 0, 1, argo_log_file_callback, NULL, event_file, zpath_stats_collection, 60*1000*1000);
            if (!event_file_reader) {
                ZPATH_LOG(AL_WARNING, "Could not create file based event log reader");
            }
        }

        stats_file = argo_log_file_create(zpath_stats_collection,
                                          ZPATH_LOCAL_STATS_LOG_FILE,
                                          ZPATH_LOCAL_STATS_LOG_FILE_SHORT,
                                          1024*1024*1024,
                                          argo_serialize_binary);
        if (!stats_file) {
            ZPATH_LOG(AL_WARNING, "Could not create stats log file named %s", ZPATH_LOCAL_STATS_LOG_FILE);
        } else {
            stats_file_reader = argo_log_read(zpath_stats_collection, "stats_log_file", 0, 1, argo_log_file_callback, NULL, stats_file, zpath_stats_collection, 60*1000*1000);
            if (!stats_file_reader) {
                ZPATH_LOG(AL_WARNING, "Could not create file based stats log reader");
            }
        }
    }

    fohh_set_debugcmd_pool_support(params->debugcmd_pool_support);

    struct zcrypt_pkey *zpkey = NULL;
    if (params->fohh_private_key_pem) {
        if ((zpkey = zcrypt_pkey_create_from_pem(params->fohh_private_key_pem)) == NULL) {
            ZPATH_LOG(AL_WARNING, "Could not load a private key from PEM string");
        }
    }

    /* Get FOHH initialized and running. */
    result = fohh_init(params->fohh_thread_count,
                       params->completely_disable_ssl,
                       ZPATH_LOCAL_FOHH_SERVICE_PREFIX,
                       ZPATH_LOCAL_ROOT_CERTIFICATE_FILE,
                       ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE,
                       ZPATH_LOCAL_PRIVATE_KEY_FILE,
                       zcrypt_get_pkey(zpkey),
                       1,
                       params->fohh_watchdog_s,
                       params->use_localhost_resolver);

    if (zpkey) {
        zcrypt_pkey_destroy(zpkey);
        zpkey = NULL;
    }

    if (result) {
        if (result == FOHH_RESULT_BAD_ARGUMENT) {
            ZPATH_LOG(AL_ERROR, "fohh_init returned %s: Perhaps bad path/filename for certificates or keys?(%s, %s, %s)",
                      zpath_result_string(result),
                      ZPATH_LOCAL_ROOT_CERTIFICATE_FILE,
                      ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE,
                      ZPATH_LOCAL_PRIVATE_KEY_FILE);
        } else {
            ZPATH_LOG(AL_ERROR, "fohh_init returned %s", zpath_result_string(result));
        }
        return result;
    }

    result = zpath_debug_init("event_log", "stats_log", ZPATH_LOCAL_DEBUG_PORT);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize zpath_debug");
        return ZPATH_RESULT_ERR;
    }

    result = fohh_install_ip_trigger_callback(zpath_debug_catch_ip,
                                              zpath_debug_uncatch_ip);
    if (result) {
        ZPATH_LOG(AL_ERROR, "fohh_init could not install IP trigger callback");
        return result;
    }

    //ZPATH_LOG(AL_NOTICE, "Initialized with %d worker threads.", fohh_thread_count);

    /* Safeguard overriding true origin: only allow replicating config  into a local db,
     * i.e. localhost or *********/8, not a remote one (could be true origin) */
    if (ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST &&
        ZPATH_LOCAL_WALLYD_POSTGRES_HOST &&
        strcasecmp(ZPATH_LOCAL_WALLYD_POSTGRES_HOST, "localhost") &&
        strncmp(ZPATH_LOCAL_WALLYD_POSTGRES_HOST, "127.", strlen("127."))) {
        ZPATH_LOG(AL_ERROR, "unsupported configuration: wallyd_postgres_host=%s, wallyd_fohh_remote_host=%s",
                            ZPATH_LOCAL_WALLYD_POSTGRES_HOST, ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST);
        return ZPATH_RESULT_ERR;
    }

    if (ZPATH_LOCAL_WALLYD_IS_GLOBAL &&
        ZPATH_LOCAL_WALLYD_DB_NAME &&
        ZPATH_LOCAL_WALLYD_POSTGRES_HOST) {
        /* If we are wallyd and are exporting the global DB itself, we
         * use that global DB for our instance state */
        db_name = ZPATH_LOCAL_WALLYD_DB_NAME;
        postgres_hostname = ZPATH_LOCAL_WALLYD_POSTGRES_HOST;
        postgres_password = ZPATH_LOCAL_WALLYD_POSTGRES_PASSWORD;
        postgres_writable = ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST != 0;
        postgres_username = ZPATH_LOCAL_WALLYD_POSTGRES_USERNAME ? ZPATH_LOCAL_WALLYD_POSTGRES_USERNAME : "postgres";
        postgres_schema = ZPATH_LOCAL_WALLYD_POSTGRES_SCHEMA;
    } else {
        db_name = ZPATH_LOCAL_SLAVE_DB_NAME;
        postgres_hostname = zpath_app_db_hostname;
        postgres_password = NULL;
        postgres_writable = 1;
        postgres_username = "postgres";
    }

    ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_global_slave) as %s.", db_name);


    /* Attach slave DB. */
    if (!params->use_sqlt) {
        db_handle =
            wally_postgres_create_with_schema(NULL,                 /* Cookie for calling back into wally. */
                                              postgres_hostname,    /* Host to connect to for database.    */
                                              postgres_username,    /* User to connect as for database.    */
                                              db_name,              /* Database name to use to connect.    */
                                              "global_postgres",    /* Name to give thread... */
                                              postgres_password,    /* Password. */
                                              postgres_schema,      /* Schema */
                                              4,                    /* Number of connections
                                                                     * to use to connect to
                                                                     * the DB. If making
                                                                     * massive access to
                                                                     * postgres, might want
                                                                     * to make this
                                                                     * larger. Larger values
                                                                     * are only really
                                                                     * needed for reading-
                                                                     * writing is very slow
                                                                     * and synchronous.  */
                                              1,                    /* Row writable */
                                              postgres_writable,    /* Table alterable writable */
                                              !postgres_writable,   /* True origin */
                                              wally_is_endpoint(zpath_global_wally),
                                              postgres_writable ? 0 : 500000); /* If writable, don't poll for data,
                                                                                * otherwise, use 1/2s polling */
    } else {
        db_handle = wally_sqlt_create(db_name, /* dbname */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(zpath_global_wally),
                                      0);   /* polling_interval_us */
    }

    if (!db_handle) {
        ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s (It might not exist?)", db_name);
        return ZPATH_RESULT_ERR;
    }

    snprintf(str, sizeof(str), params->use_sqlt ? "sqlt:%s" : "postgres:%s", db_name);

    zpath_global_slave_db = wally_add_origin(zpath_global_wally,
                                             str,
                                             db_handle,
                                             wally_db_register_for_index,
                                             wally_db_deregister_for_index,
                                             wally_db_set_cookie,
                                             wally_db_get_status,
                                             NULL,
                                             postgres_writable ? wally_db_set_min_sequence : NULL,
                                             wally_db_dump_state,
                                             1);
    if (!zpath_global_slave_db) {
        ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
        return ZPATH_RESULT_ERR;
    }

    /* If we are not WALLYD, OR if WALLYD has a remote origin
     * configured, attach remote origin. */
    if ((strcmp(params->role_name, "wallyd") == 0) && (ZPATH_LOCAL_WALLYD_IS_GLOBAL)) {
        if (ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST) {
            ZPATH_LOG(AL_NOTICE, "Attaching remote origin (global: %s(%d)) DB...",
                      ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST,
                      ZPATH_LOCAL_WALLYD_FOHH_REMOTE_PORT);
            result = zpath_app_attach_global_remote(ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST,
                                                    ZPATH_LOCAL_WALLYD_FOHH_REMOTE_PORT,
                                                    NULL);
            if (result) {
                ZPATH_LOG(AL_ERROR, "zpath_app_attach_global_failed for %s(%d): %s",
                          ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST,
                          (int)ZPATH_LOCAL_WALLYD_FOHH_REMOTE_PORT,
                          zpath_result_string(result));
                return result;
            }
            ZPATH_LOG(AL_NOTICE, "Attaching remote instance DB... COMPLETE");
        } else {
            slave_db_only = 1;
        }
    } else {
        char hostname[ARGO_MAX_NAME_LENGTH];
        int zthread_num = -1;
        snprintf(hostname, sizeof(hostname), "g-wally.%s", ZPATH_LOCAL_CLOUD_NAME);
        ZPATH_LOG(AL_NOTICE, "Attaching remote global origin: %s(%d)...", hostname, ZPATH_LOCAL_FOHH_PORT);
        result = zpath_app_attach_global_remote(hostname, ZPATH_LOCAL_FOHH_PORT, &zthread_num);
        if (result) {
            ZPATH_LOG(AL_ERROR, "zpath_app_attach_global failed: %s", zpath_result_string(result));
            return result;
        }
        ZPATH_LOG(AL_NOTICE, "Setting g-wally thread num: %d(%s) as don't kill on terminate",
                                zthread_num, zthread_get_thread_name(zthread_num));
        zthread_set_dont_kill_on_terminate_flag(zthread_num);

        ZPATH_LOG(AL_NOTICE, "Attaching remote instance DB... COMPLETE");
    }

    /* Our global databases are attached up- we can read in the
     * zpath_table table now. */
    result = zpath_table_init(zpath_global_wally,
                              zpath_global_slave_db,
                              zpath_global_remote_db,
                              1);
    if (result) {
        ZPATH_LOG(AL_ERROR, "zpath_table_init failed for global wally");
        return result;
    }

    ZPATH_LOG(AL_NOTICE, "Retrieving cloud configuration from zpath_cloud...");
    result = zpath_cloud_init(zpath_global_wally,
                              zpath_global_slave_db,
                              zpath_global_remote_db,
                              slave_db_only,
                              1/*use zpath_table*/);
    if (result) {
        ZPATH_LOG(AL_ERROR, "zpath_cloud_init failed");
        return result;
    }
    ZPATH_LOG(AL_NOTICE, "Retrieving cloud configuration from zpath_cloud... COMPLETE");

    ZPATH_LOG(AL_NOTICE, "Retrieving instance configuration from zpath_instance...");
    result = zpath_instance_init(zpath_global_wally,
                                 zpath_global_slave_db,
                                 zpath_global_remote_db,
                                 slave_db_only);
    if (result) {
        ZPATH_LOG(AL_ERROR, "zpath_instance_init failed");
        return result;
    }
    ZPATH_LOG(AL_NOTICE, "Retrieving instance configuration from zpath_instance... COMPLETE");

    zpath_debug_wally_add(zpath_global_wally, ZPATH_WALLY_STATS_INTERVAL_US);
    zpath_debug_wally_add(zpath_local_wally, ZPATH_WALLY_STATS_INTERVAL_US);

    init_zpath_app_config_override_registration(zpath_init_common_config_overrides);
    snprintf(str, sizeof(str), "statslog-pg-ccf.%s", ZPATH_LOCAL_CLOUD_NAME);
    ZPATH_LOG(AL_NOTICE, "Sending statistics logs to %s...", str);

    if (itasca_logging_port_he) {
        result = fohh_log_send(str,
                               "statistics_log",
                               str,
                               "apilog",
                               NULL,
                               "statslog",
                               htons(itasca_logging_port_he),
                               NULL, // ssl_ctx
                               0, 0,
                               NULL);
        if (result == ZPATH_RESULT_NO_ERROR) zpath_debug_add_fohh_log_collection(argo_log_get("statistics_log"));
    } else {
        result = fohh_log_attach(str, "statistics_log", str, "/apilog/log/statslog", NULL, 443, 1, 0);
    }

    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not attach stats log to logging system: %s", zpath_result_string(result));
        return result;
    }
    ZPATH_LOG(AL_NOTICE, "Sending statistics logs to %s... COMPLETE", str);

    if (params->personality & ZPATH_APP_PERSONALITY_SEND_EVENTLOG_TO_NETWORK) {
        snprintf(str, sizeof(str), "eventlog-pg-ccf.%s", ZPATH_LOCAL_CLOUD_NAME);
        ZPATH_LOG(AL_NOTICE, "Sending system event logs to %s...", str);
        result = fohh_log_attach(str, "event_log", str, "/apilog/log/eventslog", NULL, 443, 1, 1);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not attach stats log to logging system: %s", zpath_result_string(result));
            return result;
        }
        ZPATH_LOG(AL_NOTICE, "Sending system event logs to %s... COMPLETE", str);
    }

    result = zpath_debug_add_read_command("Get the service IPs for this instance",
                                     "/app/service_ips",
                                     service_ip_callback,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add service ip debug command");
        return result;
    }

    result = zpath_debug_add_read_command("Get the IPv4 service IPs for this instance",
                                     "/app/service_ips_v4",
                                     service_ip_callback_v4,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add service ip debug command");
        return result;
    }

    result = zpath_debug_add_read_command("Get the IPv6 service IPs for this instance",
                                     "/app/service_ips_v6",
                                     service_ip_callback_v6,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add service ip debug command");
        return result;
    }

    result = zpath_debug_add_read_command("Check if application is initialized and running",
                                     "/app/status",
                                     status_callback,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add status debug command");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to shutdown. Much like SIG_TERM",
                                     "/app/shutdown",
                                     service_shutdown_callback,
                                     NULL,
                                    "min_delay", "<optional> min delay after which shutdown starts. Range:[1-14400]",
                                    "max_delay", "<optional> max delay before which shutdown starts. Range:[1-14400]",

                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add shutdown command");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to crash with assert",
                                     "/app/crash/assert",
                                     debug_crash_assert,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /app/crash/assert");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to crash with abort",
                                     "/app/crash/abort",
                                     debug_crash_abort,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /app/crash/abort");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to crash with heartbeat",
                                     "/app/crash/heartbeat",
                                     debug_crash_heartbeat,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /app/crash/heartbeat");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to crash with fortify source",
                                     "/app/crash/fortify",
                                     debug_crash_fortify,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /app/crash/fortify");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to crash with stack smash",
                                     "/app/crash/stack_smash",
                                     debug_crash_stack_smash,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /app/crash/stack_smash");
        return result;
    }

    result = zpath_debug_add_admin_command("Tell application to crash with segfault",
                                     "/app/crash/segfault",
                                     debug_crash_segfault,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /app/crash/segfault");
        return result;
    }

    result = zpath_debug_add_admin_command("System exit",
                                     "/system/ungraceful_shutdown",
                                     zpn_system_ungraceful_shutdown,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /system/ungraceful_shutdown");
        return result;
    }

    result = zpath_debug_add_read_command("Dump dynamically added argo structure fields",
                                     "/argo/structure/dump_dynamic_fields",
                                     debug_argo_structure_dump_dynamic_fields,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not add /argo/structure/dump_dynamic_fields");
        return result;
    }

    result = zpath_app_debug_add_commands();
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could initialize zpath_app_debug_add_commands");
        return result;
    }

    sanitizer_add_debug_commands();

    zpath_init_cloud_config();
    result = zpath_load_zpa_cloud_config(ZPA_CLOUD_CONFIG_FILE_FULL_PATH);
    if (ZPATH_RESULT_NO_ERROR != result) {
        ZPATH_LOG(AL_ERROR, "Could not load zpa_cloud_config");
        return result;
    }

    ZPATH_LOG(AL_NOTICE, "zpath_app_init complete");

    zpath_app_reached_milestone(zpath_app_milestone_app_initialized);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_just_init_shards(int use_sqlt)
{
    return zpath_app_just_init_shards_static(use_sqlt, 0 /*regular_tables*/);
}

int zpath_app_just_init_shards_static(int use_sqlt, int static_wally)
{
    int i;
    int result;
    const char *table_type = (static_wally)? "static tables" : "regular tables";

    for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
        if (static_wally) {
            result = zpath_app_shard_init_static(i, use_sqlt, 1 /*static tables*/);
        } else {
            result = zpath_app_shard_init(i, use_sqlt);
        }
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize shard %d for %s", i, table_type);
            return result;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_init_shards(int skip_zpath_ip_location, int use_sqlt)
{
    int i;
    int result;

    zpath_app_debug_init_fully_loaded_table_tracking();

    for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
        result = zpath_app_shard_init(i, use_sqlt);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize shard %d", i);
            return result;
        }
    }

#define UGH_INIT(__xxx)                                                                     \
    if ((result = __xxx())) {                                                               \
        ZPATH_LOG(AL_ERROR, "Could not init:" #__xxx ": %s", zpath_result_string(result));  \
        return result;                                                                      \
    }

    // This is for initializing tables that can be initialized single-tenant:
#define UGH_UGH_INIT(__xxx)                                                                     \
    if ((result = __xxx(NULL, 0, 0, 0))) {                                                      \
        ZPATH_LOG(AL_ERROR, "Could not init:" #__xxx ": %s", zpath_result_string(result));      \
        return result;                                                                          \
    }

    UGH_INIT(zpath_cidr_lookup_init);
    UGH_INIT(zpath_cidr_lookup_init6);
    UGH_INIT(zpath_ip_table_init);
    UGH_INIT(zpath_entity_init);
    UGH_INIT(zpath_rule_init);
    UGH_INIT(zpath_domainlist_init);
    UGH_INIT(zpath_customer_notification_init);
    UGH_INIT(zpath_category_init);
    if (!skip_zpath_ip_location) {
        UGH_INIT(zpath_ip_location_init);
    }
    UGH_INIT(zpath_tag_entity_init);
    UGH_INIT(zpath_customer_logo_init);
    UGH_INIT(zpath_location_init);
    //UGH_INIT(zpath_log_store_init);
    UGH_UGH_INIT(zpath_customer_init);
    UGH_INIT(zpath_log_config_init);
    UGH_INIT(zpath_customer_log_config_init);

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_init_shards_static(int skip_zpath_ip_location, int use_sqlt)
{
    int i;
    int result;

    for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
        result = zpath_app_shard_init_static(i, use_sqlt, 1 /*static tables*/);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize shard %d for static tables", i);
            return result;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_init_np_shards(int use_sqlt, int use_zpath_table)
{
    int i;
    int result;

    zpath_app_debug_init_fully_loaded_table_tracking();

    for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
        result = zpath_app_np_shard_init(i, use_sqlt, use_zpath_table);
        if (result) {
            ZPATH_LOG(AL_ERROR, "Could not initialize shard %d", i);
            return result;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_add_global_table(struct argo_structure_description *argo_description,
                               wally_row_callback_f *all_rows_callback,
                               void *all_rows_callback_cookie,
                               int multiple_index_consistency,
                               wally_row_fixup_f *fixup_f)
{
    int res;
    struct wally_table *table;

    table = wally_table_create(zpath_global_wally,
                               0,
                               argo_description,
                               all_rows_callback,
                               all_rows_callback_cookie,
                               1,
                               multiple_index_consistency,
                               fixup_f);
    if (!table) {
        ZPATH_LOG(AL_ERROR, "Could not create global table <%s>", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }

    /* Register with zpath_table... */
    res = zpath_table_register(zpath_global_wally,
                               argo_description_get_type(argo_description),
                               zpath_table_default_callback,
                               table);
    if (res) {
        ZPATH_LOG(AL_NOTICE, "Could not register global table <%s>", argo_description_get_type(argo_description));
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_app_add_sharded_table(struct argo_structure_description *argo_description,
                                wally_row_callback_f *all_rows_callback,
                                void *all_rows_callback_cookie,
                                int multiple_index_consistency,
                                wally_row_fixup_f *fixup_f)
{
    return zpath_app_add_sharded_table_static(argo_description, all_rows_callback,
                                                all_rows_callback_cookie, multiple_index_consistency,
                                                fixup_f, 0 /*regular table*/);
}

int zpath_app_add_sharded_table_static(struct argo_structure_description *argo_description,
                                wally_row_callback_f *all_rows_callback,
                                void *all_rows_callback_cookie,
                                int multiple_index_consistency,
                                wally_row_fixup_f *fixup_f,
                                int static_wally)
{
    int shard_index;
    int res;
    const char *table_type = (static_wally)? "static table" : "regular table";

    for (shard_index = 1; shard_index <= ZPATH_CLOUD_SHARD_COUNT; shard_index++) {
        struct wally_table *table;
        struct wally *wally;

        if (static_wally) {
            wally = zpath_shard_wally_static[shard_index];
        } else {
            wally = zpath_shard_wally[shard_index];
        }

        table = wally_table_create(wally,
                                   0,
                                   argo_description,
                                   all_rows_callback,
                                   all_rows_callback_cookie,
                                   1,
                                   multiple_index_consistency,
                                   fixup_f);
        if (!table) {
            ZPATH_LOG(AL_ERROR, "Could not create %s <%s> on shard %d", table_type, argo_description_get_type(argo_description), shard_index);
            return ZPATH_RESULT_ERR;
        }

        /* Register with zpath_table... */
        res = zpath_table_register(wally,
                                   argo_description_get_type(argo_description),
                                   zpath_table_default_callback,
                                   table);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Could not register %s <%s> with zpath_table for shard %d", table_type, argo_description_get_type(argo_description), shard_index);
            return res;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_add_np_sharded_table(struct argo_structure_description *argo_description,
                                   wally_row_callback_f *all_rows_callback,
                                   void *all_rows_callback_cookie,
                                   int multiple_index_consistency,
                                   wally_row_fixup_f *fixup_f)
{
    char *table_name = argo_description_get_type(argo_description);
    int res;

    for (int shard_index = 1; shard_index <= ZPATH_CLOUD_SHARD_COUNT; shard_index++) {
        struct wally_table *table;
        struct wally *wally;

        wally = zpath_np_shard_wally[shard_index];

        table = wally_table_create(wally,
                                   0,
                                   argo_description,
                                   all_rows_callback,
                                   all_rows_callback_cookie,
                                   1,
                                   multiple_index_consistency,
                                   fixup_f);
        if (!table) {
            ZPATH_LOG(AL_ERROR, "Could not create <%s> on np shard %d", table_name, shard_index);
            return ZPATH_RESULT_ERR;
        }

        /* Register with zpath_table... */
        res = zpath_table_register(wally,
                                   table_name,
                                   zpath_table_default_callback,
                                   table);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Could not register <%s> with zpath_table for np shard %d", table_name, shard_index);
            return res;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int fully_loaded_callback(void *response_callback_cookie,
                                 struct wally_registrant *registrant,
                                 struct wally_table *table,
                                 int64_t request_id,
                                 int row_count)
{
    int32_t *count = response_callback_cookie;
    char total_time[50] = "NA";

    int32_t asynch =__sync_sub_and_fetch_4(count, 1);
    if(registrant && registrant->turnaround_time_us > 0) {
        snprintf(total_time , sizeof(total_time),"%"PRId64" us" , registrant->turnaround_time_us);
        registrant->turnaround_time_us = 0;
    }
    ZPATH_LOG(AL_NOTICE, "%s: table %s: Fully loaded: Read %d rows, asynch = %d load time %s",
        wally_table_wally_name(table), wally_table_name(table), row_count, (int)asynch, total_time);

    zpath_app_debug_track_fully_loaded_table_end(table, row_count, WALLY_RESULT_NO_ERROR);

    return WALLY_RESULT_NO_ERROR;
}

int zpath_app_fully_loaded_global_table(struct argo_structure_description *argo_description,
                                        wally_row_callback_f *all_rows_callback,
                                        void *all_rows_callback_cookie,
                                        wally_row_fixup_f *fixup_f,
                                        int sync_pause_register)
{
    int res;
    int32_t wait_int = 0;
    struct wally_table *table;
    struct wally_index_column *col;

    table = wally_table_create_named_db(zpath_global_wally,
                                        0,
                                        argo_description_get_type(argo_description),
                                        argo_description,
                                        all_rows_callback,
                                        all_rows_callback_cookie,
                                        1,
                                        1, /* Multiple index
                                            * consistency is
                                            * needed to force a
                                            * load from scratch to
                                            * get going- otherwise
                                            * old partial tables
                                            * will be there, and
                                            * we won't read
                                            * correctly.  */
                                        1 /* Fully loaded */,
                                        fixup_f);
    if (!table) {
        ZPATH_LOG(AL_ERROR, "Could not create table <%s>", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }

    if (sync_pause_register) {
        /* Register as a pausable table */
        zpath_lib_sync_pause_register_table(zpath_global_wally, table);
    }

    /* Register for all rows... */
    col = wally_table_get_index(table, "");
    if (!col) {
        ZPATH_LOG(AL_ERROR, "Could not get full table index for %s", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }

    zpath_app_debug_track_fully_loaded_table_start(table);

    res = wally_table_register_for_row(NULL,
                                       col,
                                       NULL,
                                       0,
                                       0,
                                       0,
                                       0,
                                       0,
                                       0,
                                       fully_loaded_callback,
                                       &wait_int);
    if (res) {
        if (res != WALLY_RESULT_ASYNCHRONOUS) {
            ZPATH_LOG(AL_ERROR, "Could not register for all rows from %s: %s",  argo_description_get_type(argo_description), wally_error_strings[res]);
            zpath_app_debug_track_fully_loaded_table_end(table, 0, res);
            return WALLY_RESULT_ERR;
        } else {
            /* We will wait for this value to return to 0 indicating that config has been loaded */
            __sync_add_and_fetch_4(&(wait_int), 1);
            ZPATH_LOG(AL_NOTICE, "%s: Waiting for data to be read...", argo_description_get_type(argo_description));
        }
        res = WALLY_RESULT_NO_ERROR;
    } else {
        zpath_app_debug_track_fully_loaded_table_end(table, 0, 0);
    }

    /* Register with zpath_table... */
    res = zpath_table_register(zpath_global_wally,
                               argo_description_get_type(argo_description),
                               zpath_table_default_callback,
                               table);
    if (res) {
        ZPATH_LOG(AL_NOTICE, "Could not register table <%s> with zpath_table", argo_description_get_type(argo_description));
        return res;
    }

    int i = 0;
    while (wait_int != 0) {
        if ((i % 100) == 0) {
            ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s", argo_description_get_type(argo_description));
        }
        usleep(10000);
        i++;
    }

    if (col->is_null) {
        ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s -- COMPLETE. interest_list_row_count: %ld",
                         argo_description_get_type(argo_description), (long)col->null_column_interest->interest_list_row_count);
    }else{
        ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s -- COMPLETE.",
                         argo_description_get_type(argo_description));
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_fully_loaded_sharded_table(struct argo_structure_description *argo_description,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         wally_row_fixup_f *fixup_f)
{
    return zpath_app_fully_loaded_sharded_table_static(argo_description, all_rows_callback,
                                                  all_rows_callback_cookie, fixup_f, 0 /*regular table*/);
}

int zpath_app_fully_loaded_sharded_table_static(struct argo_structure_description *argo_description,
                                                  wally_row_callback_f *all_rows_callback,
                                                  void *all_rows_callback_cookie,
                                                  wally_row_fixup_f *fixup_f,
                                                  int static_wally)
{
    int shard_index;
    int res;
    int32_t wait_int = 0;
    const char *table_type = (static_wally)? "static table" : "regular table";

    for (shard_index = 1; shard_index <= ZPATH_CLOUD_SHARD_COUNT; shard_index++) {
        struct wally_table *table;
        struct wally_index_column *col;
        struct wally *wally = NULL;

        if (static_wally) {
            wally = zpath_shard_wally_static[shard_index];
        } else {
            wally = zpath_shard_wally[shard_index];
        }

        table = wally_table_create_named_db(wally,
                                            0,
                                            argo_description_get_type(argo_description),
                                            argo_description,
                                            all_rows_callback,
                                            all_rows_callback_cookie,
                                            1,
                                            1, /* Multiple index
                                                * consistency is
                                                * needed to force a
                                                * load from scratch to
                                                * get going- otherwise
                                                * old partial tables
                                                * will be there, and
                                                * we won't read
                                                * correctly.  */
                                            1 /* Fully loaded */,
                                            fixup_f);
        if (!table) {
            ZPATH_LOG(AL_ERROR, "Could not create %s <%s> on shard %d", argo_description_get_type(argo_description), table_type, shard_index);
            return ZPATH_RESULT_ERR;
        }

        /* Register for all rows... */
        col = wally_table_get_index(table, "");
        if (!col) {
            ZPATH_LOG(AL_ERROR, "Could not get full %s index for %s", argo_description_get_type(argo_description), table_type);
            return ZPATH_RESULT_ERR;
        }

        zpath_app_debug_track_fully_loaded_table_start(table);

        res = wally_table_register_for_row(NULL,
                                           col,
                                           NULL,
                                           0,
                                           0,
                                           0,
                                           0,
                                           0,
                                           0,
                                           fully_loaded_callback,
                                           &wait_int);
        if (res) {
            if (res != WALLY_RESULT_ASYNCHRONOUS) {
                ZPATH_LOG(AL_ERROR, "Could not register for all rows for %s from %s: %s",  argo_description_get_type(argo_description), table_type, wally_error_strings[res]);
                zpath_app_debug_track_fully_loaded_table_end(table, 0, res);
                return WALLY_RESULT_ERR;
            } else {
                int32_t current_count = __sync_add_and_fetch_4(&(wait_int), 1);
                ZPATH_LOG(AL_NOTICE, "%s: Waiting for %d shards to be read for %s...", argo_description_get_type(argo_description), (int) current_count, table_type);
            }
            res = WALLY_RESULT_NO_ERROR;
        } else {
            zpath_app_debug_track_fully_loaded_table_end(table, 0, 0);
        }

        /* Register with zpath_table... */
        res = zpath_table_register(wally,
                                   argo_description_get_type(argo_description),
                                   zpath_table_default_callback,
                                   table);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Could not register %s <%s> with zpath_table for shard %d", table_type, argo_description_get_type(argo_description), shard_index);
            return res;
        }
    }

    int i = 0;
    int wrote_one = 0;
    while (wait_int != 0) {
        if ((i % 100) == 0) {
            wrote_one = 1;
            ZPATH_LOG(AL_NOTICE, "Waiting to fully load %s %s (%d shards left)", table_type, argo_description_get_type(argo_description), (int) wait_int);
        }
        usleep(10000);
        i++;
    }
    if (wrote_one) {
        ZPATH_LOG(AL_NOTICE, "Waiting to fully load %s %s (%d shards left) -- COMPLETE", table_type, argo_description_get_type(argo_description), (int) wait_int);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_fully_loaded_customer_table(struct wally_table **table_result,
                                          int64_t customer_gid,
                                          struct wally *single_tenant_wally,
                                          struct argo_structure_description *argo_description,
                                          wally_row_callback_f *all_rows_callback,
                                          void *all_rows_callback_cookie,
                                          wally_row_fixup_f *fixup_f,
                                          int register_with_zpath_table)
{
    int res;
    int32_t wait_int = 0;

    struct wally_table *table;
    struct wally_index_column *col;

    table = wally_table_create_named_db(single_tenant_wally,
                                        0,
                                        argo_description_get_type(argo_description),
                                        argo_description,
                                        all_rows_callback,
                                        all_rows_callback_cookie,
                                        1, // Use all origins.
                                        1, /* Multiple index
                                            * consistency is
                                            * needed to force a
                                            * load from scratch to
                                            * get going- otherwise
                                            * old partial tables
                                            * will be there, and
                                            * we won't read
                                            * correctly.  */
                                        1 /* Fully loaded */,
                                        fixup_f);
    if (!table) {
        ZPATH_LOG(AL_ERROR, "Could not create table <%s>", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }

    /* Register for all rows... */
    if (strncmp(table->name, ZPATH_CUSTOMER_TABLE, strlen(ZPATH_CUSTOMER_TABLE)) == 0) {
        col = wally_table_get_index(table, "gid");
    }  else if (strncmp(table->name, ZPATH_RULE_TABLE, strlen(ZPATH_RULE_TABLE)) == 0)  {
        col = wally_table_get_index(table, "and_entity");
    } else if (strncmp(table->name, ZPATH_SERVICE_TABLE, strlen(ZPATH_SERVICE_TABLE)) == 0)  {
        col = wally_table_get_index(table, "customer_id");
    } else {
        col = wally_table_get_index(table, "customer_gid");
    }

    if (!col) {
        ZPATH_LOG(AL_ERROR, "Could not get table index for %s, column customer_gid", argo_description_get_type(argo_description));
        return ZPATH_RESULT_ERR;
    }
    res = wally_table_register_for_row_through_fully_loaded(NULL,           // registrant
                                                            col,            // column
                                                            &customer_gid,  // key
                                                            sizeof(customer_gid),
                                                            0,              // request_id
                                                            0,              // request_sequence
                                                            request_atleast_once(), // request_atleast_one
                                                            0,              // just_callback
                                                            0,              // unique_registration
                                                            fully_loaded_callback, // response_callback
                                                            &wait_int);     // response_callback_cookie
    if (res) {
        if (res != WALLY_RESULT_ASYNCHRONOUS) {
            ZPATH_LOG(AL_ERROR, "Could not register for all rows for customer from %s: %s",  argo_description_get_type(argo_description), wally_error_strings[res]);
            return WALLY_RESULT_ERR;
        } else {
            /* We will wait for this value to return to 0 indicating that config has been loaded */
            __sync_add_and_fetch_4(&(wait_int), 1);
            ZPATH_LOG(AL_NOTICE, "%s: Waiting for data to be read...", argo_description_get_type(argo_description));
        }
        res = WALLY_RESULT_NO_ERROR;
    }

    if (register_with_zpath_table) {
        /* Register with zpath_table... */
        res = zpath_table_register(single_tenant_wally,
                                   argo_description_get_type(argo_description),
                                   zpath_table_default_callback,
                                   table);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Could not register table <%s> with zpath_table", argo_description_get_type(argo_description));
            return res;
        }
    }

    int i = 0;
    int wrote_one = 0;
    while (wait_int != 0) {
        if ((i % 100) == 0) {
            wrote_one = 1;
            ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s", argo_description_get_type(argo_description));
        }
        usleep(10000);
        i++;
    }
    if (wrote_one) {
        ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s -- COMPLETE", argo_description_get_type(argo_description));
        if (col->is_null) {
            ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s -- COMPLETE. interest_list_row_count: %ld",
                            argo_description_get_type(argo_description), (long)col->null_column_interest->interest_list_row_count);
        }else{
            ZPATH_LOG(AL_NOTICE, "Waiting to fully load table %s -- COMPLETE.",
                            argo_description_get_type(argo_description));
        }
    }

    *table_result = table;

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_app_add_writable_sharded_table(struct argo_structure_description *argo_description,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         int multiple_index_consistency,
                                         wally_row_fixup_f *fixup_f)
{
    int shard_index;
    int res;

    for (shard_index = 1; shard_index <= ZPATH_CLOUD_SHARD_COUNT; shard_index++) {
        struct wally_table *table;

        table = wally_table_create(zpath_shard_wally[shard_index],
                                   1,
                                   argo_description,
                                   all_rows_callback,
                                   all_rows_callback_cookie,
                                   1,
                                   multiple_index_consistency,
                                   fixup_f);
        if (!table) {
            ZPATH_LOG(AL_ERROR, "Could not create table <%s> on shard %d", argo_description_get_type(argo_description), shard_index);
            return ZPATH_RESULT_ERR;
        }

        /* Register with zpath_table... */
        res = zpath_table_register(zpath_shard_wally[shard_index],
                                   argo_description_get_type(argo_description),
                                   zpath_table_default_callback,
                                   table);
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Could not register table <%s> with zpath_table for shard %d", argo_description_get_type(argo_description), shard_index);
            return res;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

struct wally_index_column *zpath_app_get_global_index(const char *table_name,
                                                      const char *field_name)
{
    struct wally_index_column *col;
    struct wally_table *table;

    table = wally_table_get(zpath_global_wally,
                            table_name);
    if (!table) {
        ZPATH_LOG(AL_ERROR, "Could not get global table <%s>", table_name);
        return NULL;
    }

    col = wally_table_get_index(table, field_name);
    if (!col) {
        ZPATH_LOG(AL_ERROR, "Could not get global shard table index <%s>  table <%s>", field_name ? field_name : "null", table_name);
        return NULL;
    }

    return col;
}

struct wally_index_column **zpath_app_get_sharded_index(const char *table_name,
                                                        const char *field_name)
{
    return zpath_app_get_sharded_index_static(table_name, field_name, 0/*regular_table*/);
}

struct wally_index_column **zpath_app_get_sharded_index_static(const char *table_name,
                                                        const char *field_name,
                                                        int static_wally)
{
    struct wally_index_column **cols;
    int shard_index;
    const char *table_type = (static_wally)? "static table" : "regular table";

    cols = (struct wally_index_column **)ZLIB_CALLOC(ZPATH_MAX_SHARDS * sizeof(struct wally_index_column *));
    if (cols) {
        for (shard_index = 1; shard_index <= ZPATH_CLOUD_SHARD_COUNT; shard_index++) {
            struct wally_table *table;

            if (static_wally) {
                table = wally_table_get(zpath_shard_wally_static[shard_index],
                                        table_name);
            } else {
                table = wally_table_get(zpath_shard_wally[shard_index],
                                        table_name);
            }
            if (!table) {
                ZPATH_LOG(AL_ERROR, "Could not get table <%s> on shard %d for %s", table_name, shard_index, table_type);
                ZLIB_FREE(cols); // Free the structure to avoid a leak
                return NULL;
            }

            cols[shard_index] = wally_table_get_index(table, field_name);
            if (!cols[shard_index]) {
                ZPATH_LOG(AL_ERROR, "Could not get index <%s> for %s <%s> on shard %d", field_name ? field_name : "null", table_type, table_name, shard_index);
                ZLIB_FREE(cols); // Free the structure to avoid a leak
                return NULL;
            }
        }
    }
    return cols;
}

struct wally_index_column **zpath_app_get_np_sharded_index(const char *table_name, const char *field_name)
{
    struct wally_index_column **cols;
    int shard_index;

    cols = (struct wally_index_column **)ZLIB_CALLOC(ZPATH_MAX_SHARDS * sizeof(struct wally_index_column *));
    if (cols) {
        for (shard_index = 1; shard_index <= ZPATH_CLOUD_SHARD_COUNT; shard_index++) {
            struct wally_table *table;

            table = wally_table_get(zpath_np_shard_wally[shard_index], table_name);
            if (!table) {
                ZPATH_LOG(AL_ERROR, "Could not get table <%s> on np shard %d", table_name, shard_index);
                ZLIB_FREE(cols); // Free the structure to avoid a leak
                return NULL;
            }

            cols[shard_index] = wally_table_get_index(table, field_name);
            if (!cols[shard_index]) {
                ZPATH_LOG(AL_ERROR, "Could not get index <%s> for <%s> on np shard %d", field_name ? field_name : "null", table_name, shard_index);
                ZLIB_FREE(cols); // Free the structure to avoid a leak
                return NULL;
            }
        }
    }
    return cols;
}

void zpath_app_set_specific_max_logging_mb(int max_logging_mb)
{
    if (max_logging_mb > 0)
        zpath_app_specific_max_logging_mb = max_logging_mb;
}

void zpath_app_set_specific_argo_mem_threshold(int threshold_percent)
{
    zpath_app_specific_argo_mem_threshold = threshold_percent;
}

void zpath_simple_app_init_params_default(struct zpath_simple_app_init_params *params)
{
    if (params) {
        memset(params, 0, sizeof(*params));
        params->role_version_string = ZPATH_VERSION;
        params->fohh_thread_count = 4;
        params->fohh_watchdog_s = 20;
        params->log_stderr = 1;
        params->log_syslog = 1;
        params->dump_lib_versions = 1;
        params->fips_mode = 1;
        params->load_zpa_cloud_config = 1;
        params->rbac_support = 0;   /* no RBAC support unless configured */
    }
}

void zpath_app_init_params_default(struct zpath_app_init_params *params)
{
    if (params) {
        memset(params, 0, sizeof(*params));
        params->local_db_hostname = "localhost";
        params->local_db_name = "local_db";
        params->role_version_string = ZPATH_VERSION;
        params->fohh_thread_count = 4;
        params->fohh_watchdog_s = 20;
        params->system_start_us = epoch_us();
        params->is_endpoint = 1;
        params->fips_mode = 1;
    }
}
