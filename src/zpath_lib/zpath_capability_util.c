/*
 * zpath_capability_util.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <ctype.h>
#include "zpath_capability_util.h"
#include "zpath_lib/zpath_debug.h"

#define MAX_CUSTOM_CAPS 7
#define MAX_CAP_LENGTH 32

static const char *full_caps[] = {
    "cap_net_admin",
    "cap_net_bind_service",
    "cap_net_raw",
    "cap_sys_nice",
    "cap_sys_time",
    "cap_setfcap",
    "cap_sys_resource"
};
static const char *default_minimal_caps[] = {"cap_net_raw"};
static const int default_minimal_caps_count = 1;

static int is_openshift_env_local = 0;
static int is_kubernetes_env_local = 0;
static int env_flags_initialized = 0;

static const char *default_docker_caps[] = {"cap_net_admin",
                                            "cap_net_bind_service",
                                            "cap_net_raw",
                                            "cap_sys_nice",
                                            "cap_sys_time"};

static void str_to_lower(char *s) {
    for (; *s; ++s) {
        *s = (char)tolower((unsigned char)*s);
    }
}

static void detect_container_platform_env(void) {
    if (env_flags_initialized)
        return;

    const char *openshift_str = getenv("OPENSHIFT_ENVIRONMENT");
    if (openshift_str && strncmp(openshift_str, "enabled", sizeof("enabled")) == 0) {
        is_openshift_env_local = 1;
        ZPATH_LOG(AL_NOTICE, "OpenShift environment detected inside capabilities util");
    }

    const char *kubernetes_str = getenv("KUBERNETES_ENVIRONMENT");
    if (kubernetes_str && strncmp(kubernetes_str, "enabled", sizeof("enabled")) == 0) {
        is_kubernetes_env_local = 1;
        ZPATH_LOG(AL_NOTICE, "Kubernetes environment detected inside capabilities util");
    }

    env_flags_initialized = 1;
}

zpath_capability_mode_t zpath_get_capability_mode() {
    const char *env = getenv("ZPA_CAPABILITY_MODE");
    if (!env) {
        if (is_openshift_env_local || is_kubernetes_env_local) {
            ZPATH_LOG(AL_INFO, "Openshift/k8s env: Final capabilities selected as full since ZPA_CAPABILITY_MODE is not set");
            return CAP_MODE_FULL;
        } else {
            ZPATH_LOG(AL_INFO, "Final capabilities selected as default since ZPA_CAPABILITY_MODE is not set");
            return CAP_MODE_DEFAULT;
        }
    }
    if (strcmp(env, "full") == 0) return CAP_MODE_FULL;
    if (strcmp(env, "custom") == 0) return CAP_MODE_CUSTOM;
    if (strcmp(env, "minimal") == 0) return CAP_MODE_MINIMAL;
    return CAP_MODE_DEFAULT;
}

const char **zpath_get_custom_capabilities(int *count) {
    static char cap_list[MAX_CUSTOM_CAPS][MAX_CAP_LENGTH];
    static const char *cap_ptrs[MAX_CUSTOM_CAPS];
    const char *env = getenv("ZPA_CUSTOM_CAPABILITIES");
    *count = 0;
    int use_default_minimal_caps = 0;

    if (!env || strlen(env) == 0) {
        *count = default_minimal_caps_count;
        use_default_minimal_caps = 1;
        goto done;
    }

    // Only allow these capabilities (without cap_ prefix) in the format which is given by users
    static const char *allowed_caps[] = {
            "net_admin", "net_bind_service", "net_raw", "sys_nice", "sys_time", "setfcap", "sys_resource"};
    const int allowed_cap_count = sizeof(allowed_caps) / sizeof(allowed_caps[0]);

    char *input = strdup(env);
    if (!input) {
        *count = default_minimal_caps_count;
        use_default_minimal_caps = 1;
        goto done;
    }

    char *saveptr = NULL;
    char *token = strtok_r(input, ",", &saveptr);

    while (token && *count < MAX_CUSTOM_CAPS) {
        while (*token == ' ')
            token++;  // trim leading spaces
        char *end = token + strlen(token) - 1;
        while (end > token && *end == ' ')
            *end-- = '\0';  // Trim trailing spaces

        str_to_lower(token);  // convert token to lowercase

        for (int i = 0; i < allowed_cap_count; i++) {
            if (strcmp(token, allowed_caps[i]) == 0) {
                snprintf(cap_list[*count], MAX_CAP_LENGTH, "cap_%s", token);
                cap_ptrs[*count] = cap_list[*count];
                (*count)++;
                break;
            }
        }
        token = strtok_r(NULL, ",", &saveptr);
    }

    free(input);

    // If nothing valid found, return cap_net_raw
    if (*count == 0) {
        *count = default_minimal_caps_count;
        use_default_minimal_caps = 1;
        goto done;
    }
done:
    if (use_default_minimal_caps) {
        zpath_log_final_caps(CAP_MODE_CUSTOM, default_minimal_caps, *count);
        return default_minimal_caps;
    } else {
        zpath_log_final_caps(CAP_MODE_CUSTOM, cap_ptrs, *count);
        return cap_ptrs;
    }
}

const char **zpath_get_full_capabilities(int *count) {
    *count = sizeof(full_caps) / sizeof(full_caps[0]);
    zpath_log_final_caps(CAP_MODE_FULL, full_caps, *count);
    return full_caps;
}

const char *const *zpath_get_final_capabilities(int *count) {
    if (!count) {
        ZPATH_LOG(AL_ERROR, "count must not be NULL");
        return NULL;
    }
    detect_container_platform_env();

    zpath_capability_mode_t mode = zpath_get_capability_mode();

    if (mode == CAP_MODE_CUSTOM) {
        return zpath_get_custom_capabilities(count);
    } else if (mode == CAP_MODE_FULL) {
        return zpath_get_full_capabilities(count);
    } else if (mode == CAP_MODE_MINIMAL) {
        *count = default_minimal_caps_count;
        zpath_log_final_caps(CAP_MODE_MINIMAL, default_minimal_caps, *count);
        return default_minimal_caps;
    } else {
        *count = sizeof(default_docker_caps) / sizeof(default_docker_caps[0]);
        zpath_log_final_caps(CAP_MODE_DEFAULT, default_docker_caps, *count);
        return default_docker_caps;
    }
}

void zpath_build_cap_string(const char * const *cap_list, int count, char *cap_string, size_t cap_string_size, int is_container_env)
{
    size_t offset = 0;

    for (int i = 0; i < count; i++) {
        if (is_container_env && strcmp(cap_list[i], "cap_sys_boot") == 0) {
            continue; // Skip cap_sys_boot in containers
        }

        size_t needed = strlen(cap_list[i]) + 2;
        if (offset + needed >= cap_string_size)
            break;

        strcpy(&cap_string[offset], cap_list[i]);
        offset += strlen(cap_list[i]);
        cap_string[offset++] = ',';
    }

    if (offset > 0 && cap_string[offset - 1] == ',') {
        cap_string[offset - 1] = '\0'; // Trim trailing comma
    } else {
        cap_string[offset] = '\0';
    }

    strncat(cap_string, "+pe", cap_string_size - strlen(cap_string) - 1);
}
const char *zpath_mode_to_string(zpath_capability_mode_t mode) {
    switch (mode) {
        case CAP_MODE_FULL: return "full";
        case CAP_MODE_CUSTOM: return "custom";
        case CAP_MODE_MINIMAL: return "minimal";
        default: return "default";
    }
}

void zpath_log_final_caps(zpath_capability_mode_t mode, const char * const *cap_list, int cap_count) {
    char cap_string[256] = {0};
    size_t offset = 0;

    for (int i = 0; i < cap_count && offset < sizeof(cap_string) - 1; i++) {
        int written = snprintf(cap_string + offset, sizeof(cap_string) - offset, "%s, ", cap_list[i]);
        if (written < 0 || written >= (int)(sizeof(cap_string) - offset))
            break;
        offset += written;
    }

    if (offset > 2)
        cap_string[offset - 2] = '\0';

    ZPATH_LOG(AL_INFO, "Final capabilities selected (mode: %s): %s", zpath_mode_to_string(mode), cap_string);
}
int is_openshift_environment(void) {
    if (!env_flags_initialized) {
        detect_container_platform_env();
    }
    return is_openshift_env_local;
}

int is_kubernetes_environment(void) {
    if (!env_flags_initialized) {
        detect_container_platform_env();
    }
    return is_kubernetes_env_local;
}
