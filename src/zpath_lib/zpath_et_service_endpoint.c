/*
 * zpath_et_service_endpoint.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 */


#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>

#include "argo/argo.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_private.h"
#include "zpath_misc/zpath_misc.h"
#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/http_parser.h"
#include "argo/argo_hash.h"
#include "argo/argo_log.h"

#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app_debug.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_et_service_endpoint_compiled.h"

struct argo_structure_description *zpath_et_service_endpoint_description;

struct wally_index_column *zpath_et_service_endpoint_column;
struct wally_index_column *zpath_et_service_endpoint_zone_id_column;

static zpath_mutex_t service_endpt_lock;
static struct argo_hash_table *service_endpt_queues;

static int itasca_logging_port_he = 0;
static uint64_t producer_log_repeat_count = 1;

TAILQ_HEAD(zpath_service_endpoint_object_element_head, zpath_service_endpoint_object_element);

struct zpath_service_endpoint_object_element {
    TAILQ_ENTRY(zpath_service_endpoint_object_element) list;
    struct argo_object *object;
};

struct zpath_service_endpoint_queue {
    zpath_mutex_t lock;
    int64_t service_endpoint_id;
    struct zpath_service_endpoint_object_element_head list;
    struct argo_log_collection *collection;
    int collection_attached;
    struct argo_object *row;
};

char *service_endpoint_names[] = {
    [zpath_customer_log_type_stats] = "service.zpa.log.stats",
    [zpath_customer_log_type_dns_raw] = "service.zpa.log.dns.raw",
    [zpath_customer_log_type_dns_aggregate] = "service.zpa.log.dns.aggregate",
    [zpath_customer_log_type_zpn_auth] = "service.zpa.log.zpn.auth",
    [zpath_customer_log_type_zpn_ast_auth] = "service.zpa.log.zpn.ast_auth",
    [zpath_customer_log_type_zpn_sys_auth] = "service.zpa.log.zpn.sys_auth",
    [zpath_customer_log_type_zpn_health] = "service.zpa.log.zpn.health",
    [zpath_customer_log_type_zpn_transaction] = "service.zpa.log.zpn.transaction",
    [zpath_customer_log_type_zpn_clientless_auth] = "service.zpa.log.zpn.clientless_auth",
    [zpath_customer_log_type_zpn_clientless_transaction] = "service.zpa.log.zpn.clientless_transaction",
    [zpath_customer_log_type_ci_stats] = "service.zpa.log.ci_stats",
    [zpath_customer_log_type_comprehensive_stats] = "service.zpa.log.zpn.comprehensive_stats",
    [zpath_customer_log_type_zpn_http_inspection] = "service.zpa.log.zpn.http.insp",
    [zpath_customer_log_type_zpn_http_api_inspection] = "service.zpa.log.zpn.http.api_insp",
    [zpath_customer_log_type_admin_probe] = "service.zpa.log.zpn.command_probe",
    [zpath_customer_log_type_zpn_auth_state] = "service.zpa.log.zpn.auth.state",
    [zpath_customer_log_type_pb_comprehensive_stats] = "service.zpa.log.zpn.pb_comprehensive_stats",
    [zpath_customer_log_type_zpn_event] = "service.zpa.log.zpn.event",
    [zpath_customer_log_type_zpn_csp] = "service.zpa.log.zpn.csp",
    [zpath_customer_log_type_zpn_pra_session] = "service.zpa.log.zpn.zpn_pra_session",
    [zpath_customer_log_type_zpn_sc_auth] = "service.zpa.log.zpn.sc_auth",
    [zpath_customer_log_type_sc_comprehensive_stats] = "service.zpa.log.zpn.sc_comprehensive_stats",
    [zpath_customer_log_type_zpn_adp] = "service.zpa.log.zpn.adp",
    [zpath_customer_log_type_zpn_ptag] = "service.zpa.log.zpn.ptag",
    [zpath_customer_log_type_zpn_pra_session_email]= "service.zpa.log.zpn.zpn_pra_session_email",
    [zpath_customer_log_type_zpn_email]= "service.zpa.log.zpn.zpn_email",
    [zpath_customer_log_type_zia_health]= "service.zpa.log.zpn.zia_health",
    [zpath_customer_log_type_np_comprehensive_stats]= "service.zpa.log.zpn.np.comprehensive_stats",
    [zpath_customer_log_type_zpn_app_inspection] = "service.zpa.log.zpn.app.inspection",
    [zpath_customer_log_type_zpn_krb_inspection] = "service.zpa.log.zpn.krb.inspection",
    [zpath_customer_log_type_zpn_ldap_inspection] = "service.zpa.log.zpn.ldap.inspection",
    [zpath_customer_log_type_zpn_smb_inspection] = "service.zpa.log.zpn.smb.inspection",
    [zpath_customer_log_type_zpn_managed_browser_payload] = "service.zpa.log.zpn.managed.browser.payload",
    [zpath_customer_log_type_zpn_np_nw_comp_stats] = "service.zpa.log.zpn.np.nw.comp.stats",
};

struct zpath_service_endpoint_queue *zpath_service_endpoint_get_queue(int64_t service_endpt_id);



int zpath_et_service_endpoint_response_callback(void *cookie,
											  struct wally_registrant *registrant,
											  struct wally_table *table,
											  int64_t sequence,
											  int status)
{
    struct wally_interlock *lock = (struct wally_interlock *) cookie;
    ZPATH_DEBUG_DOMAIN_LOOKUP("Received response, status(count) = %d",
                              status);
    if (lock) wally_interlock_release(lock);
    return ZPATH_RESULT_NO_ERROR;
}

/* This will get called as a callback from collection_thread function once
 * the thread for the argo collection is triggered. This happens only when
 * the argo collection becomes active (i.e., objects are logged to it).
 *
 * Hence, now, we get to defer the creation of FOHH connection underpinning such a
 * collection until the collection actually start getting objects logged into it.
 */
static void zpath_service_endpoint_fohh_attach(void *cookie)
{
    struct argo_log_collection_create_reader *args = cookie;
    int res;

    if (itasca_logging_port_he) {
        /* Topic comes from path. Path is of the form
         * /apilog/log/topic_name. We search for last '/' to find the
         * topic name */
        char *last_slash = args->path;
        char *w = args->path;
        const char *temp_argo_collection_name = NULL;

        temp_argo_collection_name = ZLIB_STRDUP(args->name, strlen(args->name));

        while (*w) {
            if (*w == '/') last_slash = w;
            w++;
        }
        res = fohh_log_send(args->name,
                            temp_argo_collection_name, //will be directly dereferenced later in states_walk hence strduped
                            args->domain_name,
                            "apilog",
                            NULL,
                            last_slash + 1,  // Topic begins character after last slash in path
                            htons(itasca_logging_port_he),
                            NULL, 0, 0,
                            NULL); // ssl_ctx
        if (res) {
            ZPATH_LOG(AL_CRITICAL, "%s: Could not fohh_log_send due to error %s", args->name, zpath_result_string(res));
        }
        res = zpath_debug_add_fohh_log_collection(argo_log_get(args->name)); //for debugging output
        if (res) {
            ZPATH_LOG(AL_CRITICAL, "%s: Failed to add to FOHH debug log due to error %s", args->name, zpath_result_string(res));
        }
    } else {
        res = fohh_log_attach(args->name,
                              args->name,
                              args->domain_name,
                              args->path,
                              args->identity,
                              args->service_port_he,
                              args->use_ssl,
                              0);
        if (res) {
            ZPATH_LOG(AL_CRITICAL, "%s: Could not fohh_log_attach due to error %s", args->name, zpath_result_string(res));
        }
    }
    return;
}

int zpath_et_service_endpoint_row_callback(void *cookie,
										 struct wally_registrant *registrant,
										 struct wally_table *table,
										 struct argo_object *previous_row,
										 struct argo_object *row,
										 int64_t request_id)
{
    struct zpath_service_endpoint_queue *queue;
    struct et_service_endpoint *service_endpt = row->base_structure_void;
    int create_collection = 0;
    struct http_parser_url url;
    int res;

    if (zpath_debug & ZPATH_DEBUG_DOMAIN_LOOKUP_BIT) {
        char dump[1000];

        if (argo_object_dump(row, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPATH_DEBUG_DOMAIN_LOOKUP("Row callback: %s", dump);
        }
    }

    if(service_endpt->deleted) {
        // cleanup queue, collection
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!service_endpt->service_url) {
        /* Don't create collection for this... */
        create_collection = 0;
        ZPATH_LOG(AL_ERROR, "%ld: Service Endpoint: no url", (long) service_endpt->id);
    } else {
        // check if our endpoint is a zpn or customer infra endpoint
        // if the service_endpoint isn't a log.zpn endpoint and it is not a log.ci endpoint we will ignore it
        // strstr returns NULL on failure to find substring so we will check to see if we have neither of our substrings and skip if we don't
        if(strstr(service_endpt->name, "service.zpa.log.zpn") == NULL && strstr(service_endpt->name, "service.zpa.log.ci_") == NULL ) {
            ZPATH_LOG(AL_DEBUG,"skipping collection creation for [%s]",service_endpt->name);
            return ZPATH_RESULT_NO_ERROR;
        }
        memset(&url, 0, sizeof(url));
        res = http_parser_parse_url(service_endpt->service_url,
                                    strlen(service_endpt->service_url),
                                    0,
                                    &url);
        if (res ||
            (url.field_data[UF_HOST].len == 0) ||
            (url.field_data[UF_PATH].len == 0) ||
            (url.field_data[UF_SCHEMA].len < 4) ||
            (url.field_data[UF_SCHEMA].len > 5) ||
            ((url.field_data[UF_SCHEMA].len == 4) &&
             (strncmp("http", &(service_endpt->service_url[url.field_data[UF_SCHEMA].off]), 4))) ||
            ((url.field_data[UF_SCHEMA].len == 5) &&
             (strncmp("https", &(service_endpt->service_url[url.field_data[UF_SCHEMA].off]), 5))) ||
            (url.port == 0)) {
            /* URL parse failed. Don't create collection... */
            create_collection = 0;
            ZPATH_LOG(AL_ERROR, "%ld: Service Endpoint url parse failed: (require schema = %d, host = %d, port = %d, path = %d) %s",
                      (long) service_endpt->id,
                      url.field_data[UF_SCHEMA].len,
                      url.field_data[UF_HOST].len,
                      url.port,
                      url.field_data[UF_PATH].len,
                      service_endpt->service_url);
        } else {
            create_collection = 1;
        }
    }

    if(create_collection) {
        queue = zpath_service_endpoint_get_queue(service_endpt->id);
        if (queue) {
            struct argo_log_collection *new_collection;
            struct zpath_service_endpoint_object_element *ele;
            int created = 0;
            char name[1000];
            char host[256];
            char path[256];

            ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);

            snprintf(name, sizeof(name), "SE_%ld_%s", (long)service_endpt->id, service_endpt->service_url);
            snprintf(host, sizeof(host), "%.*s", url.field_data[UF_HOST].len, &(service_endpt->service_url[url.field_data[UF_HOST].off]));
            snprintf(path, sizeof(path), "%.*s", url.field_data[UF_PATH].len, &(service_endpt->service_url[url.field_data[UF_PATH].off]));

            ZPATH_DEBUG_LOG_STORE("%ld: Creating name = %s, host = %s, path = %s", (long) service_endpt->id, name, host, path);

            struct argo_log_collection_create_reader create_reader = {0};
            snprintf(create_reader.name, sizeof(create_reader.name), "%s", name);
            snprintf(create_reader.domain_name, sizeof(create_reader.domain_name), "%s", host);
            snprintf(create_reader.path, sizeof(create_reader.path), "%s", path);
            snprintf(create_reader.identity, sizeof(create_reader.identity), "%s", ZPATH_LOCAL_FULL_NAME);
            create_reader.service_port_he = url.port;
            create_reader.use_ssl = url.field_data[UF_SCHEMA].len == 4 ? 0 : 1;
            create_reader.create_reader_cb = zpath_service_endpoint_fohh_attach;

            new_collection = argo_log_create(name, &create_reader, &created); //reader created by collection when it becomes active
            if (!new_collection) {
                ZPATH_LOG(AL_ERROR, "Could not create Service Endpoint collection %s", name);
                ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
                return WALLY_RESULT_NO_ERROR;
            }
            if (created) {
                res = zpath_debug_add_collection(new_collection);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "%ld: Could not add to debug set", (long) service_endpt->id);
                }
            } else {
                res = ZPATH_RESULT_NO_ERROR;
                /* Fall through to fixing up queued logs */
            }
            if (res) {
                ZPATH_LOG(AL_ERROR, "%ld: Could not fohh_attach [%s]", (long) service_endpt->id, zpath_result_string(res));
            } else {
                if (queue->collection) {
                    ZPATH_LOG(AL_CRITICAL, "Fix Memory Leak");
                    // clean collection
                }
                queue->collection = new_collection;
                /* Dequeue everything that was waiting... */
                while ((ele = TAILQ_FIRST(&(queue->list)))) {
                    ZPATH_DEBUG_LOG_STORE("%ld: Dequeueing stored log entries", (long) service_endpt->id);
                    TAILQ_REMOVE(&(queue->list), ele, list);
                    argo_log_log_object_immediate(queue->collection, ele->object);
                    argo_object_release(ele->object);
                    ZLIB_FREE(ele);
                }
            }

            ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
        } else {
            ZPATH_LOG(AL_ERROR, "%ld: could not get queue?", (long) service_endpt->id);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_et_service_endpoint_init(struct wally *wally,
                                   struct wally_origin *slave_db,
                                   struct wally_origin *remote_db,
                                   int use_itasca_logging_port_he)
{
    struct wally_index_column *column;
    struct wally_interlock lock;
    int res;
    struct wally_table *table;

    itasca_logging_port_he = use_itasca_logging_port_he;

    ZPATH_LOG(AL_NOTICE, "Initializing et_service_endpoint table");
    service_endpt_queues = argo_hash_alloc(7, 1);
    if (!service_endpt_queues) {
    		ZPATH_LOG(AL_ERROR, "Could NOT allocate service_endpt_queues");
        return ZPATH_RESULT_ERR;
    }

    if (!(zpath_et_service_endpoint_description = argo_register_global_structure(ET_SERVICE_ENDPOINT_HELPER))) return ZPATH_RESULT_ERR;

    table = wally_table_create(wally,
                               1,
							   zpath_et_service_endpoint_description,
							   zpath_et_service_endpoint_row_callback,
                               NULL,
                               0,
                               0,
                               NULL);
    if (!table) {
        ZPATH_LOG(AL_ERROR, "Could not get et_service_endpoint table.");
        return ZPATH_RESULT_ERR;
    }

    /* Register as a pausable table */
    zpath_lib_sync_pause_register_table(wally, table);

    res = wally_table_add_origin(wally, table, slave_db);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not add slave_db origin for et_service_endpoint table.");
        return res;
    }

    res = wally_table_add_origin(wally, table, remote_db);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not add remote_db origin for et_service_endpoint table.");
        return res;
    }

    column = wally_table_get_index(table, "");
    if (!column) {
        ZPATH_LOG(AL_ERROR, "Could not get null column for et_service_endpoint table");
        return ZPATH_RESULT_ERR;
    }

    zpath_et_service_endpoint_column = wally_table_get_index(table, "id");
    if (!zpath_et_service_endpoint_column) {
        ZPATH_LOG(AL_ERROR, "Could not get et_service_endpoint table index et_service_endpoint table on.");
        return ZPATH_RESULT_ERR;
    }
    zpath_et_service_endpoint_zone_id_column = wally_table_get_index(table, "zone_id");
    if (!zpath_et_service_endpoint_column) {
        ZPATH_LOG(AL_ERROR, "Could not get et_service_endpoint table index et_service_endpoint table on.");
        return ZPATH_RESULT_ERR;
    }

    zpath_app_debug_track_fully_loaded_table_start(table);
    wally_interlock_lock_1(&lock);
    res = wally_table_register_for_row(NULL, column, NULL, 0, 0, 0, 1, 0, 0, zpath_et_service_endpoint_response_callback, &lock);
    while (res == WALLY_RESULT_NOT_READY) {
        ZPATH_LOG(AL_ERROR, "register_for_row: %s", zpath_result_string(res));
        res = wally_table_deregister_for_row(NULL, column, NULL, 0);
        sleep(1);
        res = wally_table_register_for_row(NULL, column, NULL, 0, 0, 0, 1, 0, 0, zpath_et_service_endpoint_response_callback, &lock);
    }

    ZPATH_LOG(AL_NOTICE, "Reading zpath_et_service_endpoint...");
    wally_interlock_lock_2(&lock);
    ZPATH_LOG(AL_NOTICE, "Reading zpath_et_service_endpoint... COMPLETE");
    zpath_app_debug_track_fully_loaded_table_end(table, table->row_count, WALLY_RESULT_NO_ERROR);

    /* Register with zpath_table... */
    res = zpath_table_register(wally, "et_service_endpoint", zpath_table_default_callback, table);
    if (res) {
        ZPATH_LOG(AL_NOTICE, "Could not register with zpath_table");
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpath_et_service_endpoint_get(int64_t zone_id,
								struct et_service_endpoint **endpoints,
								size_t *endpoints_count)
{
    int res;

    res = wally_table_get_rows_fast(zpath_et_service_endpoint_zone_id_column,
    									&zone_id,
                                    sizeof(zone_id),
                                    (void **) endpoints,
									endpoints_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);

    return res;
}

struct zpath_service_endpoint_queue *zpath_service_endpoint_get_queue(int64_t service_endpt_id)
{
    struct zpath_service_endpoint_queue *queue;
    int res;

    ZPATH_MUTEX_LOCK(&service_endpt_lock, __FILE__, __LINE__);

    queue = argo_hash_lookup(service_endpt_queues,
                             &service_endpt_id,
                             sizeof(service_endpt_id),
                             NULL);
    if (!queue) {
        queue = ZLIB_CALLOC(sizeof(*queue));
        if (!queue) {
            ZPATH_MUTEX_UNLOCK(&service_endpt_lock, __FILE__, __LINE__);
            return NULL;
        }
        TAILQ_INIT(&(queue->list));
        queue->lock = ZPATH_MUTEX_INIT;
        queue->service_endpoint_id = service_endpt_id;

        res = argo_hash_store(service_endpt_queues,
                              &service_endpt_id,
                              sizeof(service_endpt_id),
                              0,
                              queue);
        if (res) {
            ZPATH_LOG(AL_CRITICAL, "%ld: Could not store?", (long) service_endpt_id);
            ZLIB_FREE(queue);
            ZPATH_MUTEX_UNLOCK(&service_endpt_lock, __FILE__, __LINE__);
            return NULL;
        }
    }
    ZPATH_MUTEX_UNLOCK(&service_endpt_lock, __FILE__, __LINE__);

    return queue;
}

static int zpath_send_log_to_service_endpoint(int64_t zone_id,
                                              const char *log_name,
                                              const char *instance_name,
                                              int64_t instance_gid,
                                              const char *role,
                                              enum zpath_customer_log_type log_type,
                                              const char *priority,
                                              const char *disposition,
                                              const char *system_name,
                                              const char *category_name,
                                              struct argo_structure_description *description,
                                              void *data)
{
    struct et_service_endpoint *endpoints[100];
    size_t endpoints_count = 100;
    int res;
    size_t i;
    struct argo_object *obj;
    struct zpath_service_endpoint_queue *queue;
    int logged_one = 0;
    uint64_t repeat;

	res = zpath_et_service_endpoint_get(zone_id, endpoints, &endpoints_count);
	if (res != ZPATH_RESULT_NO_ERROR) {
		ZPATH_LOG(AL_WARNING, "Could not find Service Endpoint for Zone Id [%ld] log_name [%s] res [%s]",
				(long)zone_id, log_name, zpath_result_string(res));
		return res;
	}

    for (repeat = 0; repeat < producer_log_repeat_count; repeat++) {
        // for each service endpoint send log object
        if (log_type == zpath_customer_log_type_zpn_event) {
            obj = argo_log_create_event_log_object(log_name,
                                                   priority,
                                                   disposition,
                                                   system_name,
                                                   category_name,
                                                   instance_name,
                                                   instance_gid,
                                                   description,
                                                   data);
        } else {
            obj = argo_log_create_log_object(epoch_us(),
                                             argo_log_priority_info,
                                             0,
                                             log_name,
                                             instance_name,
                                             instance_gid,
                                             description,
                                             data,
                                             role);
        }

        if (!obj) {
            ZPATH_LOG(AL_ERROR, "Could not create object");
            return ZPATH_RESULT_ERR;
        }

        for(i = 0; i < endpoints_count; i++) {

            if(strcmp(endpoints[i]->name, service_endpoint_names[log_type]) != 0) {
                continue;
            }
            logged_one = 1;

            // send object to the correct queue/collection
            queue = zpath_service_endpoint_get_queue(endpoints[i]->id);
            if(queue) {
                if (queue->collection) {
                    res = argo_log_log_object_immediate(queue->collection,
                                                        obj);
                } else {
                    ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);
                    if (queue->collection) {
                        res = argo_log_log_object_immediate(queue->collection,
                                                            obj);
                    } else {
                        if (repeat == 0) { //Log onto queue only the first time
                            struct zpath_service_endpoint_object_element *ele;

                            ele = ZLIB_CALLOC(sizeof(*ele));
                            if (!ele) {
                                res = ZPATH_RESULT_NO_MEMORY;
                            } else {
                                ele->object = obj;
                                TAILQ_INSERT_TAIL(&(queue->list), ele, list);
                                res = ZPATH_RESULT_NO_ERROR;
                                argo_object_hold(obj);
                            }
                        }
                    }
                    ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
                }
            }
            else {
                ZPATH_LOG(AL_WARNING,"Could not get queue for service endpoint [%ld] [%s]",
                        (long)endpoints[i]->id, endpoints[i]->name);
            }
        }

        argo_object_release(obj);
    } /*End-of-repeat-for*/

    if (!logged_one) {
        ZPATH_DEBUG_LOG_ENDPOINT("No endpoint for logging type %s for zone ID %ld", service_endpoint_names[log_type], (long) zone_id);
    }
	return res;
}

int zpath_service_endpoint_log_struct(int64_t customer_gid,
								      const char *log_name,
									  enum zpath_customer_log_type log_type,
                                      const char *priority,
                                      const char *disposition,
                                      const char *system_name,
                                      const char *category_name,
								      struct argo_structure_description *description,
									  void *data)
{
    return zpath_service_endpoint_log_struct_config(customer_gid,
                                                    log_name,
                                                    log_type,
                                                    description,
                                                    ZPATH_LOCAL_FULL_NAME,
                                                    ZPATH_INSTANCE_GID,
                                                    NULL,
                                                    priority,
                                                    disposition,
                                                    system_name,
                                                    category_name,
                                                    data);
}

int zpath_service_endpoint_log_struct_config(int64_t customer_gid,
                                             const char *log_name,
                                             enum zpath_customer_log_type log_type,
                                             struct argo_structure_description *description,
                                             const char *instance_name,
                                             int64_t instance_gid,
                                             const char *role,
                                             const char *priority,
                                             const char *disposition,
                                             const char *system_name,
                                             const char *category_name,
                                             void *data)
{
	int res;
	struct et_zone *zones[10];
	struct et_customer_zone *customer_zones[100];
	size_t zones_count = 10;
	size_t customer_zones_count = 100;
	size_t i;

	res = zpath_et_customer_zone_get(customer_gid, customer_zones, &customer_zones_count);
	if(res == ZPATH_RESULT_NOT_FOUND) {
		// use 'default' zone
		res = zpath_et_zone_get_by_name(ZPATH_ET_ZONE_DEFAULT_NAME,
										strlen(ZPATH_ET_ZONE_DEFAULT_NAME),
										zones,
										&zones_count);
		if(res != ZPATH_RESULT_NO_ERROR) {
			ZPATH_LOG(AL_WARNING, "Could not find default zone for customer [%ld]", (long)customer_gid);
			return ZPATH_RESULT_NO_ERROR;
		}
		if (zones_count > 1) {
			// there should be only ONE default zone
			ZPATH_LOG(AL_WARNING,"Multiple DEFAULT Zones Found [%d]", (int)zones_count);
		}
		// get endpoints for zone_id and send log entries
	    for(i = 0; i < zones_count; i++) {
			res = zpath_send_log_to_service_endpoint(zones[i]->id,
													log_name,
                                                    instance_name,
                                                    instance_gid,
                                                    role,
													log_type,
                                                    priority,
                                                    disposition,
                                                    system_name,
                                                    category_name,
													description,
													data);
			if(res != ZPATH_RESULT_NO_ERROR) {
				ZPATH_LOG(AL_DEBUG, "zpath_send_log_to_service_endpoint zone [%s] returned [%s]",
						zones[i]->name, zpath_result_string(res));
			}
	    }
		return res;
	}
	else if (res != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_WARNING, "Could not find zones for customer [%ld]", (long)customer_gid);
        return 0;
    }

    for(i = 0; i < customer_zones_count; i++) {
		res = zpath_send_log_to_service_endpoint(customer_zones[i]->zone_id,
												log_name,
                                                instance_name,
                                                instance_gid,
                                                role,
												log_type,
                                                priority,
                                                disposition,
                                                system_name,
                                                category_name,
												description,
												data);
		if(res != ZPATH_RESULT_NO_ERROR) {
			ZPATH_LOG(AL_DEBUG, "zpath_send_log_to_service_endpoint returned [%s]", zpath_result_string(res));
		}
    }
    return res;
}

int zpath_debug_update_natural_logs_repeat_count(struct zpath_debug_state*  request_state,
                                                 const char **              query_values,
                                                 int                        query_value_count,
                                                 void*                      cookie)
{
    if (query_values[0]) {
        int64_t repeat_count = strtol(query_values[0], NULL, 0);
        if (repeat_count > 0) {
            ZPATH_LOG(AL_NOTICE,"Updating natural c-pg log repeater: From %"PRIu64" to %"PRId64,
                                            producer_log_repeat_count, repeat_count);
            producer_log_repeat_count = repeat_count;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}
