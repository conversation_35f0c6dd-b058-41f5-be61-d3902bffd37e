/*
 * zpn_oauth_utils.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <unistd.h>
#include <dirent.h>
#include <string.h>
#include <time.h>
#include <sys/stat.h>
#include "fohh/fohh_http.h"
#include "zcdns/zcdns_libevent.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_lib.h"
#include <openssl/x509v3.h>
#include "zpath_oauth_utils.h"
#include "parson/parson.h"
#include "zpath_misc/zsysinfo.h"
#include "zpath_lib/zpath_system_linux.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"

int is_oauth_enrollment_disabled()
{
    if (access(OAUTH_ENROLL_DISABLE_FLAG, F_OK) == 0) {
        return 1;
    }
    return 0;
}

int oauth_rand_bytes(char* rand_str, size_t len) {
    const char chlng_set[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                             "abcdefghijklmnopqrstuvwxyz"
                             "1234567890";
    int c = 0;
    int limit = sizeof(chlng_set)-1;

    if (len <= 0) {
        return 1;
    }

    srand(time(NULL));

    for(int i = 0; i < len; i++) {
        c = rand() % limit;
        rand_str[i] = chlng_set[c];
    }
    rand_str[len] = '\0';

    return 0;
}

int oauth_get_challenge(char *oauth_challenge, size_t size) {
    if (oauth_rand_bytes(oauth_challenge, size) != 0) {
        ZPN_LOG(AL_ERROR, "Failed to generate OAuth challenge");
        return 1;
    }

    return 0;
}

/* Function to generate SHA256 hash */
int oauth_get_sha256_hash(const char* input, size_t input_len, unsigned char hash[], size_t *hash_length)
{
    uint8_t sha[SHA256_DIGEST_LENGTH];
    EVP_MD_CTX *ctx;

    /* Input validation */
    if (input == NULL) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Invalid input");
        return ZPATH_RESULT_ERR;
    }

    /* Check if output memory is good */
    if (hash == NULL) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - output memory is NULL");
        return ZPATH_RESULT_ERR;
    }

    if (!(ctx = EVP_MD_CTX_create())) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Could not SHA create");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), NULL)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Could not do SHA init");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestUpdate(ctx, input, input_len)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Could not do SHA update");
        return ZPATH_RESULT_ERR;
    }

    unsigned int shalen = sizeof(sha);
    if (1 != EVP_DigestFinal_ex(ctx, sha, &shalen)) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Could not get SHA final");
        EVP_MD_CTX_destroy(ctx);
        return ZPATH_RESULT_ERR;
    }
    EVP_MD_CTX_destroy(ctx);

    if (shalen != sizeof(sha)) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Bad SHA length");
        return ZPATH_RESULT_ERR;
    }

    if (*hash_length < shalen) {
        ZPATH_LOG(AL_ERROR, "[OAuth] Get challenge hash - Output Buffer too small. Cannot write hash");
        return ZPATH_RESULT_ERR;
    }

    *hash_length = shalen;

    memcpy(hash, sha, shalen);

    return ZPATH_RESULT_NO_ERROR;
}

/* Calculate SHA256 of the content */
int oauth_get_challenge_hash(const char *input, size_t input_len, char *hash_hex_str, size_t hash_hex_len)
{
    unsigned char hash[OAUTH_SHA256_HASH_SIZE];  // Generating a 256 bit hash
    int i;
    int res;

    if (input_len <= 0 || (hash_hex_len < OAUTH_SHA256_HASH_SIZE)) {
        return ZPATH_RESULT_ERR;
    }

    /* Hash of the content in a form of hex string */
    size_t hash_len = sizeof(hash);
    res = oauth_get_sha256_hash(input, input_len, hash, &hash_len);
    if (res != ZPATH_RESULT_NO_ERROR || hash_len != OAUTH_SHA256_HASH_SIZE) {
        return res;
    }

    /* Convert hash byte array to hex string */
    char *curr = hash_hex_str;
    for (i = 0; i < hash_len; i++) {
        curr += sprintf(curr, "%02x", hash[i]);
    }

    return ZPATH_RESULT_NO_ERROR;
}
