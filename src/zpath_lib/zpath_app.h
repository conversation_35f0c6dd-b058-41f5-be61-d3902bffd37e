/*
 * zpath_app.h. Copyright (C) 2013-2025 Zscaler, Inc. All Rights Reserved.
 */

/*
 * zpath_app - Provide initialization for basic app functionality
 * within zpath cloud. This software initializes all the databases
 * (wally, fohh, argo, debug, etc), initializes all remote access
 * (instance database, primarily), and otherwise configures the system
 * to be running as indicated in the local database (zpath_local).
 *
 */

#ifndef __ZPATH_APP_H__
#define __ZPATH_APP_H__

#include "argo/argo_log.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_lib.h"

#define ZPATH_MAX_SHARDS 256
#define ZPATH_SHARD_FROM_GID(x) (((uint64_t)x) >> 56)

#define ZPATH_MAX_TABLE_ROWS_GET 1000
#define ZPATH_INSTANCE_NAME_LEN 99
#define ZPATH_APP_SITEC_INSTANCE_NAME "zpa-pcc-child"

#define ZPATH_WALLY_STATS_INTERVAL_US (60l*1000l*1000l)

#define ZPATH_APP_DEBUG_INSTANCE_BIT     (uint64_t)0x000000001

#define ZPATH_APP_LOG(priority, format...) ARGO_LOG(event_collection, priority, "zpath_app", ##format)
#define ZPATH_APP_DEBUG_LOG(condition, format...) ARGO_DEBUG_LOG(condition, event_collection, argo_log_priority_debug, "zpath_app", ##format)

#define ZPATH_APP_DEBUG_INSTANCE(format...) ZPATH_APP_DEBUG_LOG(zpath_app_debug & ZPATH_APP_DEBUG_INSTANCE_BIT, ##format)

#define ZPN_STANDARD_COMPONENT_ONETIME_INIT_CHECK \
do { \
    static int comp_initialized = 0; \
    static pthread_mutex_t comp_init_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER; \
    pthread_mutex_lock(&comp_init_lock); \
    if (comp_initialized) { \
        pthread_mutex_unlock(&comp_init_lock); \
        return 0; \
    } else {\
        comp_initialized = 1; \
        pthread_mutex_unlock(&comp_init_lock);\
    }\
} while(0)

#define ZPATH_ARGO_RPC_REGISTRATIONS_MAX 576

extern int64_t zpath_system_start_time;
extern int64_t zpath_rbac_enabled;
extern int64_t zpath_rbac_supported;

/*
 * Set this to non-zero when initialization is complete!
 */
extern int zpath_service_init_complete;
extern int64_t zpath_service_init_complete_s;

/*
 * This will be set if the service is shutting down!
 */
extern int zpath_service_shutdown;

/*
 * The instance ID.
 */
extern int zpath_app_instance_id;

extern int zpath_config_override_init_complete;

extern int zpath_app_is_endpoint;
extern struct wally *zpath_global_wally;
extern struct wally *zpath_np_global_wally;
extern struct wally *zpath_shard_wally[ZPATH_MAX_SHARDS];
extern struct wally *zpath_shard_wally_static[ZPATH_MAX_SHARDS];
extern struct wally *zpath_np_shard_wally[ZPATH_MAX_SHARDS];
extern struct wally_origin *zpath_global_slave_db;
extern struct wally_origin *zpath_global_remote_db;
extern struct wally_origin *zpath_np_global_slave_db;
extern struct wally_origin *zpath_np_global_remote_db;
extern struct argo_log_collection *zpath_event_collection;
extern struct argo_log_collection *zpath_stats_collection;

extern struct argo_structure_description *zthread_rusage_desc;
extern struct argo_structure_description *zpath_inittime_desc;

struct zpath_inittime {      /* _ARGO: object_definition */
    uint64_t inittime_s;     /* _ARGO: integer */
    uint64_t build_tag;      /* _ARGO: integer */
    uint64_t version_tag;    /* _ARGO: integer */
    uint64_t major_ver;      /* _ARGO: integer */
    uint64_t minor_ver;      /* _ARGO: integer */
    uint64_t patch_ver;      /* _ARGO: integer */
    int32_t arch;            /* _ARGO: integer */
    double os_version_id;    /* _ARGO: double */
    char os_version_str[64]; /* _ARGO: string */
};

/*
 * Each zpath_app can exhibit multiple personality traits(hence bitmap, not enum) or nothing at all(default in this
 * case).
 */
#define ZPATH_APP_PERSONALITY_MINIMUM_MEMORY_FOOTPRINT 0x00000001
#define ZPATH_APP_PERSONALITY_NO_FILE_LOGGING          0x00000002
#define ZPATH_APP_PERSONALITY_SEND_EVENTLOG_TO_NETWORK 0x00000004

/*
 * By default the argo_log buffer is 64k entries long. But if the app says that it wanted to operate in a memory
 * constrained environment, we are ok with allocating 4k entrires long buffer.
 */
#define ZPATH_APP_ARGO_LOG_BUFFER_SIZE_IN_EXPONENT_DEFAULT           16
#define ZPATH_APP_ARGO_LOG_BUFFER_SIZE_IN_EXPONENT_MINIMUM_FOOTPRINT 12

/* if the domian is valid ipv4 or IPv6 address */
int is_valid_ip(char *domain);

/*
 * Get wally to use for accessing specified shard. Creates wally if it
 * does not exist.
 */
int zpath_app_shard_init(int shard_index, int use_sqlt);
int zpath_app_np_shard_init(int shard_index, int use_sqlt, int use_zpath_table);

int zpath_app_create_np_global(int use_sqlt);

/*
 * Get wally to use for accessing specified shard for static tables.
 * Creates wally if it does not exist.
 */
int zpath_app_shard_init_static(int shard_index, int use_sqlt, int static_wally);

/*
 * Initialize all the tables that are needed for shard access. Without
 * all the tables initailized by this routine, zpath_app_shard_init
 * will fail.
 */
int zpath_app_shard_pre_init(void);

/*
 * Initializes:
 *
 * ARGO, FOHH, WALLY, LOGGING
 *
 * Optional: Stderr log output.
 * Optional: filename to log to. (argo format)
 * Optional: Debug interface.
 * Optional: personality
 *
 * And that's it. No 'readers' are initialized for logging- that is up
 * to the caller to create. Logging specifics (instance name) can be
 * reconfigured with later calls to argo_log.
 *
 * Note: wally init is exceptionally lightweight. Real wally
 * initialization occurs when someone creates a wally.
 */
struct zpath_simple_app_init_params
{
    const char *instance_name;
    const char *role_name;
    const char *role_version_string;
    char *root_cert_file;
    char *cert_chain_file;
    char *private_key_file;
    int fohh_thread_count;
    int fohh_watchdog_s;
    const char *log_filename;
    int debug_port;
    uint32_t personality;
    uint8_t log_stderr:1,
            log_syslog:1,
            debuglog:1,
            dump_lib_versions:1,
            fips_mode:1,
            load_zpa_cloud_config:1,
            debugcmd_pool_support:1,
            rbac_support:1;
};

void zpath_simple_app_init_params_default(struct zpath_simple_app_init_params *params);

/*
 * There was a long list of function arguments for zpath_simple_app_init() before.
 * We have consolidated those arguments into a single structure zpath_simple_app_init_params.
 * A caller of zpath_simple_app_init() should call zpath_simple_app_init_params_default()
 * right after instantiating zpath_simple_app_init_params so that typical argument values
 * will be set. Then override specific argument values as needed.
 */
int zpath_simple_app_init(const struct zpath_simple_app_init_params *params);

/*
 * Initialize basic app funcitonality.
 *
 * Note: stderr is used for logging output until the logging
 * infrastructure is running.
 *
 * instance_id - The instance number on this physical system that is
 * being run.
 *
 * local_db_name - The name of the local database, within which we
 *    will get configuration from the table 'zpath_local'. (This table
 *    contains the name of the slave DB we use)
 *
 * override_global_db_name - Whether or not to override the global db
 *    name (based on instance name) with the configured db name (based
 *    on local_db contents)
 *
 * writable_slave_db - Whether or not we are allowed to write to the
 *    slave db.
 *
 * remote_slave_db - Whether or not we query a remote system for our
 *    slave database info, or just use the slave DB contents.
 *    (i.e. if the true origin DB IS our slave DB.)
 *
 * role_name - The name of this application, for log initialization
 *    and instance configuration verification.
 *
 * role_version_string - The version of software being run...
 *
 * fohh_thread_count - The number of FOHH (worker) threads to run.
 *
 * fohh_watchdog - The time (in seconds) for the FOHH watchdog.
 *
 * system_start_in_us - The start time of the system, in epoch form,
 *    in microseconds.
 *
 * use_slave_db - If set, then we should replicate shard databases
 *    locally.
 *
 * incremental_shard_ports - If set (non-zero), then the shard wally
 *    client ports used will be shard 0 = 444, shard 1 = 445,
 *    etc. This configuration exists in order to talk to local
 *    wallyd's over multiple ports via localhost.
 *
 * completely_disable_ssl - If set, then this app tries to do all
 *    communication without SSL at all.
 *
 * is_endpoint - If set, then this application is an 'endpoint', and
 *    will treat 'soft-deletes' as 'hard deletes'
 *
 * syslog_debug -
 *
 * use_localhost_resolver - If set, will prefer to use localhost
 *    resolution rather than contents of /etc/resolv.conf
 *
 * personality - tell the personality of the application which calls this library.
 * Based on the personality of the app, we can tune the environment for the app.
 *
 * fips_mode - Use 1/0 to enable/disable fips during ssl init.
 *
 * This routine initializes:
 *
 * argo
 * argo_log
 *   - event collection (zpath_event_collection)
 *   - statistics collection (zpath_stats_collection)
 * wally (zpath_wally)
 *   - local database.
 *   - slave database. (zpath_slave_db)
 * fohh
 * ssl
 * libevent
 * zpath_lib
 *   - debug
 *   - local
 *   - instance
 *
 * This routine only sets up slave DB, that's it. Controls whether or
 * not the slave DB is writable and/or remote. That's all.
 */
struct zpath_app_init_params
{
    int instance_id;
    const char *zpath_local_config_file;
    const char *local_db_hostname;
    const char *local_db_name;
    const char *role_name;
    const char *role_version_string;
    int fohh_thread_count;
    int fohh_watchdog_s;
    const char *fohh_private_key_pem;
    int64_t system_start_us;
    uint32_t personality;
    uint8_t use_sqlt:1,
            incremental_shard_ports_arg:1,
            completely_disable_ssl:1,
            is_endpoint:1,
            debuglog:1,
            use_localhost_resolver:1,
            fips_mode:1,
            debugcmd_pool_support:1;
};

void zpath_app_init_params_default(struct zpath_app_init_params *params);

/*
 * There was a long list of function arguments for zpath_app_init() before.
 * We have consolidated those arguments into a single structure zpath_app_init_params.
 * A caller of zpath_app_init() should call zpath_app_init_params_default()
 * right after instantiating zpath_app_init_params so that typical argument values
 * will be set. Then override specific argument values as needed.
 */
int zpath_app_init(const struct zpath_app_init_params *params);

void update_itasca_logs_port(int itasca_log_port);

#define ZPATH_APP_EVENT_LOG_LVL_CONSOLE "-log-lvl-console"
#define ZPATH_APP_EVENT_LOG_LVL_SYSLOG "-log-lvl-syslog"
int zpath_app_logging_parse_args(int* argc, char *argv[]);
int zpath_app_logging_init(const char* role_name,
                            int stderr_log_mask,
                            int syslog_log_mask,
                            int abbreviated,
                            int add_debug_commands);
void zpath_app_logging_usage_print();
const char* zpath_app_get_role_name();
void zpath_app_enable_console_log();

/*
 * Callback enum- each callback is called once to see if shutdown can
 * start, Once to start shutdown, and once to see if shutdown can be
 * complete.
 *
 * NOTE: If changes made, update zpath_app_shutdown_name.
 */
enum zpath_app_shutdown_state {
    zpath_app_shutdown_can_start = 0,
    zpath_app_shutdown_start,
    zpath_app_shutdown_can_complete,
    zpath_app_shutdown_complete
};

/*
 * A callback that is called when the system is shutting down.
 *
 * This routine will be called once, in order, for each shutdown_state.
 *
 * This routine should return 'ZPATH_RESULT_NO_ERROR' if the system can
 * proceed with shutdown. Any other response will prolong the shutdown
 * process- this routine will be called again, with the same arguments.
 */
typedef int (zpath_app_shutdown_verify_callback)(void *cookie,
                                                 enum zpath_app_shutdown_state state);

/*
 * Register a function to be checked for shutdown handling. This
 * routine is successively callled with _can_start, _start, and
 * _can_complete arguments.
 *
 * All callbacks must pass _can_start without failure before callbacks
 * will proceed for _start. And likewise for _can_complete.
 *
 * Note that if shutdown takes 'too long', a heartbeat may expire.
 *
 * Shutdown is indicated to the app by sending it a SIG_TERM.
 */
int zpath_app_shutdown_notice(zpath_app_shutdown_verify_callback *callback,
                              void *cookie);

/*
 * Get a name for the shutdown state.
 */
const char *zpath_app_shutdown_name(enum zpath_app_shutdown_state state);

/*
 * run the application shutdown hooks.
 * timeout_s - number of seconds allowed in this process.
 * exit_proc - whether the process terminates at the end of the call.
 */
int zpath_app_shutdown(int64_t timeout_s, int exit_proc);

/*
 * Inform zpath module about start of zpath init...
 */
void zpath_registration_started(int64_t time);

/*
 * Inform zpath module about completion of zpath init...
 */
void zpath_registration_completed(void);

/*
 * To add a 'sharded' table dynamically, use this routine after
 * zpath_app_init is called.
 *
 * Any failure that is returnes is a very hard error, and should
 * precipiate an abort.
 */
int zpath_app_add_sharded_table(struct argo_structure_description *argo_description,
                                wally_row_callback_f *all_rows_callback,
                                void *all_rows_callback_cookie,
                                int multiple_index_consistency,
                                wally_row_fixup_f *fixup_f);
int zpath_app_add_sharded_table_static(struct argo_structure_description *argo_description,
                                wally_row_callback_f *all_rows_callback,
                                void *all_rows_callback_cookie,
                                int multiple_index_consistency,
                                wally_row_fixup_f *fixup_f,
                                int static_wally);

int zpath_app_add_np_sharded_table(struct argo_structure_description *argo_description,
                                   wally_row_callback_f *all_rows_callback,
                                   void *all_rows_callback_cookie,
                                   int multiple_index_consistency,
                                   wally_row_fixup_f *fixup_f);

int zpath_app_fully_loaded_sharded_table(struct argo_structure_description *argo_description,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         wally_row_fixup_f *fixup_f);
int zpath_app_fully_loaded_sharded_table_static(struct argo_structure_description *argo_description,
                                                  wally_row_callback_f *all_rows_callback,
                                                  void *all_rows_callback_cookie,
                                                  wally_row_fixup_f *fixup_f,
                                                  int static_wally);
int zpath_app_add_writable_sharded_table(struct argo_structure_description *argo_description,
                                         wally_row_callback_f *all_rows_callback,
                                         void *all_rows_callback_cookie,
                                         int multiple_index_consistency,
                                         wally_row_fixup_f *fixup_f);
/* Same as sharded table, but, well, global. Duh. */
int zpath_app_add_global_table(struct argo_structure_description *argo_description,
                               wally_row_callback_f *all_rows_callback,
                               void *all_rows_callback_cookie,
                               int multiple_index_consistency,
                               wally_row_fixup_f *fixup_f);
int zpath_app_fully_loaded_global_table(struct argo_structure_description *argo_description,
                                        wally_row_callback_f *all_rows_callback,
                                        void *all_rows_callback_cookie,
                                        wally_row_fixup_f *fixup_f,
                                        int sync_pause_register);
/*
 * Add a fully loaded table, but by customer. (Can be used by
 * connectors, private brokers, etc)
 */
int zpath_app_fully_loaded_customer_table(struct wally_table **table_result,
                                          int64_t customer_gid,
                                          struct wally *single_tenant_wally,
                                          struct argo_structure_description *argo_description,
                                          wally_row_callback_f *all_rows_callback,
                                          void *all_rows_callback_cookie,
                                          wally_row_fixup_f *fixup_f,
                                          int register_with_zpath_table);


/*
 * This routine will retrieve a pointer to (up to) 256 indexed column
 * references, one for each shard, for accessing data based on that
 * column. (Or NULL on failure)
 */
struct wally_index_column **zpath_app_get_sharded_index(const char *table_name,
                                                        const char *field_name);
struct wally_index_column **zpath_app_get_sharded_index_static(const char *table_name,
                                                        const char *field_name,
                                                        int static_wally);

struct wally_index_column **zpath_app_get_np_sharded_index(const char *table_name, const char *field_name);

/* Same as sharded index, but just one, and global */
struct wally_index_column *zpath_app_get_global_index(const char *table_name,
                                                      const char *field_name);
/*
 * If you wish to use all the shard databases, call this routine after
 * zpath_app_init.
 */
int zpath_app_just_init_shards(int use_sqlt);
int zpath_app_init_shards(int skip_zpath_ip_location, int use_sqlt);

/*
 * Initialize np shards.
 */
int zpath_app_init_np_shards(int use_sqlt, int use_zpath_table);

/*
 * If you wish to use all the shard databases for static tables,
 * call this routine after  zpath_app_init.
 */
int zpath_app_just_init_shards_static(int use_sqlt, int static_wally);
int zpath_app_init_shards_static(int skip_zpath_ip_location, int use_sqlt);

void zpath_app_set_specific_argo_mem_threshold(int threshold_percent);
void zpath_app_set_specific_max_logging_mb(int max_logging_mb);

void zpath_lib_termination_handler_init(int (*exit_handler_fn)(enum zpath_termination_code tc, void *cookie, int self_thread_num));

void zpath_init_common_config_overrides(int64_t instance_gid, int64_t customer_id, int64_t client_default);

#endif /* __ZPATH_APP_H__ */
