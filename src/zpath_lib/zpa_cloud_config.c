/*
 * zpa_cloud_config.c Copyright (C) 2014 Zscaler, Inc. All Rights Reserved
 */

#include "fohh/fohh.h"
#include <stdio.h>
#include <stdlib.h>
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_debug.h"
#include <sys/stat.h>
#include "zpath_misc/zpath_misc.h"
#include "base64/base64.h"
#include "fohh/fohh_http.h"
#include "zpath_lib/zpath_cloud.h"
#include <zpa_cloud_config_default.h>

struct zpath_all_cloud_config g_zpa_cloud_config;
struct argo_structure_description *zpa_cloud_config_description = NULL;
struct zpath_allocator zpa_cloud_config_allocator = ZPATH_ALLOCATOR_INIT("zpa_cloud_config");
int g_zpa_config_source=0;
/* Root certificate mapping for flex or any new cloud. */
#define MAX_ROOT_CERTS 100
char **g_zpa_cloud_config_root_certs = NULL;
static size_t g_zpa_cloud_config_root_cert_cnt = 0;
static time_t g_zpa_cloud_config_root_cert_time[MAX_ROOT_CERTS];
static int g_zpa_cloud_config_root_cert_drop_fips[MAX_ROOT_CERTS];
char *g_zpa_cloud_config_default = NULL;

#define ZPA_CLOUD_CONFIG_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ZPA_CLOUD_CONFIG_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ZPA_CLOUD_CONFIG_CALLOC(x) zpath_calloc(&zpa_cloud_config_allocator, x, __LINE__, __FILE__)
#define ZPA_CLOUD_CONFIG_STRDUP(x) zpath_strdup(&zpa_cloud_config_allocator, x, strlen(x) + 1, __LINE__, __FILE__)
#define ZPA_CLOUD_CONFIG_STRNDUP(x, y) zpath_strdup(&zpa_cloud_config_allocator, x, y, __LINE__, __FILE__)
#define ZPA_CLOUD_CONFIG_ERROR_INVALID -1
#define ZPA_CLOUD_CONFIG_NO_DATA 0

static char *zpa_cloud_config_root_cert="-----BEGIN CERTIFICATE-----\nMIIDkTCCAnmgAwIBAgIJANiIXC9Ht8RdMA0GCSqGSIb3DQEBCwUAMF8xCzAJBgNV\nBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRAwDgYDVQQKDAdac2NhbGVyMSkw\nJwYDVQQDDCBFVCBDbG91ZCBDb25maWcgUm9vdCBDZXJ0aWZpY2F0ZTAeFw0yNDA4\nMjkyMzAwMjRaFw00NDA4MjQyMzAwMjRaMF8xCzAJBgNVBAYTAlVTMRMwEQYDVQQI\nDApDYWxpZm9ybmlhMRAwDgYDVQQKDAdac2NhbGVyMSkwJwYDVQQDDCBFVCBDbG91\nZCBDb25maWcgUm9vdCBDZXJ0aWZpY2F0ZTCCASIwDQYJKoZIhvcNAQEBBQADggEP\nADCCAQoCggEBAOONCJnD2m7c/wNB3pb4qF321OWfd6T7AyZm2wP1NSGDCisOFxNn\nmJ+C7TpDAQZCcFYsOL1eXQ69C07XCWydX87Goj2Jl0nGru3xWzH6YSo0p7JERopc\nnyx0xMN1udqAtuqQ5iphb4NCRo2noTNsKmNgdVg+dX3CLrdBDeH2o2jTo+NBNDMe\nQEokAcHBSx6CxLjcCLfqHHoCAJieUXG2jPrphbEL8rW0MWufGlVNZ1YWtdSnOBmo\nX+HZH8RWHFqA9T37x7O2myr4JfRnsaKPDUPqBXqcvRqL+UB6G/ZRaTDmK5kF0IvG\nI+C5ZdMx/LLQc5lRfmPb6HNHyl6YXZOKNmsCAwEAAaNQME4wHQYDVR0OBBYEFD9p\nQBpEJ8NWqirGLRlIkJxoH/IOMB8GA1UdIwQYMBaAFD9pQBpEJ8NWqirGLRlIkJxo\nH/IOMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAHmFkvcWS/he47hI\nULkdm8nMWx8g+3VVfJ7yJUjutKM3SG5DgqK9OQIxC+sk86lXO5YXSQjkauTdtett\nvnflJogMWeB6/vF/ZZP7keFQl8mfy6wh2QccHDu6KvgEk/2zKFGa4tDrC88qinlM\n0Q4jja1bTxyO09texcxSXbhKuSMwrHIt1wmwtT83sdBTdAcc6z1okvlnpida/rby\nXGSYGdjPN7dxlD2t5vkAZVnACuUy0jhRObG7A/i+zlFld6e7bQIC7PASPjqzTt1S\nW/BNZ/YebydxZeYycGvEbYlPFKdeVn2wpqrIxEkcZklXNlOyhcXWTGcrMjqAEJPh\nfuz/6kk=\n-----END CERTIFICATE-----\n";
static const char *zpa_cloud_config_root_cert_create_time="2024-08-30 00:31:07";

const int ZPATH_LOCAL_MAX_CONFIG=100;
const int ZPA_MAX_CLOUDS=1000;
const int ZPA_MAX_CLOUD_CONFIG_ROOT_CERTS=100;
const int ZPA_CLOUD_CONFIG_GET_DEFAULT_CLOUD=1;
const int ZPA_CLOUD_CONFIG_NOT_GET_DEFAULT_CLOUD=0;
const int MAX_FILE_CHUNK_SIZE=65536;
// Accepting Cloud Config size up to 10MB for now
const uint32_t MAX_ZPA_CLOUD_CONFIG_PAYLOAD_LEN = 10485760;

const char ZPA_CLOUD_CONFIG_FILE_FULL_PATH[] = "/opt/zscaler/etc/zpa_cloud_config_jws.json";
const char ZPA_CLOUD_CONFIG_DIST_FILE_PATH[] = "cloud-config/zpa_cloud_config_jws.json";
const char ZPA_CLOUD_CONFIG_API_RESPONSE[] =  " enrollment api ";
char ZPA_CLOUD_CONFIG_CUST_APPS_PLAIN_FILE[100] =  "zpa_cloud_config_jws.json";
char ZPA_CLOUD_CONFIG_CUST_APPS_CRYPT_FILE[100] =  "zpa_cloud_config_jws.crypt";
const char ZPA_CLOUD_CONFIG_DEV_CLOUD_NAME[] = "dev.zpath.net";
const char ZPA_CLOUD_JSON_ROOT_CONFIG_DATA[] = "zpa_cloud_config_data";
const char ZPA_CLOUD_JSON_CONFIG_ZPA_CLOUDS[] = "zpa_clouds";
const char ZPA_CLOUD_JSON_CONFIG_CLOUD_NAME[] = "cloud_name";
const char ZPA_CLOUD_JSON_CONFIG_PBROKER_BROKER_DOMAIN[] = "pbroker_broker_domain";
const char ZPA_CLOUD_JSON_CONFIG_CLOUD_TYPE[] = "cloud_type";

const char ZPA_CLOUD_JSON_CONFIG_SARGE[] = "sarge";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_HOSTNAME[] = "dist_hostname";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_PROXYNAME[] = "dist_proxyname";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_PATH[] = "dist_path";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_PATH_DEVELOP[] = "dist_path_develop";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_FILENAME_INDICATOR[] = "filename_indicator";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_REQUIRE_FIPS[] = "require_fips";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_PROXY_ENABLED[] = "proxy_enabled";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_DEVELOP[] = "develop";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_STACK_UPLOAD_HOST[] = "stack_upload_host";
const char ZPA_CLOUD_JSON_CONFIG_SARGE_STACK_UPLOAD_PATH[] = "stack_upload_path";

const char ZPA_CLOUD_JSON_CONFIG_EXPORTER[] = "exporter";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_COOKIE_PREFIX[] = "cookie_prefix";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_AUTH_DOMAIN[] = "auth_domain";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_SAML_AUTH_DOMAIN[] = "saml_auth_domain";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_BROKER_DOMAIN[] = "broker_domain";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_BROKER_OT_DOMAIN[] = "broker_ot_domain";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_UATU_DOMAIN[] = "uatu_domain";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_GOOGLE_POSTURE_DOMAIN[] = "google_posture_domain";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_PORTAL_HOSTNAME[] = "portal_hostname";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_PRA_PORTAL_HOSTNAME[] = "pra_portal_hostname";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_PORTAL_VERSION[] = "portal_version";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_PRA_SERVICE_PORTAL_HOSTNAME[] = "pra_service_portal_hostname";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_UNIFIED_PORTAL_TLD_CUSTOMER_APP[] = "unified_portal_tld_name";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_UNIFIED_PORTAL_PRA_TLD_CUSTOMER_APP[] = "unified_portal_pra_tld_name";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_MANAGED_APP_TLD_CUSTOMER_APP[] = "managed_ba_app_tld_name";
const char ZPA_CLOUD_JSON_CONFIG_EXPORTER_PRA_CMS_HOSTNAME[] = "pra_cms_hostname";

const char ZPA_CLOUD_JSON_CONFIG_ZPN_APIS[] = "zpn_apis";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_APIS_API[] = "api";

const char ZPA_CLOUD_JSON_CONFIG_ZPN_SAMLSP[] = "zpn_samlsp";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_SAMLSP_SAMLSP[] = "samlsp";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_SAMLSP_proxy[] = "proxy";

const char ZPA_CLOUD_JSON_CONFIG_ZPN_AUTHSP[] = "zpn_authsp";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_AUTHSP_AUTHSP[] = "authsp";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_AUTHSP_PROXY[] = "proxy";

const char ZPA_CLOUD_JSON_CONFIG_SAMLSP_AUDIENCE_LIST[] = "saml_audience_list";

const char ZPA_CLOUD_JSON_CONFIG_ZPA_CLOUD_ROOT_CERTS[] = "zpa_cloud_root_certs";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_ROOT_CERT_ROOT_CERT[] = "root_cert";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_ROOT_CERT_ROOT_CERT_TIME[] = "root_cert_time";
const char ZPA_CLOUD_JSON_CONFIG_ZPN_ROOT_CERT_CERT_CAN_DROP_FIPS[] = "cert_can_drop_fips";

const char ZPA_CLOUD_JSON_CONFIG_ZPA_CLOUD_TRUSTED_CA_CERTS[] = "zpa_cloud_trusted_ca_certs";

const char ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC[] = "zpath_misc";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC_DOMAIN[] = "domain";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC_RESOLVE_DOMAIN[] = "resolve_domain";

const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI[] = "zpath_authenticated_sni";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_CLIENT_DOMAIN[] = "client_domain";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_CLIENT_DOMAIN_WILDCARD_PREFIX[] =
        "client_domain_wildcard_prefix";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_BROKER_DOMAIN[] = "broker_domain";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_BROKER_DOMAIN_WILDCARD_PREFIX[] =
        "broker_domain_wildcard_prefix";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_BROKER_DOMAIN_CLOUD_SUFFIX[] = "broker_domain_cloud_suffix";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_RESOLVE_DOMAIN[] = "resolve_domain";
const char ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_CLIENT_TYPE[] = "client_type";


/**
 * zpath_add_root_cert_to_list - Adds a root certificate to the list.
 * @cert: The root certificate to add.
 * @cert_time: The time for the certificate.
 * @can_drop_fips: Whether the certificate can drop FIPS.
 *
 * This function adds a root certificate to the list of trusted root certificates
 * used for certificate chain verification
 * zpath_load_cloud_config_root should be called before calling this.
 */
void zpath_add_root_cert_to_list(const char *cert, const time_t cert_time, const int can_drop_fips) {
    if (!cert) {
        ZPATH_LOG(AL_WARNING, "Cloud Config: cloud config root cert NOT provided");
        return;
    }
    g_zpa_cloud_config_root_certs[g_zpa_cloud_config_root_cert_cnt] = ZPA_CLOUD_CONFIG_STRDUP(cert);
    g_zpa_cloud_config_root_cert_time[g_zpa_cloud_config_root_cert_cnt] = cert_time;
    g_zpa_cloud_config_root_cert_drop_fips[g_zpa_cloud_config_root_cert_cnt] = can_drop_fips;
    g_zpa_cloud_config_root_cert_cnt++;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Added Cloud Config Root Cert to Trusted CA list");
}

/**
 * zpath_load_cloud_config_root:
 * Loads the cloud configuration root certificates.
 */
static void zpath_load_cloud_config_root() {
    struct tm tm_info;
    time_t timestamp;
    g_zpa_cloud_config_root_certs = ZPA_CLOUD_CONFIG_CALLOC(MAX_ROOT_CERTS * sizeof(char *));
    sscanf(zpa_cloud_config_root_cert_create_time, "%4d-%2d-%2d %2d:%2d:%2d",
           &tm_info.tm_year, &tm_info.tm_mon, &tm_info.tm_mday,
           &tm_info.tm_hour, &tm_info.tm_min, &tm_info.tm_sec);

    tm_info.tm_isdst = -1;
    timestamp = mktime(&tm_info);

    zpath_add_root_cert_to_list(zpa_cloud_config_root_cert, timestamp, 1);
}

/**
 * zpath_get_all_cloud_config:
 * Used in unit test functions to verify the cloud config.
 */
struct zpath_all_cloud_config *zpath_get_all_cloud_config() {
    return &g_zpa_cloud_config;
}

/**
 * zpath_get_cloud_config_from_name:
 * Returns the zpath_cloud_config based on cloud_name
 *
 * @cloud_name: Input cloud_name to search for config
 *
 * Returns:
 * If found, returns zpath_cloud_config
 * If not found, returns NULL
 */
struct zpath_cloud_config *zpath_get_cloud_config_from_name(const char *cloud_name) {
    const char *tmp_cloud_name = cloud_name;
    if(!cloud_name){
        ZPATH_DEBUG_CLOUD_CONFIG("Failed: Searching for Cloud Config based on Cloud Name - NULL");
        return NULL;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Searching for Cloud Config based on Cloud Name %s",cloud_name);
    if (zpath_is_flex_cloud((char *)tmp_cloud_name)) {
        tmp_cloud_name = ZPA_CLOUD_CONFIG_DEV_CLOUD_NAME;
        ZPATH_LOG(AL_WARNING,"This is Flex Cloud: Searching for Cloud Config based on Dev Cloud ");
    }
    for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
        struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
        if ( tmp_config &&
                (!strcmp(tmp_cloud_name, tmp_config->cloud_name)) ) {
            ZPATH_DEBUG_CLOUD_CONFIG("Success: Searching for Cloud Config based on Cloud Name %s",cloud_name);
            return tmp_config;
        }
    }
    ZPATH_LOG(AL_WARNING,"Failed: Searching for Cloud Config based on Cloud Name - %s - Not found",cloud_name);
    return NULL;
}

/**
 * zpath_get_cloud_config_from_apiname:
 * Returns the zpath_cloud_config based on enrollment server api_name
 *
 * @cloud_name: Input api_name to search for config
 *
 * Returns:
 * If found, returns zpath_cloud_config
 * If not found, returns NULL
 */
struct zpath_cloud_config *zpath_get_cloud_config_from_apiname(const char *api_name) {
    if(!api_name) {
        ZPATH_DEBUG_CLOUD_CONFIG("Failed: Searching for Cloud Config based on API Name - NULL");
        return NULL;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Searching for Cloud Config based on API Name %s",api_name);
    for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
        struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
        if (!tmp_config) {
            continue;
        }
        for (int ac = 0; ac < tmp_config->zpn_apis_count; ac++) {
            if (tmp_config->zpn_apis[ac].api && (!strcmp(api_name, tmp_config->zpn_apis[ac].api))) {
                ZPATH_DEBUG_CLOUD_CONFIG("Success: Searching for Cloud Config based on API Name %s",api_name);
                return tmp_config;
            }
        }
        if ( (tmp_config->cloud_type == zpath_environment_type_dev) &&
                (strlen(api_name) > strlen(tmp_config->cloud_name)) &&
                (strcmp(&api_name[strlen(api_name) - strlen(tmp_config->cloud_name)],
                tmp_config->cloud_name) == 0)) {
            return tmp_config;
        }
    }
    ZPATH_LOG(AL_WARNING,"Failed: Searching for Cloud Config based on API Name %s - Not Found",api_name);
    return NULL;
}

/**
 * zpath_get_cloud_config_by_sni:
 * Returns the zpath_cloud_config based on sni
 *
 * @cloud_name: Input sni to search for config
 *
 * Returns:
 * If found, returns zpath_cloud_config
 * If not found, returns NULL
 */
struct zpath_cloud_config *zpath_get_cloud_config_by_sni(const char *sni) {
    if(!sni)
    {
        ZPATH_DEBUG_CLOUD_CONFIG("Failed: Searching for Cloud Config based on SNI - NULL");
        return NULL;
    }
    for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
        struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
        if (tmp_config &&
            (strstr(sni, tmp_config->cloud_name)))  {
            ZPATH_DEBUG_CLOUD_CONFIG("Success: Searching for Cloud Config based on SNI %s",sni);
            return tmp_config;
        }
    }
    ZPATH_LOG(AL_WARNING,"Failed: Searching for Cloud Config based on SNI %s - Not Found",sni);
    return NULL;
}

/**
 * zpath_get_cloud_config_by_filename_argv:
 * Returns the zpath_cloud_config based on command line param
 *
 * @cloud_name: Input command param to search for config
 *
 * Returns:
 * If found, returns zpath_cloud_config
 * If not found, returns NULL
 */
struct zpath_cloud_config *zpath_get_cloud_config_by_filename_argv(const char *cmd) {
    if(!cmd) {
        ZPATH_DEBUG_CLOUD_CONFIG("Failed: Searching for Cloud Config based on param - NULL");
        return NULL;
    }
    for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
        struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
        if ( tmp_config &&
             (tmp_config->sarge) &&
            (tmp_config->sarge->filename_indicator) &&
            (strcmp(tmp_config->sarge->filename_indicator, cmd) == 0)) {
            ZPATH_DEBUG_CLOUD_CONFIG("Success: Searching for Cloud Config based on param %s",cmd);
            return tmp_config;
        }
    }
    ZPATH_LOG(AL_WARNING,"Failed: Searching for Cloud Config based on param %s - Not Found",cmd);
    return NULL;
}

/**
 * zpath_get_cloud_config_by_filesystem:
 * Returns the zpath_cloud_config based on foldername matching cloud
 *
 * Returns:
 * If found, returns zpath_cloud_config
 * If not found, returns NULL
 */
struct zpath_cloud_config *zpath_get_cloud_config_by_filesystem() {
    struct stat st;
    int cloud_found = 0;
    struct zpath_cloud_config *cloud_config = NULL;
    for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
        struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
        if ((tmp_config->sarge->filename_indicator) && (stat(tmp_config->sarge->filename_indicator, &st) == 0)) {
            if (!cloud_found) {
                cloud_config = tmp_config;
                cloud_found = 1;
            } else {
                ZPATH_LOG(AL_WARNING,
                          "Multiple key folders for custom cloud names present - please remove redundant folders %s "
                          "and %s",
                          tmp_config->cloud_name, tmp_config->cloud_name);
                return NULL;
            }
        }
    }
    if(!cloud_config){
        ZPATH_LOG(AL_WARNING,"Failed: Searching for Cloud Config based on folder in disk - Not Found");
        return NULL;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Searching for Cloud Config based on folder in disk");
    return cloud_config;
}


/**
 * zpath_get_exporter_config:
 * Returns the exporter object based on cloud_name
 *
 * @cloud_name: Input cloud name to search for config
 *
 * Returns:
 * If found, returns zpath_exporter_cloud_config
 * If not found, returns NULL
 */
struct zpath_exporter_cloud_config *zpath_get_exporter_config(const char *cloud_name) {
    if(cloud_name) {
        ZPATH_DEBUG_CLOUD_CONFIG("Searching for Exporter Config based on Cloud Name %s",cloud_name);
        for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
            struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
            if ( tmp_config &&
                 !strcmp(cloud_name, tmp_config->cloud_name) &&
                 (tmp_config->exporter)) {
                ZPATH_DEBUG_CLOUD_CONFIG("Searching for Exporter Config based on Cloud Name %s - Success",cloud_name);
                return tmp_config->exporter;
            }
        }
        ZPATH_LOG(AL_WARNING,"Searching for Exporter Config based on Cloud Name %s - Not found",cloud_name);
    }
    return NULL;
}

/**
 * zpath_is_cloud_dev_env:
 * Checks if the given cloud is dev or not
 *
 * @cloud_name: Input cloud name to check for dev
 *
 * Returns:
 * 1 - if Dev cloud
 * 0 - if NOT Dev cloud
 * -1 - if cloud config not found
 */
int zpath_is_cloud_dev_env(const char *cloud_name) {
    if(!cloud_name) {
        ZPATH_DEBUG_CLOUD_CONFIG("Failed: Checking cloud for Dev or Not - NULL");
        return -1;
    }
    const struct zpath_cloud_config *cloud_config = zpath_get_cloud_config_from_name(cloud_name);
    if (cloud_config) {
        if ((cloud_config->cloud_type == zpath_environment_type_dev) || (ZPATH_LOCAL_IS_DEV_ENV)) {
            ZPATH_DEBUG_CLOUD_CONFIG("Success: Given cloud %s is a Dev cloud", cloud_name);
            return 1;
        }
        ZPATH_DEBUG_CLOUD_CONFIG("Success: Given cloud %s is NOT Dev cloud", cloud_name);
        return 0;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Failed: Unable to check Dev configuration for cloud %s", cloud_name);
    return -1;
}

/**
 * zpath_get_saml_audience_list:
 * Returns the configured saml audience list for the cloud
 *
 * @cloud_name: Input cloud name
 *
 * Returns:
 * If found:
 * saml_audience_list - List of saml audience as per configuration
 * Count - Count of saml audience
 * If Not found
 * -1
 */
size_t zpath_get_saml_audience_list(const char *cloud_name, char ***saml_audience_list) {
    if(!cloud_name) {
        ZPATH_DEBUG_CLOUD_CONFIG("Failed: Checking SAML audience for Cloud - NULL");
        return ZPA_CLOUD_CONFIG_ERROR_INVALID;
    }
    for (int c = 0; c < g_zpa_cloud_config.cloud_config_count; c++) {
        struct zpath_cloud_config *tmp_config = g_zpa_cloud_config.cloud_config[c];
        if ( tmp_config &&
            (!strcmp(cloud_name, tmp_config->cloud_name)) &&
             (tmp_config->saml_audience_list) ) {
            *saml_audience_list = tmp_config->saml_audience_list;
                ZPATH_DEBUG_CLOUD_CONFIG("Success: Checking SAML audience for Cloud - %s", cloud_name);
                return tmp_config->saml_audience_list_count;
        }
    }
    ZPATH_LOG(AL_WARNING,"Failure: Checking SAML audience for Cloud - %s", cloud_name);
    return ZPA_CLOUD_CONFIG_ERROR_INVALID;
}

/**
 * zpath_get_zpath_authenticated_sni_for_cloud
 * Retrieves the zpath authenticated SNIs from cloud
 * configuration.
 *
 * @zpath_auth_snis:
 * Cloud Name - (Optional)
 * Pointer to the zpath_authenticated_sni_map list.
 *
 * Returns: The number of zpath authenticated sni in the configuration.
 */
size_t zpath_get_zpath_authenticated_sni_for_cloud(const char *cloud_name, struct zpath_authenticated_sni_map **zpath_auth_snis) {
    const char *tmp_cloud_name = ZPATH_LOCAL_CLOUD_NAME;
    if ( cloud_name ) {
        tmp_cloud_name = cloud_name;
    }
    struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_name(tmp_cloud_name);

    if(!cloud_config) {
        ZPATH_DEBUG_CLOUD_CONFIG("NO Authenticated SNIs available for Cloud %s", tmp_cloud_name);
        return 0;
    }

    if(cloud_config->zpath_authenticated_sni &&
        cloud_config->zpath_authenticated_sni_count > 0){
        *zpath_auth_snis = cloud_config->zpath_authenticated_sni;
            ZPATH_DEBUG_CLOUD_CONFIG("Success: Fetching Authenticated SNIs");
            return cloud_config->zpath_authenticated_sni_count;
    }
    ZPATH_LOG(AL_WARNING,"FAILED: Fetching Authenticated SNIs");
    return 0;
}

static void zpath_free_cloud_objects(void *obj) {
    if (obj) {
        ZPA_CLOUD_CONFIG_FREE(obj);
    }
}

static int zpath_json_get_int(const JSON_Object *object, const char *key) {
    int tmp = 0;
    if (object && key) {
        tmp = (int)json_object_get_number(object, key);
    } else {
        if (key)
            ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Unable to parse JSON: Integer Key %s not found in JSON Object", key);
    }
    return tmp;
}

static char *zpath_json_strdup(const JSON_Object *object, const char *name) {
    const char *tmp = NULL;
    if (object && name) {
        tmp = json_object_get_string(object, name);
    }
    if (!tmp) {
        if (name)
            ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Unable to parse JSON: String Key %s not found in JSON Object", name);
        return NULL;
    }
    return ZPA_CLOUD_CONFIG_STRDUP(tmp);
}

static char *zpath_json_array_strdup(const JSON_Array *array, int index) {
    const char *tmp = NULL;
    if (array && (index >= 0)) {
        tmp = json_array_get_string(array, index);
    }
    if (!tmp) {
        ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Unable to parse JSON: Array Index %d not found in JSON Array", index);
        return NULL;
    }
    return ZPA_CLOUD_CONFIG_STRDUP(tmp);
}

static JSON_Array *zpath_json_get_array_object(const JSON_Object *object, const char *key, size_t *count) {
    JSON_Array *tmp_array = NULL;

    if (object && key) {
        tmp_array = json_object_get_array(object, key);
        *count = json_array_get_count(tmp_array);
    }

    if ((!tmp_array) || (*count == 0)) {
        if (key)
            ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Unable to parse JSON: JSON Array %s not found in JSON Object \n", key);
        return NULL;
    }

    return tmp_array;
}

static JSON_Object *zpath_json_get_object_from_array(const JSON_Array *array, int ind) {
    JSON_Object *tmp_obj = NULL;
    if (array && (ind >= 0)) {
        tmp_obj = json_array_get_object(array, ind);
    }
    if ( (!tmp_obj) || (json_object_get_count(tmp_obj) == 0) ) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Unable to parse JSON: JSON Object %d not found in JSON Array\n", ind);
        return NULL;
    }
    return tmp_obj;
}

static JSON_Object *zpath_json_get_object_from_object(const JSON_Object *object, const char *key) {
    JSON_Object *tmp_obj = NULL;
    if(object && key) {
        tmp_obj=json_object_get_object(object, key);
    }
    if (!tmp_obj) {
        if(key)
            ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Unable to parse JSON: JSON Object %s not found in JSON Object\n", key);
        return NULL;
    }
    return tmp_obj;
}

static void zpath_free_sarge_objects(struct zpath_sarge_cloud_config *tmpSarge) {
    if (tmpSarge) {
        zpath_free_cloud_objects(tmpSarge->stack_upload_path);
        zpath_free_cloud_objects(tmpSarge->stack_upload_host);
        zpath_free_cloud_objects(tmpSarge->filename_indicator);
        zpath_free_cloud_objects(tmpSarge->dist_path_develop);
        zpath_free_cloud_objects(tmpSarge->dist_path);
        zpath_free_cloud_objects(tmpSarge->dist_proxyname);
        zpath_free_cloud_objects(tmpSarge->dist_hostname);
    }
}

static struct zpath_sarge_cloud_config *zpath_get_get_sarge_object(const JSON_Object *cloud_json_obj) {
    struct zpath_sarge_cloud_config *tmpSarge;
    const JSON_Object *sar_obj = zpath_json_get_object_from_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_SARGE);
    if (!sar_obj) {
        ZPATH_LOG(AL_WARNING, "Cloud Config: Failed to Parse Sarge config from JSON");
        return NULL;
    }
    tmpSarge = ZPA_CLOUD_CONFIG_CALLOC(sizeof(struct zpath_sarge_cloud_config));
    tmpSarge->dist_hostname = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_HOSTNAME);
    tmpSarge->dist_proxyname = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_PROXYNAME);
    tmpSarge->dist_path = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_PATH);
    tmpSarge->dist_path_develop = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_DIST_PATH_DEVELOP);
    tmpSarge->filename_indicator = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_FILENAME_INDICATOR);
    tmpSarge->stack_upload_host = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_STACK_UPLOAD_HOST);
    tmpSarge->stack_upload_path = zpath_json_strdup(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_STACK_UPLOAD_PATH);
    tmpSarge->require_fips = zpath_json_get_int(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_REQUIRE_FIPS);
    tmpSarge->proxy_enabled = zpath_json_get_int(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_PROXY_ENABLED);
    tmpSarge->develop = zpath_json_get_int(sar_obj, ZPA_CLOUD_JSON_CONFIG_SARGE_DEVELOP);
    if ((!tmpSarge->dist_hostname) || (!tmpSarge->dist_proxyname) || (!tmpSarge->dist_path) ||
        (!tmpSarge->dist_path_develop) || (!tmpSarge->stack_upload_host) || (!tmpSarge->stack_upload_path)) {
        ZPATH_LOG(AL_WARNING, "Cloud Config: Unable to parse Sarge Config: Key Parameters are missing \n");
        zpath_free_sarge_objects(tmpSarge);
        zpath_free_cloud_objects(tmpSarge);
        return NULL;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Sarge Config");
    return tmpSarge;
}

static void zpath_free_exporter_objects(struct zpath_exporter_cloud_config *tmpExp) {
    if (tmpExp) {
        zpath_free_cloud_objects(tmpExp->cookie_prefix);
        zpath_free_cloud_objects(tmpExp->auth_domain);
        zpath_free_cloud_objects(tmpExp->saml_auth_domain);
        zpath_free_cloud_objects(tmpExp->broker_domain);
        zpath_free_cloud_objects(tmpExp->broker_ot_domain);
        zpath_free_cloud_objects(tmpExp->uatu_domain);
        zpath_free_cloud_objects(tmpExp->google_posture_domain);
        zpath_free_cloud_objects(tmpExp->portal_hostname);
        zpath_free_cloud_objects(tmpExp->pra_portal_hostname);
        zpath_free_cloud_objects(tmpExp->portal_version);
        zpath_free_cloud_objects(tmpExp->pra_service_portal_hostname);
        zpath_free_cloud_objects(tmpExp->unified_portal_tld_appln_name);
        zpath_free_cloud_objects(tmpExp->unified_portal_pra_tld_appln_name);
        zpath_free_cloud_objects(tmpExp->managed_app_tld_appln_name);
        zpath_free_cloud_objects(tmpExp->pra_cms_hostname);
    }
}

static struct zpath_exporter_cloud_config *zpath_get_cc_exporter_object(const JSON_Object *cloud_json_obj) {
    struct zpath_exporter_cloud_config *tmpExp;
    const JSON_Object *exp_obj = zpath_json_get_object_from_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER);
    if (!exp_obj) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Exporter config from JSON");
        return NULL;
    }
    tmpExp = ZPA_CLOUD_CONFIG_CALLOC(sizeof(struct zpath_exporter_cloud_config));
    tmpExp->cookie_prefix = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_COOKIE_PREFIX);
    tmpExp->auth_domain = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_AUTH_DOMAIN);
    tmpExp->saml_auth_domain = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_SAML_AUTH_DOMAIN);
    tmpExp->broker_domain = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_BROKER_DOMAIN);
    tmpExp->broker_ot_domain = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_BROKER_OT_DOMAIN);
    tmpExp->uatu_domain = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_UATU_DOMAIN);
    tmpExp->google_posture_domain = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_GOOGLE_POSTURE_DOMAIN);
    tmpExp->portal_hostname = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_PORTAL_HOSTNAME);
    tmpExp->pra_portal_hostname = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_PRA_PORTAL_HOSTNAME);
    tmpExp->portal_version = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_PORTAL_VERSION);
    tmpExp->pra_service_portal_hostname = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_PRA_SERVICE_PORTAL_HOSTNAME );
    tmpExp->unified_portal_tld_appln_name= zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_UNIFIED_PORTAL_TLD_CUSTOMER_APP);
    tmpExp->unified_portal_pra_tld_appln_name= zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_UNIFIED_PORTAL_PRA_TLD_CUSTOMER_APP);
    tmpExp->managed_app_tld_appln_name = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_MANAGED_APP_TLD_CUSTOMER_APP);
    tmpExp->pra_cms_hostname = zpath_json_strdup(exp_obj, ZPA_CLOUD_JSON_CONFIG_EXPORTER_PRA_CMS_HOSTNAME);

    if ((!tmpExp->cookie_prefix) || (!tmpExp->auth_domain) || (!tmpExp->saml_auth_domain) || (!tmpExp->broker_domain) ||
        (!tmpExp->broker_ot_domain) || (!tmpExp->uatu_domain) || (!tmpExp->google_posture_domain) ||
        (!tmpExp->portal_hostname) || (!tmpExp->pra_portal_hostname) || (!tmpExp->pra_service_portal_hostname) ) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Unable to parse Exporter Config: Key Parameters are missing \n");
        zpath_free_exporter_objects(tmpExp);
        zpath_free_cloud_objects(tmpExp);
        return NULL;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Exporter Config");
    return tmpExp;
}

static void zpa_free_sniapis_list(struct zpn_apimap *tmpApis, const size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            zpath_free_cloud_objects(tmpApis[ind].api);
        }
    }
}

static struct zpn_apimap *zpath_get_cc_zpnapi_list(const JSON_Object *cloud_json_obj, size_t *count) {
    struct zpn_apimap *tmpApis;
    size_t tmp_count=0;
    const JSON_Array *zpn_apis_array =
            zpath_json_get_array_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_APIS, &tmp_count);
    if ( (!zpn_apis_array) ||
         (tmp_count == 0) ) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse ZPN APIs from JSON");
        return NULL;
    }
    tmpApis = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zpn_apimap));
    size_t valid_cnt = 0;
    for(int ind=0;ind < tmp_count; ind++) {
        const JSON_Object *zpi_api_obj = zpath_json_get_object_from_array(zpn_apis_array, ind);
        // Should we validate APIs and ignore wrong ones ? To be handled while encoding the JSON to JWS
        if(zpi_api_obj) {
            tmpApis[valid_cnt].api = zpath_json_strdup(zpi_api_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_APIS_API);
            valid_cnt++;
        }
    }
    if(valid_cnt == 0) {
        zpath_free_cloud_objects(tmpApis);
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse ZPN APIs from JSON, No valid entries");
        *count=0;
        return NULL;
    }
    *count=valid_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing ZPN APIs");
    return tmpApis;
}

static void zpa_free_samlsps_list(struct zpn_samlsp_map *tmpApis, const size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            zpath_free_cloud_objects(tmpApis[ind].samlsp);
            zpath_free_cloud_objects(tmpApis[ind].proxy);
        }
    }
}

static struct zpn_samlsp_map *zpath_get_cc_samlsps_list(const JSON_Object *cloud_json_obj, size_t *count) {
    struct zpn_samlsp_map *tmpApis;
    size_t tmp_count=0;
    const JSON_Array *zpn_samlsp_array =
            zpath_json_get_array_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_SAMLSP, &tmp_count);
    if  ( (tmp_count == 0) ||
         (!zpn_samlsp_array)) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse SAML SPs from JSON");
        return NULL;
    }
    tmpApis = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zpn_samlsp_map));
    size_t valid_cnt = 0;
    for(int ind=0;ind < tmp_count; ind++) {
        const JSON_Object *zpn_samlsp_obj = zpath_json_get_object_from_array(zpn_samlsp_array, ind);
        if(zpn_samlsp_obj){
            tmpApis[valid_cnt].samlsp = zpath_json_strdup(zpn_samlsp_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_SAMLSP_SAMLSP);
            tmpApis[valid_cnt].proxy = zpath_json_strdup(zpn_samlsp_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_SAMLSP_proxy);
            valid_cnt++;
        }
    }
    if(valid_cnt == 0) {
        zpath_free_cloud_objects(tmpApis);
        *count=0;
        ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Failed to Parse SAML SPs List from JSON, No valid entries");
        return NULL;
    }
    *count=valid_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing SAML SPs List");
    return tmpApis;
}

static void zpa_free_authsp_list(struct zpn_authsp_map *tmpApis, const size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            zpath_free_cloud_objects(tmpApis[ind].authsp);
            zpath_free_cloud_objects(tmpApis[ind].proxy);
        }
    }
}

// zpn_authsp_map zpn_authsps
static struct zpn_authsp_map *zpath_get_cc_authsp_list(const JSON_Object *cloud_json_obj, size_t *count) {
    struct zpn_authsp_map *tmpApis;
    size_t tmp_count=0;
    const JSON_Array *zpn_authsp_array =
            zpath_json_get_array_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_AUTHSP, &tmp_count);
    if ( (!zpn_authsp_array) ||
         (tmp_count == 0)) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse AUTH SPs from JSON");
        return NULL;
    }
    tmpApis = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zpn_authsp_map));
    size_t valid_cnt = 0;
    for(int ind=0;ind < tmp_count; ind++) {
        const JSON_Object *zpn_authsp_obj = zpath_json_get_object_from_array(zpn_authsp_array, ind);
        if(zpn_authsp_obj) {
            tmpApis[valid_cnt].authsp = zpath_json_strdup(zpn_authsp_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_AUTHSP_AUTHSP);
            tmpApis[valid_cnt].proxy = zpath_json_strdup(zpn_authsp_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_AUTHSP_PROXY);
            valid_cnt++;
        }
    }
    if(valid_cnt == 0) {
        zpath_free_cloud_objects(tmpApis);
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Auth SPs List from JSON, No valid entries");
        *count=0;
        return NULL;
    }
    *count=valid_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Auth SPs List");
    return tmpApis;
}

static void zpa_free_saml_audience_list(char **tmpApis, const size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            zpath_free_cloud_objects(tmpApis[ind]);
        }
    }
}

static char **zpath_get_cc_saml_audience_list(const JSON_Object *cloud_json_obj, size_t *count) {
    char **tmpApis;
    size_t tmp_count=0;
    const JSON_Array *saml_audience_list_array =
            zpath_json_get_array_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_SAMLSP_AUDIENCE_LIST, &tmp_count);
    if ((tmp_count == 0) ||
        (!saml_audience_list_array)) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse SAML Audience List from JSON");
        return NULL;
    }
    tmpApis = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(char *));
    for (int ind = 0; ind < tmp_count; ind++) {
        tmpApis[ind] = zpath_json_array_strdup(saml_audience_list_array, ind);
    }
    *count=tmp_count;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing SAML Audience List");
    return tmpApis;
}

static void zpa_free_cloud_certs_list(struct zcrypt_root_cert_map *tmpCerts, size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            free(tmpCerts[ind].root_cert);
        }
    }
    *count=0;
}

static struct zcrypt_root_cert_map *zpath_get_cc_certs_list(const char *cert_type, const JSON_Object *cloud_json_obj, size_t *count) {
    struct zcrypt_root_cert_map *tmpCACerts;
    size_t tmp_count=0;
    const JSON_Array *zpa_cloud_trusted_ca_certs_array =
            zpath_json_get_array_object(cloud_json_obj, cert_type, &tmp_count);
    if ((tmp_count==0) ||
        (!zpa_cloud_trusted_ca_certs_array)) {
        ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config: Failed to Parse Trusted CA Certs from JSON");
        return NULL;
    }
    int valid_cert_cnt = 0;
    tmpCACerts = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zcrypt_root_cert_map));
    for (int ind = 0; ind < tmp_count; ind++) {
        const JSON_Object *zpa_cloud_root_cert_obj = zpath_json_get_object_from_array(zpa_cloud_trusted_ca_certs_array, ind);
        char *b64encoded_str =
                zpath_json_strdup(zpa_cloud_root_cert_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_ROOT_CERT_ROOT_CERT);
        if (b64encoded_str) {
            char *tmpcert = base64_decode(b64encoded_str);
            zpath_free_cloud_objects(b64encoded_str);
            if (tmpcert) {
                tmpCACerts[valid_cert_cnt].root_cert = tmpcert;
                tmpCACerts[valid_cert_cnt].root_cert_time = (time_t)zpath_json_get_int(
                        zpa_cloud_root_cert_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_ROOT_CERT_ROOT_CERT_TIME);
                tmpCACerts[valid_cert_cnt].cert_can_drop_fips = zpath_json_get_int(
                        zpa_cloud_root_cert_obj, ZPA_CLOUD_JSON_CONFIG_ZPN_ROOT_CERT_CERT_CAN_DROP_FIPS);
                valid_cert_cnt++;
            }
        }
    }
    if (0 == valid_cert_cnt) {
        zpa_free_cloud_certs_list(tmpCACerts, &tmp_count);
        zpath_free_cloud_objects(tmpCACerts);
        *count=0;
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Root Certs List from JSON, No valid entries");
        return NULL;
    }
    *count=valid_cert_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Root Certs List");
    return tmpCACerts;
}

static void zpath_free_cert_chains(char **certsList, int count) {
    if(certsList){
        for (int ind = 0; ind < count; ind++) {
            zpath_free_cloud_objects(certsList[ind]);
        }
    }
}

static char **zpath_get_cert_chains_from_header(const JSON_Object *header_json_obj, size_t *count) {
    char **certsList;
    size_t tmp_count=0;
    const JSON_Array *cert_chain_array = zpath_json_get_array_object(header_json_obj, "x5c", &tmp_count);
    if (!cert_chain_array) {
        ZPATH_LOG(AL_WARNING, "Cloud Config: Failed to read Cert Chains from header JSON");
        return NULL;
    }
    certsList = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(char *));
    for (int ind = 0; ind < tmp_count; ind++) {
        certsList[ind] = zpath_json_array_strdup(cert_chain_array, ind);
    }
    *count=tmp_count;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Cert Chains from header JSON");
    return certsList;
}

static void zpa_free_domain_misc_list(struct zpath_misc_domain_map *tmpApis, const size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            zpath_free_cloud_objects(tmpApis[ind].domain);
            zpath_free_cloud_objects(tmpApis[ind].resolve_domain);
        }
    }
}

static struct zpath_misc_domain_map *zpath_get_domain_misc_list(const JSON_Object *cloud_json_obj, size_t *count) {
    struct zpath_misc_domain_map *tmpApis;
    size_t tmp_count=0;
    const JSON_Array *zpath_misc_array = zpath_json_get_array_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC, &tmp_count);
    if ((tmp_count == 0) ||
        (!zpath_misc_array)) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Domain Misc List from JSON");
        return NULL;
    }
    tmpApis = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zpath_misc_domain_map));
    size_t valid_cnt=0;
    for (int ind = 0; ind < tmp_count; ind++) {
        const JSON_Object *zpath_misc_obj = zpath_json_get_object_from_array(zpath_misc_array, ind);
        if(zpath_misc_obj) {
            tmpApis[valid_cnt].domain =
                    zpath_json_strdup(zpath_misc_obj, ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC_DOMAIN);
            tmpApis[valid_cnt].resolve_domain = NULL;
            const char *tmp = json_object_get_string(zpath_misc_obj, ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC_RESOLVE_DOMAIN);
            if ( (tmp) && (tmp[0] != '\0' )) {
                tmpApis[valid_cnt].resolve_domain =
                    zpath_json_strdup(zpath_misc_obj, ZPA_CLOUD_JSON_CONFIG_ZPATH_MISC_RESOLVE_DOMAIN);
            }
            valid_cnt++;
        }
    }
    if(valid_cnt==0) {
        zpath_free_cloud_objects(tmpApis);
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Domain Misc List from JSON, No valid entries");
        *count=0;
        return NULL;
    }
    *count=valid_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Authenticated SNIs List");
    return tmpApis;
}



static void zpa_free_authenticated_snis_list(struct zpath_authenticated_sni_map *tmpApis, const size_t *count) {
    if (*count > 0) {
        for (int ind = 0; ind < (*count); ind++) {
            zpath_free_cloud_objects(tmpApis[ind].broker_domain);
            zpath_free_cloud_objects(tmpApis[ind].client_domain);
            zpath_free_cloud_objects(tmpApis[ind].resolve_domain);
        }
    }
}

static struct zpath_authenticated_sni_map *zpath_get_authenticated_snis_list(const JSON_Object *cloud_json_obj, size_t *count) {
    struct zpath_authenticated_sni_map *tmpApis;
    size_t tmp_count=0;
    const JSON_Array *zpath_authenticated_sni_array = zpath_json_get_array_object(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI, &tmp_count);
    if ((tmp_count == 0) ||
        (!zpath_authenticated_sni_array)) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Authenticated SNIs List from JSON");
        return NULL;
    }
    tmpApis = ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zpath_authenticated_sni_map ));
    size_t valid_cnt=0;
    for (int c = 0; c < tmp_count; c++) {
        const JSON_Object *zpath_authenticated_sni_obj =
                zpath_json_get_object_from_array(zpath_authenticated_sni_array, c);
        if(zpath_authenticated_sni_obj){
            tmpApis[valid_cnt].client_domain = zpath_json_strdup( zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_CLIENT_DOMAIN);
            tmpApis[valid_cnt].client_domain_wildcard_prefix =zpath_json_get_int(zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_CLIENT_DOMAIN_WILDCARD_PREFIX);
            tmpApis[valid_cnt].broker_domain = zpath_json_strdup( zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_BROKER_DOMAIN);
            tmpApis[valid_cnt].broker_domain_wildcard_prefix =
                                        zpath_json_get_int(zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_BROKER_DOMAIN_WILDCARD_PREFIX);
            tmpApis[valid_cnt].broker_domain_cloud_suffix = zpath_json_get_int(zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_BROKER_DOMAIN_CLOUD_SUFFIX);
            tmpApis[valid_cnt].resolve_domain = zpath_json_strdup(zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_RESOLVE_DOMAIN);
            tmpApis[valid_cnt].client_type = zpath_json_get_int( zpath_authenticated_sni_obj,
                                        ZPA_CLOUD_JSON_CONFIG_ZPATH_AUTHENTICATED_SNI_CLIENT_TYPE);
            valid_cnt++;
        }
    }
    if(valid_cnt==0) {
        zpath_free_cloud_objects(tmpApis);
        ZPATH_DEBUG_CLOUD_CONFIG( "Cloud Config: Failed to Parse Authenticated SNIs List from JSON, No valid entries");
        *count=0;
        return NULL;
    }
    *count=valid_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Authenticated SNIs List");
    return tmpApis;
}

static int zpath_create_cloud_object_json_data(const JSON_Object *cloud_json_obj,
                                               struct zpath_cloud_config *cloud_config_obj) {
    char *tmp_cloud_name = zpath_json_strdup(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_CLOUD_NAME);
    char *tmp_pbroker_domain = zpath_json_strdup(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_PBROKER_BROKER_DOMAIN);
    struct zpath_sarge_cloud_config *tmpSarge = zpath_get_get_sarge_object(cloud_json_obj);
    struct zpath_exporter_cloud_config *tmpExp = zpath_get_cc_exporter_object(cloud_json_obj);
    size_t zpn_apis_cnt = 0;
    struct zpn_apimap *zpn_apis_array = zpath_get_cc_zpnapi_list(cloud_json_obj, &zpn_apis_cnt);
    size_t zpn_samlsp_cnt = 0;
    struct zpn_samlsp_map *zpn_samlsp_array = zpath_get_cc_samlsps_list(cloud_json_obj, &zpn_samlsp_cnt);
    size_t zpn_authsp_cnt = 0;
    struct zpn_authsp_map *zpn_authsp_array = zpath_get_cc_authsp_list(cloud_json_obj, &zpn_authsp_cnt);
    size_t zpn_saml_audience_cnt = 0;
    char **saml_audience_list_array = zpath_get_cc_saml_audience_list(cloud_json_obj, &zpn_saml_audience_cnt);
    size_t zpn_root_cert_cnt = 0;
    struct zcrypt_root_cert_map *tmpRoots = zpath_get_cc_certs_list(ZPA_CLOUD_JSON_CONFIG_ZPA_CLOUD_ROOT_CERTS, cloud_json_obj, &zpn_root_cert_cnt);
    size_t zpn_trusted_ca_cert_cnt = 0;
    struct zcrypt_root_cert_map *tmpCaCerts = zpath_get_cc_certs_list(ZPA_CLOUD_JSON_CONFIG_ZPA_CLOUD_TRUSTED_CA_CERTS, cloud_json_obj, &zpn_trusted_ca_cert_cnt);
    size_t misc_count = 0;
    struct zpath_misc_domain_map *tmp_misc = zpath_get_domain_misc_list(cloud_json_obj, &misc_count);
    size_t auth_sni_count = 0;
    struct zpath_authenticated_sni_map *tmp_authsnis = zpath_get_authenticated_snis_list(cloud_json_obj, &auth_sni_count );
    if ((!tmp_cloud_name) || (!tmp_pbroker_domain) || (!tmpSarge) || (!tmpExp) || (!zpn_apis_array) ||
        (zpn_apis_cnt == 0) || ((!zpn_samlsp_array) && (!zpn_authsp_array)) || (!saml_audience_list_array) ||
        (zpn_saml_audience_cnt == 0) || (!tmpRoots) || (zpn_root_cert_cnt == 0)) {
        ZPATH_LOG(AL_WARNING, "Error: Unable to parse JSON file - Missing parameters\n");
        zpath_free_sarge_objects(tmpSarge);
        zpath_free_cloud_objects(tmpSarge);
        zpath_free_exporter_objects(tmpExp);
        zpath_free_cloud_objects(tmpExp);
        zpa_free_sniapis_list(zpn_apis_array, &zpn_apis_cnt);
        zpath_free_cloud_objects(zpn_apis_array);
        zpa_free_samlsps_list(zpn_samlsp_array, &zpn_samlsp_cnt);
        zpath_free_cloud_objects(zpn_samlsp_array);
        zpa_free_authsp_list(zpn_authsp_array, &zpn_authsp_cnt);
        zpath_free_cloud_objects(zpn_authsp_array);
        zpa_free_saml_audience_list(saml_audience_list_array, &zpn_saml_audience_cnt);
        zpath_free_cloud_objects(saml_audience_list_array);
        zpa_free_cloud_certs_list(tmpRoots, &zpn_root_cert_cnt);
        zpath_free_cloud_objects(tmpRoots);
        zpa_free_cloud_certs_list(tmpCaCerts, &zpn_trusted_ca_cert_cnt);
        zpath_free_cloud_objects(tmpCaCerts);
        zpath_free_cloud_objects(tmp_cloud_name);
        zpath_free_cloud_objects(tmp_pbroker_domain);
        zpa_free_domain_misc_list(tmp_misc, &misc_count);
        zpath_free_cloud_objects(tmp_misc);
        zpa_free_authenticated_snis_list(tmp_authsnis, &auth_sni_count);
        zpath_free_cloud_objects(tmp_authsnis);
        return ZPATH_RESULT_ERR;
    }
    cloud_config_obj->cloud_name = tmp_cloud_name;
    cloud_config_obj->pbroker_broker_domain = tmp_pbroker_domain;
    cloud_config_obj->cloud_type = zpath_json_get_int(cloud_json_obj, ZPA_CLOUD_JSON_CONFIG_CLOUD_TYPE);
    cloud_config_obj->sarge = tmpSarge;
    cloud_config_obj->exporter = tmpExp;
    cloud_config_obj->zpn_apis = zpn_apis_array;
    cloud_config_obj->zpn_apis_count = zpn_apis_cnt;
    cloud_config_obj->zpn_samlsps = zpn_samlsp_array;
    cloud_config_obj->zpn_samlsp_count = zpn_samlsp_cnt;
    cloud_config_obj->zpn_authsps = zpn_authsp_array;
    cloud_config_obj->zpn_authsp_count = zpn_authsp_cnt;
    cloud_config_obj->saml_audience_list = saml_audience_list_array;
    cloud_config_obj->saml_audience_list_count = zpn_saml_audience_cnt;
    cloud_config_obj->zpa_cloud_root_certs = tmpRoots;
    cloud_config_obj->zpa_cloud_root_certs_count = zpn_root_cert_cnt;
    cloud_config_obj->zpa_cloud_trusted_ca_certs = tmpCaCerts;
    cloud_config_obj->zpa_cloud_trusted_ca_certs_count = zpn_trusted_ca_cert_cnt;
    cloud_config_obj->zpath_misc = tmp_misc;
    cloud_config_obj->zpath_misc_count = misc_count;
    cloud_config_obj->zpath_authenticated_sni = tmp_authsnis;
    cloud_config_obj->zpath_authenticated_sni_count = auth_sni_count;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Cloud Config: Parsing Cloud Configuration for Cloud %s ", tmp_cloud_name);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_parse_zpa_cloud_config_file(const JSON_Object *root_object) {
    // Step 1: Get zpa_clouds array object
    size_t tmp_count=0;
    const JSON_Array *cloud_config_array = zpath_json_get_array_object(root_object, ZPA_CLOUD_JSON_CONFIG_ZPA_CLOUDS,
                                                                       &tmp_count);
    if ((!cloud_config_array) ||
        (tmp_count==0)) {
        ZPATH_LOG(AL_WARNING,"Failure: Cloud Config: Parsing zpa_clouds from JSON file");
        return ZPATH_RESULT_ERR;
    }
    g_zpa_cloud_config.cloud_config =
            ZPA_CLOUD_CONFIG_CALLOC(tmp_count * sizeof(struct zpath_cloud_config *));

    if (!g_zpa_cloud_config.cloud_config) {
        return ZPATH_RESULT_ERR;
    }

    // Step2: Parse individual cloud configuration
    size_t valid_cnt=0;
    for(int cind=0; cind < tmp_count; cind++) {
        const JSON_Object *cloud_object = zpath_json_get_object_from_array(cloud_config_array, cind);
        if (!cloud_object) {
            continue;
        }
        struct zpath_cloud_config *tmp_cc_obj = ZPA_CLOUD_CONFIG_CALLOC(sizeof(struct zpath_cloud_config));
        if (ZPATH_RESULT_ERR == zpath_create_cloud_object_json_data(cloud_object, tmp_cc_obj)) {
            zpath_free_cloud_objects(tmp_cc_obj);
            continue;
        } else {
            g_zpa_cloud_config.cloud_config[valid_cnt] = tmp_cc_obj;
            valid_cnt++;
        }
    }

    // Step 3: Validate cloud configuration parsing
    if(valid_cnt <= 0) {
        ZPATH_LOG(AL_WARNING,"Failure: Cloud Config: No valid cloud config found in JSON file");
        zpath_free_cloud_objects(g_zpa_cloud_config.cloud_config);
        return ZPATH_RESULT_ERR;
    }
    if(tmp_count != valid_cnt) {
        ZPATH_LOG(AL_WARNING,"Cloud Config: JSON file includes %zu clouds, valid are %zu cloud configuration", tmp_count, valid_cnt);
    }

    // Step 4: Parse common cloud configuration
    g_zpa_cloud_config.cloud_config_count = valid_cnt;
    ZPATH_DEBUG_CLOUD_CONFIG("Total Valid Cloud Configurations loaded %zu ", valid_cnt);

    ZPATH_DEBUG_CLOUD_CONFIG("SUCCESS: Completed parsing the ZPA Cloud Configuration File");
    return ZPATH_RESULT_NO_ERROR;
}

char *base64url_to_base64(const char *input) {
    if (!input)
        return NULL;
    size_t len = strlen(input);
    size_t output_len = len + 4 - (len % 4);
    char *base64 = ZPA_CLOUD_CONFIG_CALLOC(output_len + 1);
    memcpy(base64, input, len);

    // Replace URL-safe characters
    for (int i = 0; i < len; i++) {
        if (base64[i] == '-')
            base64[i] = '+';
        if (base64[i] == '_')
            base64[i] = '/';
    }

    // Add padding if necessary
    size_t padding_needed = 4 - (len % 4);
    if (padding_needed < 4) {
        for (size_t i = 0; i < padding_needed; ++i) {
            base64[len + i] = '=';
        }
        base64[len + padding_needed] = '\0';
    } else {
        base64[len] = '\0';  // No padding needed, just null terminate
    }
    return base64;
}

static char *base64url_decode(const char *encoded_str) {
    char *b64encoded_str = NULL;
    char *decoded_str = NULL;
    if (encoded_str) {
        b64encoded_str = base64url_to_base64(encoded_str);
        if (b64encoded_str) {
            decoded_str = base64_decode(b64encoded_str);
            zpath_free_cloud_objects(b64encoded_str);
        }
    }
    if(!decoded_str){
        ZPATH_LOG(AL_WARNING,"FAILED: BaseURL Decoder");
        return decoded_str;
    }
    return decoded_str;
}

static int base64url_decode_binary(const char *encoded_str, unsigned char *signature_decoded) {
    int slen = 0;
    if (encoded_str) {
        char *b64encoded_str = base64url_to_base64(encoded_str);
        slen = base64_decode_binary(signature_decoded, b64encoded_str, strlen(b64encoded_str));
        zpath_free_cloud_objects(b64encoded_str);
    }
    if(slen == -1) {
        ZPATH_LOG(AL_WARNING,"FAILED: BaseURL Binary decoder");
        return slen;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("SUCCESS: BaseURL Binary decoder");

    return slen;
}

static int get_content_hash(const uint8_t *input, const size_t input_len, unsigned char hash[], size_t *hash_length) {
    uint8_t sha[SHA256_DIGEST_LENGTH];
    EVP_MD_CTX *ctx;

    if (!(ctx = EVP_MD_CTX_create())) {
        ZPATH_LOG(AL_WARNING, "Could not SHA create");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), NULL)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_WARNING, "Could not SHA init");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestUpdate(ctx, input, input_len)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_WARNING, "Could not SHA update");
        return ZPATH_RESULT_ERR;
    }

    unsigned int shalen = sizeof(sha);
    if (1 != EVP_DigestFinal_ex(ctx, sha, &shalen)) {
        ZPATH_LOG(AL_WARNING, "Could not SHA final");
        EVP_MD_CTX_destroy(ctx);
        return ZPATH_RESULT_ERR;
    }
    EVP_MD_CTX_destroy(ctx);

    if (shalen != sizeof(sha)) {
        ZPATH_LOG(AL_WARNING, "Bad SHA length");
        return ZPATH_RESULT_ERR;
    }

    if (*hash_length < shalen) {
        ZPATH_LOG(AL_WARNING, "Buffer too small. cannot write hash");
        return ZPATH_RESULT_ERR;
    }

    *hash_length = shalen;
    memcpy(hash, sha, shalen);
    ZPATH_DEBUG_CLOUD_CONFIG("SUCCESS: Generated Hash for given message");
    return ZPATH_RESULT_NO_ERROR;
}

static int zpa_cloud_verify_cert_chain_and_signature(const char *header,
                                                     const char *payload,
                                                     const char *signature,
                                                     const JSON_Object *header_obj) {
    unsigned char signature_decoded[1000] = {0};
    size_t raw_msg_len = 0;
    char *raw_msg = NULL;
    size_t cert_count = 0;
    char **certs_chain_list = NULL;
    char **intermediate_pems = NULL;
    char *signing_cert_pem = NULL;
    unsigned char origin_hash[SHA256_DIGEST_LENGTH];
    char cert_failure_remediation_str[1000];
    char reason[500] = {0};
    size_t intermediate_count = 0;

    // Get the Signature
    // - DO NOT free signature_decoded
    int signature_len = base64url_decode_binary(signature, signature_decoded);
    if (signature_len == -1) {
        ZPATH_LOG(AL_WARNING, "Failure: Decoding Signature Failed\n");
        return ZPATH_RESULT_ERR;
    }

    // Prepare Raw Message for Hash calculation
    // FREE - raw_msg
    raw_msg_len =
            strnlen(header, MAX_ZPA_CLOUD_CONFIG_PAYLOAD_LEN) + strnlen(payload, MAX_ZPA_CLOUD_CONFIG_PAYLOAD_LEN) + 2;
    raw_msg = ZPA_CLOUD_CONFIG_CALLOC(raw_msg_len);
    sxprintf(raw_msg, raw_msg + raw_msg_len, "%s.%s", header, payload);

    // Get the Cert Chain
    // FREE - intermediate_pems, certs_chain_list(loop and free)
    certs_chain_list = zpath_get_cert_chains_from_header(header_obj, &cert_count);
    if (!certs_chain_list) {
        ZPATH_LOG(AL_WARNING, "Failure: Missing Intermediate Certs\n");
        goto cleanup_err;
    }
    if (cert_count > 1) {
        intermediate_count = cert_count - 1;
        intermediate_pems = ZPA_CLOUD_CONFIG_CALLOC(sizeof(char *) * intermediate_count);
        for (int i = 1; i < cert_count; i++) {
            intermediate_pems[i - 1] = certs_chain_list[i];
        }
    }
    signing_cert_pem = certs_chain_list[0];

    // Step 1: Get SHA256 of RAW Message
    // We have to verify the signature for the HASH/Challenge
    // So we compute the SHA256 of the given message.
    size_t hashlen = SHA256_DIGEST_LENGTH;
    get_content_hash((uint8_t *)raw_msg, strlen(raw_msg), origin_hash, &hashlen);

    // Step 2: Verify Signature of the HASH using Leaf certificate signing_cert_pem
    int res = zcrypt_verify_signed(origin_hash, SHA256_DIGEST_LENGTH, (uint8_t *)signature_decoded,
                                   (size_t)signature_len, signing_cert_pem);
    if (res) {
        ZPATH_LOG(AL_WARNING, "Failure: Message Verification failed\n");
        goto cleanup_err;
    } else {
        ZPATH_DEBUG_CLOUD_CONFIG( "Success: Message Signature Verification\n");

        // Step 3: Verify Certificate Chain - all certs in certs_chain_list
        // - ROOT Cert Trust Anchor - available in g_zpa_cloud_config_root_certs
        // - All intermediate certs should lead to ROOT cert
        // - Leaf cert should be signed by first intermediate
        res = zcrypt_verify_chain(g_zpa_cloud_config_root_certs, g_zpa_cloud_config_root_cert_cnt, NULL, 0,
                                  intermediate_pems, intermediate_count, signing_cert_pem, zcrypt_cert_purpose_any,
                                  reason, sizeof(reason), cert_failure_remediation_str,
                                  sizeof(cert_failure_remediation_str));
        if (res != ZCRYPT_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_WARNING, "Failure: Certificate Verification failed\n");
            ZPATH_LOG(AL_WARNING, "Failure: Certificates retrieved do not successfully verify: %s, cert=\n%s\n", reason,
                                signing_cert_pem);
            if (strnlen(cert_failure_remediation_str, 1000)) {
                ZPATH_LOG(AL_WARNING, "%s", cert_failure_remediation_str);
            }
            goto cleanup_err;
        }
    }
    if (intermediate_pems)
        zpath_free_cloud_objects(intermediate_pems);
    if (cert_count > 0)
        zpath_free_cert_chains(certs_chain_list, (int)cert_count);
    if (certs_chain_list)
        zpath_free_cloud_objects(certs_chain_list);
    zpath_free_cloud_objects(raw_msg);
    ZPATH_DEBUG_CLOUD_CONFIG("SUCCESS: Message Signature and Certificate Chain");
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    if (intermediate_pems)
        zpath_free_cloud_objects(intermediate_pems);
    if (cert_count > 0)
        zpath_free_cert_chains(certs_chain_list, (int)cert_count);
    if (certs_chain_list)
        zpath_free_cloud_objects(certs_chain_list);
    if (raw_msg)
        zpath_free_cloud_objects(raw_msg);
    ZPATH_LOG(AL_WARNING,"Failure: Message Signature and Certificate Chain");
    return ZPATH_RESULT_ERR;
}

static int zpa_cloud_verify_jws(const char *jws_string, JSON_Object **payload_out, JSON_Value **payload_value, JSON_Value **root_value ) {
    *root_value = json_parse_string(jws_string);
    const JSON_Object *root_object = json_value_get_object(*root_value);
    const JSON_Object *header_json = NULL;
    JSON_Value *header_json_value = NULL;
    char *protected_header = NULL;
    char *signature_b64url = NULL;
    char *payload_json = NULL;
    char *decoded_header = NULL;
    // Convert all JSON Objects to strings
    protected_header = zpath_json_strdup(root_object, "protected");
    payload_json = zpath_json_strdup(root_object, "payload");
    *payload_value = json_parse_string(payload_json);
    *payload_out = json_value_get_object(*payload_value);
    signature_b64url = zpath_json_strdup(root_object, "signature");

    if ((!protected_header) || (!payload_json) || (!signature_b64url)) {
        ZPATH_DEBUG_CLOUD_CONFIG("Failure: Missing key data from config file - header, payload or signature");
        goto cleanup_err;
    }

    // Convert all BASE64URL Encoding to Plain Text
    decoded_header = base64url_decode(protected_header);
    header_json_value = json_parse_string(decoded_header);
    if (!header_json_value) {
        ZPATH_LOG(AL_WARNING, "Failure: Error parsing header from JSON.\n");
        goto cleanup_err;
    }

    header_json = json_value_get_object(header_json_value);

    if (ZPATH_RESULT_NO_ERROR !=
        (zpa_cloud_verify_cert_chain_and_signature(protected_header, payload_json, signature_b64url, header_json))) {
        ZPATH_LOG(AL_WARNING, "Failure: Verifying Cert Chain and Signature\n");
        goto cleanup_err;
    }

    if (header_json_value)
        json_value_free(header_json_value);
    if (decoded_header)
        free(decoded_header);
    if (signature_b64url)
        zpath_free_cloud_objects(signature_b64url);
    if (payload_json)
        zpath_free_cloud_objects(payload_json);
    if (protected_header)
        zpath_free_cloud_objects(protected_header);
    ZPATH_LOG(AL_NOTICE,"Success: Completed verification of Cloud Config JWS JSON");
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    if (header_json_value)
        json_value_free(header_json_value);
    if (decoded_header)
        free(decoded_header);
    if (signature_b64url)
        zpath_free_cloud_objects(signature_b64url);
    if (payload_json)
        zpath_free_cloud_objects(payload_json);
    if (protected_header)
        zpath_free_cloud_objects(protected_header);
    ZPATH_LOG(AL_WARNING, "Failure: Verifying the Cloud Config JWS JSON\n");
    return ZPATH_RESULT_ERR;
}

static int zpa_cloud_config_verify_parse(const char *config_file_path, const char *jws_string) {
    int ret = ZPATH_RESULT_ERR;
    JSON_Object *root_object = NULL;
    JSON_Value *root_value = NULL;
    JSON_Value *payload_value = NULL;
    ZPATH_DEBUG_CLOUD_CONFIG("Debug: Reading ZPA Cloud JSON config source: %s ", config_file_path);
    ret = zpa_cloud_verify_jws(jws_string, &root_object, &payload_value, &root_value);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_WARNING, "Error: Verifying ZPA Cloud JSON config Signature %s \n", config_file_path);
    } else {
        ret = zpath_parse_zpa_cloud_config_file(root_object);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_WARNING, "Error: Parsing ZPA Cloud JSON config contents %s \n", config_file_path);
        } else {
            ZPATH_DEBUG_CLOUD_CONFIG("!! Successfully Read ZPA Cloud config source:  %s !! ", config_file_path);
        }
    }
    json_value_free(root_value);
    json_value_free(payload_value);
    return ret;
}

static int check_if_local_file_exists(const char *filename) {
    FILE *file = fopen(filename, "r");
    if(file) {
        fclose(file);
        return ZPATH_RESULT_NO_ERROR;
    }
    return ZPATH_RESULT_NOT_FOUND;
}

static char *zpa_cloud_read_jws_from_file(const char *jws_file, size_t *len) {
    int64_t filelen = 0;
    char *buffer = NULL;
    FILE *fileptr = fopen(jws_file, "r");
    if (!fileptr) {
        ZPATH_LOG(AL_WARNING, "Failure: Unable to open file:%s ", jws_file);
        return NULL;
    }
    if (fseek(fileptr, 0, SEEK_END) != 0) {
        ZPATH_LOG(AL_WARNING, "Failure: Unable to set seek to end of file");
        fclose(fileptr);
        return NULL;
    }
    filelen = ftell(fileptr);
    if (filelen < 0) {
        ZPATH_LOG(AL_WARNING, "Failure: Failed to get length of file");
        fclose(fileptr);
        return NULL;
    }
    rewind(fileptr);
    buffer = (char *)ZPA_CLOUD_CONFIG_CALLOC(filelen * sizeof(char) + 1);
    if (!buffer) {
        ZPATH_LOG(AL_WARNING, "Failure: Unable to allocate memory.");
        fclose(fileptr);
        return NULL;
    }
    size_t fsize = fread(buffer, filelen, 1, fileptr);
    if (fsize < 1) {
        if (feof(fileptr)) {
            ZPATH_LOG(AL_WARNING,"Failure: ZPA Cloud Config file is empty");
        } else if (ferror(fileptr)) {
            int err = errno;
            ZPATH_LOG(AL_WARNING,"Failure: Error reading config file %s\n", strerror(err));
        }
        fclose(fileptr);
        ZPA_CLOUD_CONFIG_FREE(buffer);
        return NULL;
    }
    ZPATH_DEBUG_CLOUD_CONFIG("Success: ZPA Cloud Config file read from disk");
    buffer[filelen] = '\0';
    *len = (size_t)filelen;
    fclose(fileptr);
    return buffer;
}

#if 0
static uint16_t zpath_fohh_proxy_port = 80;
static struct fohh_http_client *zpath_get_file_http_client(SSL_CTX *ssl_ctx,
                                                    char *proxy_hostname,
                                                    char *api_hostname,
                                                    struct zcdns *zcdns) {
    struct fohh_http_client *client;
    enum fohh_http_client_status status;
    struct fohh_ssl_status *ssl_status;
    char str[INET6_ADDRSTRLEN];
    struct sockaddr_storage remote_addr;

    if (fohh_proxy_hostname) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Fetching from %s via %s via proxy %s:%d", api_hostname, proxy_hostname,
                  fohh_proxy_hostname, zpath_fohh_proxy_port);
    } else {
        ZPATH_DEBUG_CLOUD_CONFIG("Fetching from %s via %s", api_hostname, proxy_hostname);
    }

    client = fohh_http_client_create_synchronous(zcdns,
                                                 ssl_ctx,
                                                 api_hostname,
                                                 proxy_hostname,
                                                 fohh_proxy_hostname,
                                                 443,
                                                 zpath_fohh_proxy_port,
                                                 10 * (1000 * 1000),
                                                 &status);
    if (!client) {
        ZPATH_LOG(AL_WARNING, "Could not create HTTP context");
        return NULL;
    }

    if (status != fohh_http_client_status_success) {
        if (!fohh_http_client_get_remote_addr(client, &remote_addr)) {
            zcdns_sockaddr_storage_to_str(&remote_addr, str, sizeof(str));
        } else {
            snprintf(str, sizeof(str), "unknown");
        }
        switch (status) {
        case fohh_http_client_status_dns_timeout:
            ZPATH_LOG(AL_WARNING, "DNS timed out for %s", proxy_hostname);
            break;
        case fohh_http_client_status_dns_fail:
            ZPATH_LOG(AL_WARNING, "DNS resolution failed for %s", proxy_hostname);
            break;
        case fohh_http_client_status_connect_timeout:
            ZPATH_LOG(AL_WARNING, "TCP connection timed out to %s: %s:443", proxy_hostname, str);
            break;
        case fohh_http_client_status_connect_fail:
            ZPATH_LOG(AL_WARNING, "TCP connection failed to %s: %s:443", proxy_hostname, str);
            break;
        case fohh_http_client_status_connect_fail_ssl:
            ssl_status = fohh_http_client_get_ssl_status(client);
            if (ssl_status->err) {
                if (fohh_proxy_hostname) {
                    ZPATH_LOG(AL_WARNING,
                              "TLS Verification Failure via %s:443 via proxy %s:%d: Failed certificate check at "
                              "depth=%d, where subject=%s, issuer=%s. Error=%s",
                              str, fohh_proxy_hostname, zpath_fohh_proxy_port, ssl_status->x509_err_depth,
                              ssl_status->err_subject, ssl_status->err_issuer,
                              ssl_status->x509_err ? ssl_status->x509_err : "Unknown");
                } else {
                    ZPATH_LOG(AL_WARNING,
                              "TLS Verification Failure via %s:443: Failed certificate check at depth=%d, where "
                              "subject=%s, issuer=%s. Error=%s",
                              str, ssl_status->x509_err_depth, ssl_status->err_subject, ssl_status->err_issuer,
                              ssl_status->x509_err ? ssl_status->x509_err : "Unknown");
                }
            } else {
                ZPATH_LOG(AL_WARNING, "TLS connection failed before certificate verification");
            }
            break;
        case fohh_http_client_status_timeout:
            if (fohh_proxy_hostname) {
                ZPATH_LOG(AL_WARNING, "HTTP request timeout to %s via %s:443 via proxy %s:%d", api_hostname, str,
                          fohh_proxy_hostname, zpath_fohh_proxy_port);
            } else {
                ZPATH_LOG(AL_WARNING, "HTTP request timeout to %s via %s:443", api_hostname, str);
            }
            break;
        default:
            if (fohh_proxy_hostname) {
                ZPATH_LOG(AL_WARNING, "HTTP failure to %s via %s:443 via proxy %s:%d", api_hostname, str,
                          fohh_proxy_hostname, zpath_fohh_proxy_port);
            } else {
                ZPATH_LOG(AL_WARNING, "HTTP failure to %s via %s:443", api_hostname, str);
            }
            break;
        }
        fohh_http_client_destroy_from_another_thread(client);
        return NULL;
    } else {
        ZPATH_LOG(AL_NOTICE, "HTTP client fetch success for Cloud Config");
    }
    return client;
}

static int zpath_dist_download_zpa_cloud_config(char *tokens[MAX_ENROLLMENT_KEY_BLOCKS],
                                                const char *config_fetch_ca_file,
                                                int is_zscaler_os,
                                                struct zcdns *sarge_zcdns) {
    char zpa_cloud_config_path[1000];
    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    int res;
    FILE *fp = NULL;
    size_t len;

    if ((!tokens[DISTRIBUTION_HOST_KEY_INDEX]) || (!tokens[DISTRIBUTION_PROXY_KEY_INDEX]) ||
        (!tokens[DISTRIBUTION_PATH_KEY_INDEX])) {
        ZPATH_DEBUG_CLOUD_CONFIG( "Provisioning Key does not have Distribution server details");
        return ZPATH_RESULT_ERR;
    }
    char *fetch_proxyname = tokens[DISTRIBUTION_PROXY_KEY_INDEX];
    char *fetch_hostname = tokens[DISTRIBUTION_HOST_KEY_INDEX];

    unlink(ZPA_CLOUD_CONFIG_FILE_FULL_PATH);
    snprintf(zpa_cloud_config_path, sizeof(zpa_cloud_config_path), "%s/%s", tokens[DISTRIBUTION_PATH_KEY_INDEX],
             ZPA_CLOUD_CONFIG_DIST_FILE_PATH);

    if (config_fetch_ca_file) {
        ssl_ctx = fohh_http_client_get_ssl_ctx_from_file(config_fetch_ca_file);
    } else {
        /* Create SSL ctx with hardcoded certs, this should never fail */
        ssl_ctx = fohh_http_client_get_ssl_ctx();
        /* Add OS bundle */
        if (is_zscaler_os == 1) {
            ZPATH_DEBUG_CLOUD_CONFIG("ZscalerOS detected");
        }
        if (ssl_ctx) {
            res = fohh_http_client_add_os_bundle_to_ssl_ctx(ssl_ctx, is_zscaler_os);
            if (res) {
                ZPATH_LOG(AL_INFO, "Cannot fetch OS bundle, error: %s, using built in root CAs",
                          fohh_result_string(res));
            }
        }
    }
    if (!ssl_ctx) {
        ZPATH_LOG(AL_WARNING, ": Cannot get SSL context");
        goto cleanup_err;
    }

    client = zpath_get_file_http_client(ssl_ctx, fetch_proxyname, fetch_hostname, sarge_zcdns);
    if (!client) {
        if (fohh_proxy_hostname) {
            ZPATH_LOG(AL_WARNING, ": Could not connect to %s via %s via proxy %s:%d", fetch_hostname, fetch_proxyname,
                      fohh_proxy_hostname, zpath_fohh_proxy_port);
        } else {
            ZPATH_LOG(AL_WARNING, ": Could not connect to %s via %s", fetch_hostname, fetch_proxyname);
        }
        goto cleanup_err;
    }

    res = fohh_http_client_fetch_synchronous("Fetch ZPA Cloud Config",
                                             client,
                                             FOHH_HTTP_METHOD_GET,
                                             zpa_cloud_config_path,
                                             200,          /* Expected HTTP status */
                                             &http_status, /* Received HTTP status */
                                             &body,
                                             0);
    if (res) {
        ZPATH_LOG(AL_WARNING, ": Could not get zpa_cloud config file ");
        goto cleanup_err;
    }
    ZPATH_DEBUG_CLOUD_CONFIG(": Successfully fetched %s file from %s", zpa_cloud_config_path, fetch_hostname);

    fp = fopen(ZPA_CLOUD_CONFIG_FILE_FULL_PATH, "w");
    if (!fp) {
        goto cleanup_err;
    }
    char *buffer = ZPA_CLOUD_CONFIG_CALLOC(MAX_FILE_CHUNK_SIZE);
    while ((len = evbuffer_remove(body, buffer, MAX_FILE_CHUNK_SIZE)) > 0) {
        if (fwrite(buffer, 1, len, fp) != len) {
            ZPATH_LOG(AL_WARNING, ": Could not write to %s", ZPA_CLOUD_CONFIG_FILE_FULL_PATH);
            goto cleanup_err;
        }
    }
    ZPA_CLOUD_CONFIG_FREE(buffer);
    fclose(fp);
    evbuffer_free(body);
    body = NULL;
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Downloaded the Cloud Config from Dist server");
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    if (client)
        fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx)
        SSL_CTX_free(ssl_ctx);
    if (fp)
        fclose(fp);
    if (body)
        evbuffer_free(body);
    ZPATH_LOG(AL_WARNING, "Failure: Download the Cloud Config from Dist server");
    return ZPATH_RESULT_ERR;
}
#endif

/* zpath_load_local_zpa_cloud_config
 * Loads the cloud config from local disk
 *
 * If Failure:
 *    Removes the configuration and Frees the memory
 *
 * DO NOT call this API directly from applications.
 * Use following functions:
 *  zpath_load_zpa_cloud_config -> Read from file or fallback to default
 *  zpath_load_cloud_config_for_customer_apps -> Download or Read or fallback to default
 *
*/
int zpath_load_local_zpa_cloud_config(const char *config_file_path) {
    int ret = ZPATH_RESULT_ERR;
    char *jws_string = NULL;
    size_t filelen = 0;
    if (!config_file_path) {
        ZPATH_LOG(AL_WARNING, "Failure: ZPA Cloud JSON config file empty \n");
        return ret;
    }
    if (check_if_local_file_exists(config_file_path) == ZPATH_RESULT_NOT_FOUND) {
        ZPATH_DEBUG_CLOUD_CONFIG("Local ZPA Cloud JSON config file NOT Found \n");
        return ZPATH_RESULT_NOT_FOUND;
    }
    jws_string = zpa_cloud_read_jws_from_file(config_file_path, &filelen);
    if (jws_string) {
        ret = zpa_cloud_config_verify_parse(config_file_path, jws_string);
        if (ret != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_WARNING, "Failure: Verify and Parse ZPA Cloud JSON config file %s \n", config_file_path);
        } else {
            ZPATH_LOG(AL_NOTICE, "Successfully loaded config file: %s", config_file_path);
        }
    } else {
        ZPATH_LOG(AL_WARNING, "Failure: Reading ZPA Cloud JSON config file %s \n", config_file_path);
    }
    zpath_free_cloud_objects(jws_string);
    return ret;
}

/* zpath_load_default_zpa_cloud_config
 * Loads the default cloud config from binary
 *
 * If Failure:
 *    Removes the configuration and Frees the memory
 *
 * DO NOT call this API directly from applications.
 * Use following functions:
 *  zpath_load_zpa_cloud_config -> Read from file or fallback to default
 *  zpath_load_cloud_config_for_customer_apps -> Download or Read or fallback to default
 *
 */
int zpath_load_default_zpa_cloud_config() {
    int ret = ZPATH_RESULT_ERR;
    g_zpa_cloud_config_default = ZPA_CLOUD_CONFIG_CALLOC(zpa_cloud_config_default_len + 1);
    memcpy(g_zpa_cloud_config_default, zpa_cloud_config_default, zpa_cloud_config_default_len);
    g_zpa_cloud_config_default[zpa_cloud_config_default_len] = 0;
    ret = zpa_cloud_config_verify_parse("DEFAULT_ZPA_CLOUD_CONFIG", g_zpa_cloud_config_default);
    if (ret != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Failure: Verify and Parse DEFAULT ZPA Cloud JSON config file\n");
    } else {
        g_zpa_config_source=ZPA_COUD_CONFIG_SOURCE_DEFAULT;
        ZPATH_LOG(AL_NOTICE, "Successfully loaded config file: DEFAULT_ZPA_CLOUD_CONFIG");
    }
    return ret;
}


/* zpath_load_zpa_cloud_config
 * Loads configuration from Disk or fallback to default
 *
 * To be used by APPS that are maintainted by ZPA
 * ie. Do not depend on Dist server for Configuration
 */
int zpath_load_zpa_cloud_config(const char *config_file_path) {
    int ret = ZPATH_RESULT_ERR;

    ZPATH_DEBUG_CLOUD_CONFIG("Reading Cloud Config from %s",config_file_path);

    /* We will start supporting reading from the local configuration file
     * along with encryption support
     * after encryption support, just enable below #if 0
     */
    ret = zpath_load_local_zpa_cloud_config(config_file_path);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        g_zpa_config_source=ZPA_COUD_CONFIG_SOURCE_LOCAL_DISK;
    } else if (ret == ZPATH_RESULT_NOT_FOUND) {
        // Read the default config file only if the file is not found
        ZPATH_DEBUG_CLOUD_CONFIG("Reading Default Cloud Config file");
        ret = zpath_load_default_zpa_cloud_config();
    }
    if (ret == ZPATH_RESULT_NO_ERROR ) {
        const struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_name(ZPATH_LOCAL_CLOUD_NAME);
        if ( (cloud_config) && (cloud_config->zpa_cloud_trusted_ca_certs_count > 0 )) {
            fohh_add_ext_trusted_certs(cloud_config->zpa_cloud_trusted_ca_certs, cloud_config->zpa_cloud_trusted_ca_certs_count);
        }
    }
    return ret;
}

/**
 * Writes the encrypted cloud configuration data to a file.
 *
 * This function takes the provided zcrypt key and cloud configuration data, encrypts the data using the zcrypt key,
 * and writes the encrypted data to the specified file.
 *
 * @return ZPATH_RESULT_NO_ERROR if the operation is successful, ZPATH_RESULT_ERR otherwise.
 */
static int zpath_write_encrypted_cloud_config_file(struct zcrypt_key *key, char *cloud_config_data, const char* crypt_file, size_t data_len) {
    FILE *fp;
    uint8_t *file_data = NULL;
    size_t file_len = 0;
    int res = ZPATH_RESULT_ERR;

    fp = fopen(crypt_file, "w");
    if (!fp) {
        ZPATH_LOG(AL_WARNING, "Cannot open %s for writing\n", crypt_file);
        return ZPATH_RESULT_ERR;
    }

    file_len = ((data_len / 16) + 2) * 16;
    file_data = (uint8_t *)ZPA_CLOUD_CONFIG_CALLOC(file_len);

    res = zcrypt_encrypt(key, cloud_config_data, data_len, file_data, &file_len);
    if (res != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_WARNING, "Cannot encrypt for %s, failed  \n", crypt_file);
        fclose(fp);
        unlink(crypt_file);
        ZPA_CLOUD_CONFIG_FREE(file_data);
        return ZPATH_RESULT_ERR;
    }

    if (fwrite(file_data, file_len, 1, fp)!= 1) {
        ZPATH_LOG(AL_WARNING, "Cannot write to %s\n", crypt_file);
        fclose(fp);
        unlink(crypt_file);
        ZPA_CLOUD_CONFIG_FREE(file_data);
        return ZPATH_RESULT_ERR;
    }

    ZPA_CLOUD_CONFIG_FREE(file_data);
    fclose(fp);
    return ZPATH_RESULT_NO_ERROR;
}

/**
 * Reads and decrypts the cloud configuration file.
 *
 * This function opens the encrypted cloud configuration file, reads its contents,
 * decrypts the data using the provided key, and returns the decrypted data.
 *
 * @return The decrypted cloud configuration data, or NULL on failure.
 */
static char *zpath_read_encrypted_cloud_config_file(struct zcrypt_key *key, const char* crypt_file, size_t *cloud_config_data_len) {
    FILE *fp;
    char *file_data = NULL;
    char *out_data = NULL;
    int file_len = 0;
    size_t data_bytes = 0;
    size_t out_len = 0;
    fp = fopen(crypt_file, "rb");
    if (!fp) {
        ZPATH_LOG(AL_WARNING, "Unable to open file:%s for reading", crypt_file);
        return NULL;
    }

    if (fseek(fp, 0, SEEK_END)!= 0) {
        ZPATH_LOG(AL_WARNING, "Unable to set seek to end of file");
        fclose(fp);
        return NULL;
    }

    file_len = (int) ftell(fp);
    if (file_len < 0) {
        // Log an error if getting the file length fails.
        ZPATH_LOG(AL_WARNING, "Failed to get length of file");
        fclose(fp);
        return NULL;
    }

    if (fseek(fp, 0L, SEEK_SET)!= 0) {
        ZPATH_LOG(AL_WARNING, "Unable to set seek to beginning");
        fclose(fp);
        return NULL;
    }

    file_data = (char *)ZPA_CLOUD_CONFIG_CALLOC(file_len * sizeof(char) + 1);
    if (!file_data) {
        ZPATH_LOG(AL_WARNING, "Unable to allocate memory.");
        fclose(fp);
        return NULL;
    }

    data_bytes = fread(file_data, 1, (size_t)file_len, fp);

    out_len = file_len + 1;
    out_data = (char *)ZPA_CLOUD_CONFIG_CALLOC(out_len * sizeof(char) + 1);
    if (!out_data) {
        // Log an error if memory allocation fails.
        ZPATH_LOG(AL_WARNING, "Unable to allocate memory.");
        fclose(fp);
        return NULL;
    }

    if (zcrypt_decrypt(key, file_data, data_bytes, out_data, &out_len)!= ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_WARNING, "Unable to decrypt file");
        ZPA_CLOUD_CONFIG_FREE(out_data);
        out_data = NULL;
    }

    *cloud_config_data_len = out_len;

    fclose(fp);
    ZPA_CLOUD_CONFIG_FREE(file_data);
    return out_data;
}

/**
 * zpath_load_encrypted_cloud_config:
 *    Loads the encrypted ZPA Cloud configuration file, decrypts it using the provided key,
 *    and verifies its signature.
 *    If the file does not exist, or if any step in the process fails, the function logs an error.
 */
int zpath_load_encrypted_cloud_config(struct zcrypt_key *key, const char* crypt_file) {
    int ret = ZPATH_RESULT_ERR;
    char *jws_string = NULL;
    size_t file_len = 0;
    if (check_if_local_file_exists(crypt_file) == ZPATH_RESULT_NOT_FOUND) {
        ZPATH_DEBUG_CLOUD_CONFIG("Encrypted ZPA Cloud JSON config file NOT Found \n");
        return ZPATH_RESULT_NOT_FOUND;
    }

    jws_string = zpath_read_encrypted_cloud_config_file(key, crypt_file, &file_len);

    if (jws_string) {
        ret = zpa_cloud_config_verify_parse(crypt_file, jws_string);

        if (ret!= ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_WARNING,
                      "Failure: Verify and Parse ZPA Cloud JSON config file %s \n",
                      crypt_file);
        } else {
            ZPATH_LOG(AL_NOTICE, "Successfully loaded config file: %s", crypt_file);
        }
    } else {
        ZPATH_LOG(AL_WARNING, "Failure: Reading ZPA Cloud JSON config file %s \n", crypt_file);
    }

    zpath_free_cloud_objects(jws_string);

    return ret;
}

/**
 * Checks if a local ZPA cloud configuration file exists.
 *
 * This function checks for the existence of both the plain and encrypted
 * versions of the ZPA cloud configuration file.
 *
 */
int zpath_check_if_local_cloud_config_file_exists() {
    if (check_if_local_file_exists(ZPA_CLOUD_CONFIG_CUST_APPS_PLAIN_FILE) == ZPATH_RESULT_NOT_FOUND) {
        ZPATH_DEBUG_CLOUD_CONFIG("Local ZPA Cloud JSON config file NOT Found \n");

        if (check_if_local_file_exists(ZPA_CLOUD_CONFIG_CUST_APPS_CRYPT_FILE) == ZPATH_RESULT_NOT_FOUND) {
            // Log a debug message if the encrypted file is not found
            ZPATH_DEBUG_CLOUD_CONFIG("Encrypted Local ZPA Cloud JSON config file NOT Found \n");

            return ZPATH_RESULT_NOT_FOUND;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

/**
 * zpath_load_cloud_config_from_local_disk_for_customer_apps:
 *     Loads the ZPA Cloud configuration from local disk for customer apps.
 *     It checks if the local configuration file exists, and if not, attempts to load the encrypted configuration.
 *     If the encrypted configuration is not found, it loads the local configuration, encrypts it, and writes it to disk.
 *
 */
int zpath_load_cloud_config_from_local_disk_for_customer_apps(struct zcrypt_key *key) {
    char *jws_string = NULL;
    int ret = ZPATH_RESULT_ERR;
    size_t filelen = 0;

    if ( zpath_check_if_local_cloud_config_file_exists() == ZPATH_RESULT_NOT_FOUND ) {
        ZPATH_DEBUG_CLOUD_CONFIG("Local ZPA Cloud JSON config files NOT Found \n");
        return ZPATH_RESULT_NOT_FOUND;
    }

    // If Sarge is starting for first time, it would not have a valid encryption key
    // We just read the plain cloud config and return.
    if (key== NULL) {
        ret = zpath_load_local_zpa_cloud_config(ZPA_CLOUD_CONFIG_CUST_APPS_PLAIN_FILE);
        return ret;
    }

    // Attempt to load the encrypted ZPA Cloud configuration
    ret = zpath_load_encrypted_cloud_config(key, ZPA_CLOUD_CONFIG_CUST_APPS_CRYPT_FILE);
    if (ret == ZPATH_RESULT_NOT_FOUND) {
        // Load the local ZPA Cloud configuration
        ret = zpath_load_local_zpa_cloud_config(ZPA_CLOUD_CONFIG_CUST_APPS_PLAIN_FILE);
        if ( ret == ZPATH_RESULT_NO_ERROR ){
            jws_string = zpa_cloud_read_jws_from_file(ZPA_CLOUD_CONFIG_CUST_APPS_PLAIN_FILE, &filelen);
            // Write the encrypted configuration to disk
            ret = zpath_write_encrypted_cloud_config_file(key, jws_string, ZPA_CLOUD_CONFIG_CUST_APPS_CRYPT_FILE, filelen);
            // If the encrypted configuration is successfully written, remove the original file
            if ( ret == ZPATH_RESULT_NO_ERROR ) {
                unlink(ZPA_CLOUD_CONFIG_CUST_APPS_PLAIN_FILE);
            }
            zpath_free_cloud_objects(jws_string);
        }
    }
    return ret;
}

/**
 * zpath_load_cloud_config_from_api_server_for_customer_apps:
 *     Loads the ZPA cloud configuration from the API server response for customer apps.
 *     This function extracts the cloud configuration from the API response.
 *     If successful, it writes the encrypted cloud configuration to a file.
 *
 */
int zpath_load_cloud_config_from_api_server_for_customer_apps(struct zcrypt_key *key, struct evbuffer *body) {
    int ret = ZPATH_RESULT_NO_ERROR;

    if (body == NULL) {
        return ZPATH_RESULT_NOT_FOUND;
    }

    size_t len = evbuffer_get_length(body);

    char *buf = (char *)ZPA_CLOUD_CONFIG_CALLOC(len + 1);

    len = evbuffer_copyout(body, buf, len);
    buf[len] = '\0';

    if (len <= 0) {
        ZPATH_LOG(AL_WARNING, "Could not get extract cloud_config from api response: len = %ld", len);
        zpath_free_cloud_objects(buf);
        return ZPATH_RESULT_NOT_FOUND;
    }

    JSON_Value *jv = json_parse_string(&buf[0]);

    if (json_value_get_type(jv)!= JSONObject) {
        ZPATH_LOG(AL_WARNING, "Failed to validate JSON in api response for get enrollment details");
        if (jv) {
            json_value_free(jv);
        }
        zpath_free_cloud_objects(buf);
        return ZPATH_RESULT_NOT_FOUND;
    }

    JSON_Object *jo = json_value_get_object(jv);

    if (!jo) {
        ZPATH_LOG(AL_WARNING, "Failed to validate JSON in api response for get enrollment details");
        if (jv) {
            json_value_free(jv);
        }
        zpath_free_cloud_objects(buf);
        return ZPATH_RESULT_NOT_FOUND;
    }

    JSON_Value *cloud_config_val = json_object_get_value(jo, "cloud_config");
    char *cloud_config = json_serialize_to_string(cloud_config_val);
    if (cloud_config) {
        ZPATH_LOG(AL_DEBUG, "Cloud Config from get enrollment details api response ");
    } else {
        ZPATH_DEBUG_CLOUD_CONFIG("Cloud Config is NULL in get enrollment details api response");
        if (jv)
            json_value_free(jv);
        zpath_free_cloud_objects(buf);
        return ZPATH_RESULT_NOT_FOUND;
    }

    ret = zpa_cloud_config_verify_parse(ZPA_CLOUD_CONFIG_API_RESPONSE, cloud_config);

    if (ret!= ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_WARNING, "Failure: Verify and Parse ZPA Cloud JSON source: %s \n", ZPA_CLOUD_CONFIG_API_RESPONSE);
        zpath_free_cloud_objects(buf);
        json_free_serialized_string(cloud_config);
        return ret;
    }

    //If key is NULL, then do NOT encrypt the given cloud config buffer.
    if (key != NULL) {
        size_t data_len = strlen(cloud_config);

        ret = zpath_write_encrypted_cloud_config_file(key, (char *)cloud_config, ZPA_CLOUD_CONFIG_CUST_APPS_CRYPT_FILE, data_len);
        if ( ret == ZPATH_RESULT_ERR ) {
            if (jv) json_value_free(jv);
            zpath_free_cloud_objects(buf);
            if (cloud_config ) json_free_serialized_string(cloud_config);
            return ret;
        }
    }
    ZPATH_LOG(AL_NOTICE, "Successfully loaded config from API response ");

    if (jv) json_value_free(jv);
    zpath_free_cloud_objects(buf);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * [internal] Oauth Key Format - enrollmentAPI|CloudName|TenantGID
 */
int zpath_parse_oauth_key(const char *enrollment_key, char *tokens[MAX_ENROLLMENT_KEY_BLOCKS], int *token_cnt) {
    const char *start = NULL;
    const char *end = NULL;
    int i = 0;

    start = enrollment_key;
    while ((end = strchr(start, '|')) != NULL) {
        tokens[i] = ZPA_CLOUD_CONFIG_STRNDUP(start, (end - start));
        start = end + 1;
        i++;
    }
    *token_cnt = (i + 1);
    if (i < 1) {
        ZPATH_LOG(AL_ERROR, "OAuth Provisioning key details not available, please copy Provisioning key & restart");
        goto cleanup_err;
    }

    // Last Token - Tenant ID
    tokens[i] = ZPA_CLOUD_CONFIG_STRDUP(start);
    if (tokens[i] == NULL) {
        ZPATH_LOG(AL_ERROR, "OAuth Provisioning key doesn't have a cloud name, please copy Provisioning key & restart");
        goto cleanup_err;
    }

    ZPATH_DEBUG_CLOUD_CONFIG("Success: Reading the Oauth Provisioning details");
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    for (int ind = 0; ind < *token_cnt; ind++) {
        zpath_free_cloud_objects(tokens[ind]);
    }
    *token_cnt = 0;
    ZPATH_LOG(AL_ERROR, "Failure: Reading the OAuth Provision Key");
    return ZPATH_RESULT_ERR;
}

/* zpath_parse_provision_key
 * Parse the provided provision key into tokens
 * for reading Dist server details
 *
 * To be used by AppC, PSE and PCC
 */
int zpath_parse_provision_key(const char *enrollment_key, char *tokens[MAX_ENROLLMENT_KEY_BLOCKS], int *token_cnt) {
    const char *start = NULL;
    const char *end = NULL;
    int i = 0;

    start = enrollment_key;
    while ((end = strchr(start, '|')) != NULL) {
        tokens[i] = ZPA_CLOUD_CONFIG_STRNDUP(start, (end - start));
        start = end + 1;
        i++;
    }
    *token_cnt = (i + 1);
    if (i < 1) {
        ZPATH_LOG(AL_ERROR, "Provisioning key is not copied properly, please recopy and restart");
        goto cleanup_err;
    }
    // Last token after the final pipe
    tokens[i] = ZPA_CLOUD_CONFIG_STRDUP(start);
    if (tokens[i] == NULL) {
        ZPATH_LOG(AL_ERROR,
                  "Provisioning key does not begin with shard - most likely key is not copied properly, please recopy "
                  "and restart ");
        goto cleanup_err;
    }
    // Validate first 3 Parameters
    if (tokens[SHARD_KEY_INDEX]) {
        uint64_t val = strtol(tokens[SHARD_KEY_INDEX], NULL, 10);
        if (!val) {
            ZPATH_LOG(AL_ERROR,
                      "Provisioning key does not begin with shard - most likely key is not copied properly, please "
                      "recopy and restart ");
            goto cleanup_err;
        }
    }
    size_t api_len = strlen(tokens[API_NAME_KEY_INDEX]);
    if (api_len >= 100) {
        ZPATH_LOG(AL_ERROR,
                  "Provisioning key API too long - most likely key is not copied properly, please recopy and restart ");
        goto cleanup_err;
    }
    if (api_len < 3) {
        ZPATH_LOG(
                AL_ERROR,
                "Provisioning key API too short - most likely key is not copied properly, please recopy and restart ");
        goto cleanup_err;
    }

    // Validate Last 3 Parameters - For Cloud Config support
    ZPATH_DEBUG_CLOUD_CONFIG("Success: Reading the Provision Key");
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    for (int ind = 0; ind < *token_cnt; ind++) {
        zpath_free_cloud_objects(tokens[ind]);
    }
    *token_cnt = 0;
    ZPATH_LOG(AL_ERROR, "Failure: Reading the Provision Key");
    return ZPATH_RESULT_ERR;
}

/*
 * Retrieves the provisioning key details from the provided configuration key.
 *
 * This function parses the provisioning key into its constituent tokens and
 * extracts the API name and cloud name if available.
 *
 */
int zpath_get_oauth_cloud_details(const char* cfg_provisioning_key,
                                  char *api_name,
                                  char *cloud_name,
                                  int64_t *customer_gid) {
    char *tokens[MAX_ENROLLMENT_KEY_BLOCKS] = { NULL };
    int result = ZPATH_RESULT_ERR;
    int token_cnt = 0;
    char customer_gid_char[32];

    result = zpath_parse_oauth_key(cfg_provisioning_key, tokens, &token_cnt);

    if (result == ZPATH_RESULT_NO_ERROR) {
        if (api_name){
            snprintf(api_name, MAX_CLOUD_NAME_LEN,"%s", tokens[OAUTH_API_NAME_KEY_INDEX]);
        }

        if ((cloud_name) && (token_cnt > OAUTH_CLOUD_NAME_KEY_INDEX )) {
            if (zpath_is_flex_cloud(tokens[0])) {
                snprintf(cloud_name, MAX_CLOUD_NAME_LEN, "%s", ZPA_CLOUD_CONFIG_DEV_CLOUD_NAME);
            } else {
                snprintf(cloud_name, MAX_CLOUD_NAME_LEN, "%s", tokens[OAUTH_CLOUD_NAME_KEY_INDEX]);
            }
        }

        if (customer_gid && token_cnt > OAUTH_CUSTOMER_GID_INDEX) {
            snprintf(customer_gid_char, sizeof(customer_gid_char), "%s", tokens[OAUTH_CUSTOMER_GID_INDEX]);
            *customer_gid = strtoll(customer_gid_char, NULL, 10);
        }
    } else {
        ZPATH_LOG(AL_ERROR, "OAuth Provisioning key does not have valid data, please copy valid file and restart");
    }
    for (int i = 0; i < token_cnt; i++) {
        zpath_free_cloud_objects(tokens[i]);
    }
    return result;
}

/*
 * Retrieves the provisioning key details from the provided configuration key.
 *
 * OAuth Provision Key [EnrollmentAPI|CloudName|TenantGID]
 *
 * This function parses the provisioning key into its constituent tokens and
 * extracts the API name and cloud name if available.
 *
 */
int zpath_get_provisioning_key_details(const char *cfg_provisioning_key,
                                    char *api_name,
                                    char *cloud_name) {
    char *tokens[MAX_ENROLLMENT_KEY_BLOCKS] = { NULL };
    int result = ZPATH_RESULT_ERR;
    int token_cnt = 0;

    if (!cfg_provisioning_key) {
        ZPATH_LOG(AL_ERROR, "Provisioning key not available, please copy and restart ");
        return result;
    }

    result = zpath_parse_provision_key(cfg_provisioning_key, tokens, &token_cnt);

    if (result == ZPATH_RESULT_NO_ERROR) {
        if (api_name){
            snprintf(api_name, MAX_CLOUD_NAME_LEN,"%s", tokens[API_NAME_KEY_INDEX]);
        }

        if ((cloud_name) && (token_cnt > CLOUD_NAME_KEY_INDEX )) {
            if (zpath_is_flex_cloud(tokens[API_NAME_KEY_INDEX])) {
                snprintf(cloud_name, MAX_CLOUD_NAME_LEN,"%s", ZPA_CLOUD_CONFIG_DEV_CLOUD_NAME);
            } else {
                snprintf(cloud_name, MAX_CLOUD_NAME_LEN,"%s", tokens[CLOUD_NAME_KEY_INDEX]);
            }
        }
    } else {
        ZPATH_LOG(AL_ERROR, "Provisioning key does not have valid data, please copy valid file and restart");
    }
    for (int i = 0; i < token_cnt; i++) {
        zpath_free_cloud_objects(tokens[i]);
    }
    return result;
}

/**
 * zpath_load_cloud_config_for_customer_apps:
 *     Loads the cloud configuration for customer applications.
 *     It attempts to load the configuration from local disk, then from the API server,
 *     and finally uses the default configuration if both previous attempts fail.
 *
 */
int zpath_load_cloud_config_for_customer_apps(struct zcrypt_key *enc_key, char *api_name,
                                    struct evbuffer *evbuffer) {
    int result = ZPATH_RESULT_ERR;

    // Step 1: Attempt to load the cloud configuration from local disk
    result = zpath_load_cloud_config_from_local_disk_for_customer_apps(enc_key);

    if (result == ZPATH_RESULT_NO_ERROR) {
        ZPATH_DEBUG_CLOUD_CONFIG("Success: 1. Reading Cloud Config from Local Disk");
        g_zpa_config_source = ZPA_COUD_CONFIG_SOURCE_LOCAL_DISK;

    } else if (result == ZPATH_RESULT_NOT_FOUND) {
        // Step 2: Attempt to load the configuration from the API server
        result = zpath_load_cloud_config_from_api_server_for_customer_apps(enc_key, evbuffer);

        if(result == ZPATH_RESULT_NO_ERROR) {
            ZPATH_DEBUG_CLOUD_CONFIG("Success: 2. Downloading Cloud Config from API Server");
            g_zpa_config_source = ZPA_COUD_CONFIG_SOURCE_API_SERVER;

        } else if (result == ZPATH_RESULT_NOT_FOUND) {
            // Step 3: Use the default cloud configuration
            result = zpath_load_default_zpa_cloud_config();

            if(result == ZPATH_RESULT_NO_ERROR) {
                ZPATH_DEBUG_CLOUD_CONFIG( "Success: 3. Reading Default Cloud Config");
            }
        }
    } else {
        ZPATH_LOG(AL_ERROR, "Invalid local cloud configuration file, exiting");
    }
    if (api_name) {
        const struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_apiname(api_name);
        if ( (cloud_config) && (cloud_config->zpa_cloud_trusted_ca_certs_count > 0 )) {
            ZPATH_DEBUG_CLOUD_CONFIG("Success: Adding custom thirdparty CA Certs");
            fohh_add_ext_trusted_certs(cloud_config->zpa_cloud_trusted_ca_certs, cloud_config->zpa_cloud_trusted_ca_certs_count);
        }
    }
    return result;
}

int zpath_app_cloud_config_callback(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *cookie)
{

    zpath_debug_cb_printf_response(request_state, "ZPA Cloud Config:\n");
    if(g_zpa_config_source) {
        zpath_debug_cb_printf_response(request_state, "%-30s: SUCCESS\n","Config Status");
        zpath_debug_cb_printf_response(request_state, "%-30s: %s\n","Active Cloud Name", ZPATH_LOCAL_CLOUD_NAME);
        switch (g_zpa_config_source) {
            case 1:
                zpath_debug_cb_printf_response(request_state, "%-30s: Default Config\n", "Config Source");
                break;
            case 2:
                zpath_debug_cb_printf_response(request_state, "%-30s: Local Disk\n","Config Source");
                break;
            case 3:
                zpath_debug_cb_printf_response(request_state, "%-30s: Distribution Server\n", "Config Source");
                break;
            case 4:
                zpath_debug_cb_printf_response(request_state, "%-30s: API Server\n", "Config Source");
                break;
            default:
                zpath_debug_cb_printf_response(request_state, "%-30s: NONE\n","Config Status");
        }
    } else {
        zpath_debug_cb_printf_response(request_state, "%-30s: Failed\n","Config Status");
        zpath_debug_cb_printf_response(request_state, "%-30s: NONE\n","Config Status");
        return ZPATH_RESULT_NO_ERROR;
    }
    const struct zpath_cloud_config *cloud_config;
    if( (query_value_count==1) &&
        (query_values[0])) {
        cloud_config=zpath_get_cloud_config_from_name(query_values[0]);
    }
    else{
        cloud_config=zpath_get_cloud_config_from_name(ZPATH_LOCAL_CLOUD_NAME);
    }
    if(!cloud_config) {
        zpath_debug_cb_printf_response(request_state, "Cloud Config NOT found for %s\n", query_values[0]);
        return ZPATH_RESULT_NO_ERROR;
    }
    zpath_debug_cb_printf_response(request_state,"\n%-30s: %s\n","Cloud Name", cloud_config->cloud_name);
    zpath_debug_cb_printf_response(request_state,"%-30s: %s\n","PBroker Broker Domain",cloud_config->pbroker_broker_domain);
    zpath_debug_cb_printf_response(request_state,"%-30s: %d\n\n","Cloud Type",cloud_config->cloud_type);
    struct zpath_exporter_cloud_config *exp_obj = zpath_get_exporter_config(cloud_config->cloud_name);
    if(exp_obj) {
        zpath_debug_cb_printf_response(request_state,"%s: \n","Exporter Config");
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Auth Domain", exp_obj->auth_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","SAML Auth Domain", exp_obj->saml_auth_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Broker Domain",exp_obj->broker_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Broker OT Domain", exp_obj->broker_ot_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","UATU Domain", exp_obj->uatu_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Cookie Prefix", exp_obj->cookie_prefix);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Google Posture Domain", exp_obj->google_posture_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Portal Hostname", exp_obj->portal_hostname);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","PRA Portal Hostname", exp_obj->pra_portal_hostname);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Portal Version", exp_obj->portal_version);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n\n","PRA Service Portal Hostname", exp_obj->pra_service_portal_hostname);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n\n","Unified Portal TLD appln suffix", exp_obj->unified_portal_tld_appln_name);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n\n","Unified Portal PRA TLD appln suffix", exp_obj->unified_portal_pra_tld_appln_name);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n\n","Managed Application TLD appln suffix", exp_obj->managed_app_tld_appln_name);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n\n","PRA Service Portal Hostname", exp_obj->pra_cms_hostname);
    }
    zpath_debug_cb_printf_response(request_state,"%s: \n","Sarge Config");
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Dist Hostname",cloud_config->sarge->dist_hostname);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Dist ProxyName", cloud_config->sarge->dist_proxyname);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Dist Path", cloud_config->sarge->dist_path);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Dist Develop Path", cloud_config->sarge->dist_path_develop);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n","FIPS Status", cloud_config->sarge->require_fips);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n","Allow Develop Images", cloud_config->sarge->develop);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n","Proxy Enabled", cloud_config->sarge->proxy_enabled);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Stack Upload Host", cloud_config->sarge->stack_upload_host);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Stack Upload Path", cloud_config->sarge->stack_upload_path);
    zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Filename Selector", cloud_config->sarge->filename_indicator);

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","Cloud Root Certs");
    for(int c=0;c<cloud_config->zpa_cloud_root_certs_count;c++){
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %ld\n","Cert Time",cloud_config->zpa_cloud_root_certs[c].root_cert_time);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n\n","Drop FIPS",cloud_config->zpa_cloud_root_certs[c].cert_can_drop_fips);
    }

    zpath_debug_cb_printf_response(request_state,"\n%-30s: %zu\n","Custom trusted certs added",fohh_get_ext_trusted_certs_count());

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","SNI APIs");
    for(int c=0;c<cloud_config->zpn_apis_count;c++){
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","API",cloud_config->zpn_apis[c].api);
    }

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","AuthSP List");
    for(int c=0;c<cloud_config->zpn_authsp_count;c++){
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","AuthSP",cloud_config->zpn_authsps[c].authsp);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Proxy",cloud_config->zpn_authsps[c].proxy);
    }

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","SamlSP List");
    for(int c=0;c<cloud_config->zpn_samlsp_count;c++){
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","SamlSP",cloud_config->zpn_samlsps[c].samlsp);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Proxy",cloud_config->zpn_samlsps[c].proxy);
    }

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","SAML Audience List");
    for(int c=0;c<cloud_config->saml_audience_list_count;c++){
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","SAML Endpoint",cloud_config->saml_audience_list[c]);
    }

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","Misc Domains List");
    for(int c=0;c<cloud_config->zpath_misc_count;c++) {
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Domain",cloud_config->zpath_misc[c].domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %s\n","Resolve Domain",cloud_config->zpath_misc[c].resolve_domain);
    }

    zpath_debug_cb_printf_response(request_state,"\n%-30s: \n","Authenticated SNI List");
    for(int c=0;c<cloud_config->zpath_authenticated_sni_count;c++) {
        zpath_debug_cb_printf_response(request_state,"%-30s: %s\n","Broker Domain",cloud_config->zpath_authenticated_sni[c].broker_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n","Cloud Suffix",cloud_config->zpath_authenticated_sni[c].broker_domain_cloud_suffix);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n","Wildcard Suffix",cloud_config->zpath_authenticated_sni[c].broker_domain_wildcard_prefix);
        zpath_debug_cb_printf_response(request_state,"%-30s: %s\n","Client Domain",cloud_config->zpath_authenticated_sni[c].client_domain);
        zpath_debug_cb_printf_response(request_state,"\t%-22s: %d\n","Wildcard Prefix",cloud_config->zpath_authenticated_sni[c].client_domain_wildcard_prefix);
        zpath_debug_cb_printf_response(request_state,"%-30s: %d\n","Client Type",cloud_config->zpath_authenticated_sni[c].client_type);
        zpath_debug_cb_printf_response(request_state,"%-30s: %s\n","Resolve Domain",cloud_config->zpath_authenticated_sni[c].resolve_domain);
    }
    return ZPATH_RESULT_NO_ERROR;
}

/**
 * zpath_cloud_config_details
 * Initializes the Cloud Config ITASCURL command.
 */
static void zpath_cloud_config_details(void) {
    int result = 0;

    result = zpath_debug_add_safe_read_command("Print the Cloud Configuration details",
                                     "/app/cloud_config",
                                     zpath_app_cloud_config_callback,
                                     NULL,
                                     "cloud_name", "cloud_name",
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "couldn't add /app/cloud_config");
    }
}

/* zpath_init_cloud_config
 * Initializes the cloud config root cert and ITASCURL commands
 *
 */
void zpath_init_cloud_config(){
    zpath_load_cloud_config_root();
    zpath_cloud_config_details();
}
