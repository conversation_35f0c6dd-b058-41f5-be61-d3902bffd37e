/*
 * zpath_log_store.h. Copyright (C) 2015 Zscaler, Inc. All Rights Reserved.
 */

#ifndef __ZPATH_LOG_STORE_H__
#define __ZPATH_LOG_STORE_H__

/* WARNING: When adding fields, update max and service_endpoint_names ! */
enum zpath_customer_log_type {
    zpath_customer_log_type_stats = 0,
    zpath_customer_log_type_dns_raw = 1,
    zpath_customer_log_type_dns_aggregate = 2,
    zpath_customer_log_type_zpn_auth = 3,
    zpath_customer_log_type_zpn_ast_auth = 4,
    zpath_customer_log_type_zpn_health = 5,
    zpath_customer_log_type_zpn_transaction = 6,
    zpath_customer_log_type_zpn_clientless_auth = 7,
    zpath_customer_log_type_zpn_clientless_transaction = 8,
    zpath_customer_log_type_zpn_sys_auth = 9,
    zpath_customer_log_type_ci_stats = 10,
    zpath_customer_log_type_comprehensive_stats = 11,
    zpath_customer_log_type_zpn_http_inspection = 12,
    zpath_customer_log_type_admin_probe = 13,
    zpath_customer_log_type_pb_comprehensive_stats = 14,
    zpath_customer_log_type_zpn_auth_state = 15,
    zpath_customer_log_type_zpn_event = 16,
    zpath_customer_log_type_zpn_csp = 17,
    zpath_customer_log_type_zpn_pra_session = 18,
    zpath_customer_log_type_zpn_adp = 19, /* Application Domain Protocol Log */
    zpath_customer_log_type_zpn_sc_auth = 20,
    zpath_customer_log_type_sc_comprehensive_stats = 21,
    zpath_customer_log_type_zpn_ptag = 22, /* Application Protocol Tagging Log */
    zpath_customer_log_type_zpn_http_api_inspection = 23, /* API Inspection stream */
    zpath_customer_log_type_zpn_pra_session_email = 24,
    zpath_customer_log_type_zpn_email = 25,
    zpath_customer_log_type_zia_health = 26, // health for ZIA hipsec tunnels and ZVPN, EAS,ODC,Extranet
    zpath_customer_log_type_np_comprehensive_stats = 27,
    zpath_customer_log_type_zpn_app_inspection = 28,
    zpath_customer_log_type_zpn_krb_inspection = 29,
    zpath_customer_log_type_zpn_ldap_inspection = 30,
    zpath_customer_log_type_zpn_smb_inspection = 31,
    zpath_customer_log_type_zpn_managed_browser_payload = 32,
    zpath_customer_log_type_zpn_np_nw_comp_stats = 33,
    zpath_customer_log_type_max /* Add log types above this */
};
/* Log_type_max is the count of log types. i.e. including 0 */
#define ZPATH_CUSTOMER_LOG_TYPE_MAX zpath_customer_log_type_max

#define ZPATH_MAX_LOG_TYPES          100
#define ZPATH_MAX_PROPERTY_KEY_VALUE 100

/*
 * zpath_log_store_queue is a handle used for queueing logs in zpath.
 *
 * They can be retrieved/indexed by log_store_id.
 */
struct zpath_log_store_queue;

struct zpath_log_store {                                          /* _ARGO: object_definition */
    int64_t gid;                                                   /* _ARGO: integer, index, key */
    int64_t customer_gid;                                          /* _ARGO: integer */
    char *store_type;                                              /* _ARGO: string */
    char *name;                                                    /* _ARGO: string */
    char *log_type[ZPATH_MAX_LOG_TYPES];                           /* _ARGO: string */
    int log_type_count;                                            /* _ARGO: quiet, integer, count: log_type */
    int32_t retention_period;                                      /* _ARGO: integer */
    int32_t start_time;                                            /* _ARGO: integer */
    int32_t end_time;                                              /* _ARGO: integer */
    char *property_key[ZPATH_MAX_PROPERTY_KEY_VALUE];              /* _ARGO: string */
    int property_key_count;                                        /* _ARGO: quiet, integer, count: property_key */
    char *property_value[ZPATH_MAX_PROPERTY_KEY_VALUE];            /* _ARGO: string */
    int property_value_count;                                      /* _ARGO: quiet, integer, count: property_value */
    char *url;                                                     /* _ARGO: string */
    int8_t provisioning;                                           /* _ARGO: integer */
    int64_t sequence;                                              /* _ARGO: integer, sequence */
    int32_t modified_time;                                         /* _ARGO: integer */
    int32_t creation_time;                                         /* _ARGO: integer */
    int64_t modifiedby_userid;                                     /* _ARGO: integer */
    int8_t deleted;                                                /* _ARGO: integer, deleted */
    int64_t request_id;                                            /* _ARGO: integer, nodb, reqid */

    /* Bit set with parsed out log_types set, based on types from
     * zpath_customer_log_type */
    int32_t log_type_bitset;
};

/* Argo description for zpath_log_store state. */
extern struct argo_structure_description *zpath_log_store_description;

/*
 * Initialize and retrieve local configuration information for
 * accessing zpath_log_store.
 */
int zpath_log_store_init(void);

/*
 * Get the zpath_log_store structure for the ID passed in. This
 * retrieves DB state- it does not return a handle useful for logging.
 *
 * Returns:
 *
 * ZPATH_RESULT_NO_ERROR (and *log_store is valid) if found.
 * ZPATH_RESULT_NOT_FOUND if not found.
 * ZPATH_RESULT_ASYNCHRONOUS if being queried asynchronously.
 * ZPATH_RESULT_NOT_READY if requested DB is not ready.
 *
 * Caller must call zpath_log_store_release on the structure
 * returned. (eventually)
 */
int zpath_log_store_get(int64_t log_store_gid,
                        struct zpath_log_store **log_store,
                        wally_response_callback_f callback_f,
                        void *callback_cookie,
                        int64_t callback_id);

/*
 * Get the info from zpath_log_store structure for the ID passed in.
 *
 * Caller is responsible to allocate memory for url
 *
 * This will walk up the configuration inheritance tree looking for
 * log configuration.
 *
 * ANY log configuration for an individual customer causes the lookup
 * to stop. (i.e. no 'partial inheritance' of log configuration)
 */
int zpath_log_store_get_info(int64_t log_store_gid,
                             char *url,
                             size_t max_len,
                             char *logtype,
                             int *match,
                             wally_response_callback_f callback_f,
                             void *callback_cookie,
                             int64_t callback_id);


/*
 * Release the zpath_log_store structure...
 */
void zpath_log_store_release(struct zpath_log_store *log_store);


/*
 * Get a log_store queueing handle. This is always a synchronous call,
 * even if the queue won't necessarily be able to send data yet.
 *
 * The value returned will never become invalid- it can be held
 * forever. This routine is contentious, so should be called somewhat
 * sparingly.
 */
struct zpath_log_store_queue *zpath_log_store_get_queue(int64_t log_store_gid);

/*
 * log_store queueing routine
 */
int zpath_log_store_log_struct(struct zpath_log_store_queue *queue,
                               const char *log_name,
                               struct argo_structure_description *description,
                               void *data);


#endif /* __ZPATH_LOG_STORE_H__ */
