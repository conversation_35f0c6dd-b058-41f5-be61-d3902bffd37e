/*
 * zpath_capability_util.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPATH_CAPABILITY_UTIL_H_
#define _ZPATH_CAPABILITY_UTIL_H_

/*Enum representing the supported capability selection modes. */
/**
 * zpath_capability_mode_t defines how Linux capabilities are configured
 * for the connector container.
 *
 * Capability modes:
 * -----------------
 * 1. CAP_MODE_MINIMAL:
 *    - Enables only cap_net_raw.
 *    - Recommended for high security environments.
 *    - May restrict some connector functionality.
 *    - Use this mode if compatibility is not a concern and minimal privileges are desired.
 *
 * 2. CAP_MODE_FULL:
 *    - Enables the full set of capabilities needed by openshift or k8s deployment.
 *    - Use this mode if the connector needs all its historical capabilities for proper functioning.
 *    - Default when no mode is set and its k8s and openshift container.

 * 3. CAP_MODE_CUSTOM:
 *    - Allows specifying an explicit list of capabilities via ZPA_CUSTOM_CAPABILITIES.
 *    - Offers flexibility while maintaining security.
 *    - Use this mode for fine-tuned security.
 *
 * 4. CAP_MODE_DEFAULT:
 *    - Maps to historical set of capabilities needed for container environments spwaned from docker for proper functioning.
 *    - Default for cases where no mode is explicitly set and its non k8s and openshift container.
 */
typedef enum {
    CAP_MODE_MINIMAL,
    CAP_MODE_FULL,
    CAP_MODE_CUSTOM,
    CAP_MODE_DEFAULT
} zpath_capability_mode_t;

/*
 * Returns the current capability mode based on the environment variable
 * ZPA_CAPABILITY_MODE. If not set, defaults to FULL. If invalid defaults to minimal.
 */
zpath_capability_mode_t zpath_get_capability_mode();

/*
 * Parses the ZPA_CUSTOM_CAPABILITIES environment variable, validates and returns
 * a filtered list of allowed custom capabilities. If the variable is empty or invalid,
 * defaults to the minimal capability set.
 *
 * Parameters:
 * - count: Pointer to store the number of returned capabilities.
 *
 * Returns:
 * - An array of validated capability strings (e.g., "cap_net_raw").
 */
const char **zpath_get_custom_capabilities(int *count);

/*
 * Determines the effective capability list based on the selected mode (env driven).
 *
 * Parameters:
 * - count: Pointer to store the number of capabilities returned.
 *
 * Returns:
 * - Array of capability strings according to the resolved mode.
 */
const char * const *zpath_get_final_capabilities(int *count);

/*
 * Builds a comma-separated string of capabilities from the input list, appending "+pe"
 * at the end. If in container mode, skips restricted caps (like cap_sys_boot).
 *
 * Parameters:
 * - cap_list: List of capabilities.
 * - count: Number of capabilities in the list.
 * - cap_string: Output buffer to hold the formatted string.
 * - cap_string_size: Size of the output buffer.
 * - is_container_env: Flag to indicate container environment.
 */
void zpath_build_cap_string(const char * const *cap_list, int count, char *cap_string, size_t cap_string_size, int is_container_env);

const char *zpath_mode_to_string(zpath_capability_mode_t mode);
void zpath_log_final_caps(zpath_capability_mode_t mode, const char * const *cap_list, int cap_count);
int is_openshift_environment(void);
int is_kubernetes_environment(void);

#endif /* _ZPATH_CAPABILITY_UTIL_H_ */
