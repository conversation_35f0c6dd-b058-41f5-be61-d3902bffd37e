/*
 * Simple common zpath_lib definitions. (Basically, return codes... All taken from argo...)
 */

#ifndef _ZPATH_LIB_H_
#define _ZPATH_LIB_H_

#include "zpath_misc/zpath_misc.h"
#include "wally/wally.h"

#define ZPATH_GID_MASK_CUSTOMER ((int64_t)0xffffffffc0000000ll)
#define ZPATH_GID_GET_CUSTOMER_GID(x)  (x & ZPATH_GID_MASK_CUSTOMER)
#define ZPATH_GID_ROOT_CUSTOMER (0x0100000000000000ll)
#define ZPATH_GID_GET_SHARD(x) (((uint64_t )x) >> 56)

extern struct zpath_allocator zpath_lib_allocator;
#define ZLIB_MALLOC(x) zpath_malloc(&zpath_lib_allocator, x, __LINE__, __FILE__)
#define ZLIB_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ZLIB_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ZLIB_CALLOC(x) zpath_calloc(&zpath_lib_allocator, x, __LINE__, __FILE__)
#define ZLIB_STRDUP(x, y) zpath_strdup(&zpath_lib_allocator, x, y, __LINE__, __FILE__)

#define ZPATH_RESULT_NO_ERROR          WALLY_RESULT_NO_ERROR          /* AKA success */
#define ZPATH_RESULT_ERR               WALLY_RESULT_ERR               /* Generic error, when none other are appropriate */
#define ZPATH_RESULT_NOT_FOUND         WALLY_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define ZPATH_RESULT_NO_MEMORY         WALLY_RESULT_NO_MEMORY         /* Could not allocate memory */
#define ZPATH_RESULT_CANT_WRITE        WALLY_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define ZPATH_RESULT_ERR_TOO_LARGE     WALLY_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define ZPATH_RESULT_BAD_ARGUMENT      WALLY_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define ZPATH_RESULT_INSUFFICIENT_DATA WALLY_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define ZPATH_RESULT_NOT_IMPLEMENTED   WALLY_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define ZPATH_RESULT_BAD_DATA          WALLY_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define ZPATH_RESULT_WOULD_BLOCK       WALLY_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define ZPATH_RESULT_BAD_STATE         WALLY_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define ZPATH_RESULT_INCOMPLETE        WALLY_RESULT_INCOMPLETE
#define ZPATH_RESULT_ASYNCHRONOUS      WALLY_RESULT_ASYNCHRONOUS      /* Asynchronous means the request will still complete, but at a later time, with a
                                                                       * callback. As opposed to would_block, which didn't do the job, nor will
                                                                       * it. */
#define ZPATH_RESULT_EXCESS_DYN_FIELDS WALLY_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define ZPATH_RESULT_NOT_READY         WALLY_RESULT_NOT_READY         /* Requested DB is not ready. */
#define ZPATH_RESULT_VALIDATION_ERR    WALLY_RESULT_VALIDATION_ERR    /* Indicates non-critical yet major failure during validation of some state/args/params at runtime */
#define ZPATH_RESULT_MAX               ZPATH_RESULT_NOT_READY

#define ZPATH_SARGE_FILENAME_UPDATER   "updater.version"              /* File name to which sarge dumps its runtime version */

/* broker authenticated proxy client types */
enum zpath_broker_proxy_ssl_client_type {
    zpath_broker_proxy_ssl_client_type_unknown,
    zpath_broker_proxy_ssl_client_type_connector,
    zpath_broker_proxy_ssl_client_type_pbroker,
    zpath_broker_proxy_ssl_client_type_sitec,
};

/* Wally sync pause registration */
extern void (*zpath_lib_sync_pause_regn_cb)(struct wally *wally, struct wally_table *table);
extern void zpath_lib_sync_pause_register_table(struct wally *wally, struct wally_table *table);

extern const char *zpath_result_string(int result);
extern const char* zpath_broker_proxy_ssl_client_type_str(enum zpath_broker_proxy_ssl_client_type client_type);

extern struct zpath_interlock exit_lock;
void zpath_app_init_fohh_cipher_configuration(int64_t instance_gid, int64_t customer_id, int64_t client_default);

/* broker termination codes */
enum zpath_termination_code {
    zpath_tc_heartbeat_exceeded = 0,
    zpath_tc_exit,
    zpath_tc_lib_app_shutdown,
    zpath_tc_lib_assert,
    zpath_tc_lib_abort,
    zpath_tc_lib_fortify,
    zpath_tc_lib_stack_smash,
    zpath_tc_lib_segfault,
    zpath_tc_lib_ungraceful_shutdown,
    zpath_tc_invalid       //Keep this as last value
};

extern int (*zpath_termination_handler[])(void);
extern const char * zpath_get_termination_code_string(enum zpath_termination_code tc);

/* api host name to root certificate to cloud name mapping */
struct zpn_apimap {
    char *api;                          /* _ARGO: string */
    char *root;                        /* _ARGO: string */
    time_t root_time;                  /* _ARGO: integer */
    char *cloud;                        /* _ARGO: string */
    int can_drop_fips;                  /* _ARGO: integer */
};

struct zpn_samlsp_map {
    char *samlsp;                       /* _ARGO: string */
    char *proxy;                        /* _ARGO: string */
};

struct zpn_authsp_map {
    char *authsp;                       /* _ARGO: string */
    char *proxy;                        /* _ARGO: string */
};

struct zpath_misc_domain_map {
    char *domain;                       /* _ARGO: string */
    char *resolve_domain;               /* _ARGO: string */
};

struct zpath_authenticated_sni_map {
    char *client_domain;                /* _ARGO: string */
    int client_domain_wildcard_prefix;  /* _ARGO: integer */
    char *broker_domain;                /* _ARGO: string */
    int broker_domain_wildcard_prefix;  /* _ARGO: integer */
    int broker_domain_cloud_suffix;     /* _ARGO: integer */
    char *resolve_domain;               /* _ARGO: string */
    enum zpath_broker_proxy_ssl_client_type client_type;    /* _ARGO: integer */
};

/* double-checked locking
 * initialization attempted only once */
#define ZPATH_COMPONENT_ONETIME_INIT_CHECK \
{ \
    static int comp_initialized = 0; \
    static pthread_mutex_t comp_init_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER; \
    if (comp_initialized) { \
        return ZPATH_RESULT_NO_ERROR;\
    } \
    pthread_mutex_lock(&comp_init_lock); \
    if (comp_initialized) { \
        pthread_mutex_unlock(&comp_init_lock); \
        return ZPATH_RESULT_NO_ERROR; \
    } else {\
        comp_initialized = 1; \
        pthread_mutex_unlock(&comp_init_lock);\
    }\
}

#endif /* _ZPATH_LIB_H_ */
