
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "fohh/fohh.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_instance.h"

void (*zpath_lib_sync_pause_regn_cb)(struct wally *wally, struct wally_table *table) = NULL;

/*
 * zpath_lib_sync_pause_register_table
 *  If a table needs to be enrolled as pausable; this API has to be called.
 *  It has to be invoked during table initialization phase.
 */
void zpath_lib_sync_pause_register_table(struct wally *wally, struct wally_table *table)
{
    if (zpath_lib_sync_pause_regn_cb) {
        zpath_lib_sync_pause_regn_cb(wally, table);
    }
}

struct zpath_allocator zpath_lib_allocator = ZPATH_ALLOCATOR_INIT("zpath_lib");
struct zpath_interlock exit_lock;

const char *zpath_result_strings[] = {
    [ZPATH_RESULT_NO_ERROR] = "ZPATH_RESULT_NO_ERROR",
    [ZPATH_RESULT_ERR] = "ZPATH_RESULT_ERR",
    [ZPATH_RESULT_NOT_FOUND] = "ZPATH_RESULT_NOT_FOUND",
    [ZPATH_RESULT_NO_MEMORY] = "ZPATH_RESULT_NO_MEMORY",
    [ZPATH_RESULT_CANT_WRITE] = "ZPATH_RESULT_CANT_WRITE",
    [ZPATH_RESULT_ERR_TOO_LARGE] = "ZPATH_RESULT_ERR_TOO_LARGE",
    [ZPATH_RESULT_BAD_ARGUMENT] = "ZPATH_RESULT_BAD_ARGUMENT",
    [ZPATH_RESULT_INSUFFICIENT_DATA] = "ZPATH_RESULT_INSUFFICIENT_DATA",
    [ZPATH_RESULT_NOT_IMPLEMENTED] = "ZPATH_RESULT_NOT_IMPLEMENTED",
    [ZPATH_RESULT_BAD_DATA] = "ZPATH_RESULT_BAD_DATA",
    [ZPATH_RESULT_WOULD_BLOCK] = "ZPATH_RESULT_WOULD_BLOCK",
    [ZPATH_RESULT_BAD_STATE] = "ZPATH_RESULT_BAD_STATE",
    [ZPATH_RESULT_INCOMPLETE] = "ZPATH_RESULT_INCOMPLETE",
    [ZPATH_RESULT_ASYNCHRONOUS] = "ZPATH_RESULT_ASYNCHRONOUS",
    [ZPATH_RESULT_NOT_READY] = "ZPATH_RESULT_NOT_READY",
    [ZPATH_RESULT_EXCESS_DYN_FIELDS] = "ZPATH_RESULT_EXCESS_DYN_FIELDS",
};

const char *zpath_termination_code_strings[] = {
    [zpath_tc_heartbeat_exceeded] = "zpath_tc_heartbeat_exceeded",
    [zpath_tc_exit] = "zpath_tc_exit",
    [zpath_tc_lib_app_shutdown] = "zpath_tc_lib_app_shutdown",
    [zpath_tc_lib_assert] = "zpath_tc_lib_assert",
    [zpath_tc_lib_abort] = "zpath_tc_lib_abort",
    [zpath_tc_lib_fortify] = "zpath_tc_lib_fortify",
    [zpath_tc_lib_stack_smash] = "zpath_tc_lib_stack_smash",
    [zpath_tc_lib_segfault] = "zpath_tc_lib_segfault",
    [zpath_tc_lib_ungraceful_shutdown] = "zpath_tc_lib_ungraceful_shutdown",
    [zpath_tc_invalid] = "zpath_tc_invalid",
};

const char *zpath_broker_proxy_ssl_client_type_strings[] = {
    [zpath_broker_proxy_ssl_client_type_unknown] = "UNKNOWN",
    [zpath_broker_proxy_ssl_client_type_connector] = "APP CONNECTOR",
    [zpath_broker_proxy_ssl_client_type_pbroker] = "PSE",
    [zpath_broker_proxy_ssl_client_type_sitec] = "PCC",
};

static struct zpath_config_override_desc zpath_config_override_desc_fohh_client_cipher_suite = {
        .key                = CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE,
        .desc               = "Cipher Suite Selection for ITASCA Server application",
        .details            = "Configures the Ciphersuite list to be used by server applications\n"
                              "The value represents the index of the server cipher suite list array\n"
                              "default: 0, first cipher suite list",
        .val_type           = config_type_int,
        .component_types    = config_component_wally | config_component_broker | config_component_pbroker | config_component_appc | config_component_slogger | config_component_cdispatcher | config_component_ipars | config_component_l4proxy | config_component_exporter | config_component_ot_broker | config_component_ot_exporter | config_component_sitec,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_MIN,
        .int_range_hi       = CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_MAX,
        .int_default        = CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_DEFAULT,
        .feature_group      = FEATURE_GROUP_FOHH,
        .value_traits       = config_value_traits_normal
};

static struct zpath_config_override_desc zpath_config_override_desc_fohh_server_cipher_suite = {
        .key                = CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE,
        .desc               = "Cipher Suite Selection for ITASCA Client applications",
        .details            = "Configures the Ciphersuite list to be used by client applications\n"
                              "The value represents the index of the client cipher suite list array\n"
                              "default: 0, first cipher suite list",
        .val_type           = config_type_int,
        .component_types    = config_component_wally | config_component_broker | config_component_pbroker | config_component_appc | config_component_slogger | config_component_cdispatcher | config_component_ipars | config_component_l4proxy | config_component_ot_broker | config_component_sitec,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_MIN,
        .int_range_hi       = CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_MAX,
        .int_default        = CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_DEFAULT,
        .feature_group      = FEATURE_GROUP_FOHH,
        .value_traits       = config_value_traits_normal
};

static void zpath_app_set_fohh_server_cipher_configuration(int64_t instance_gid, int64_t customer_id, int64_t client_default);
static void zpath_app_set_fohh_client_cipher_configuration(int64_t instance_gid, int64_t customer_id, int64_t client_default);

int64_t g_tmp_cipher_server_value=0;
int64_t g_tmp_cipher_client_value=0;

static struct zpath_config_override_desc zpath_config_override_desc_role_based_access_control = {
        .key                = CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL,
        .desc               = "Defines whether role-based access controls has to be in-force when running commands",
        .details            = "0 indicates disabled; 1 indicates enabled.\n"
                              "default: 0, disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_wally | config_component_broker | config_component_ot_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_MIN,
        .int_range_hi       = CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_MAX,
        .int_default        = CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_DEFAULT,
        .feature_group      = FEATURE_GROUP_RBAC,
        .value_traits       = config_value_traits_feature_enablement
};


static struct zpath_config_override_desc zpath_config_wally_client_incompatible_version_check_override_descriptions = {
        .key                = WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK,
        .desc               = "feature flag to check if zpath wally client verion is compatible or not with server",
        .details            = "-1: default not configured\n"
                              "0: disabled, Wally Client version Check for compatibility is disabled\n"
                              "1: enabled, Wally Client version check for compatibility is enabled\n"
                              "default: -1/not configured",
        .val_type           = config_type_int,
        .component_types    = config_component_wally | config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
        .int_range_lo       = WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK_DEFAULT,
        .int_range_hi       = WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK_ENABLED,
        .int_default        = WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK
};

int64_t g_tmp_wally_client_incompatible_version_check = WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK_DEFAULT;

const char * zpath_get_termination_code_string(enum zpath_termination_code tc)
{
    if ((tc >zpath_tc_invalid) ||  !zpath_termination_code_strings[tc])
        return "";
    return zpath_termination_code_strings[tc];
}

#define CAN_DROP_FIPS 1
#define CANNOT_DROP_FIPS 0

extern const char *zpath_result_string(int result)
{
    if (result >= (sizeof(zpath_result_strings) / sizeof (const char *))) return "INVALID_RESULT";
    if (result < 0) return "INVALID_RESULT";
    if (zpath_result_strings[result] == NULL) return "INVALID_RESULT";
    return zpath_result_strings[result];
}

const char* zpath_broker_proxy_ssl_client_type_str(enum zpath_broker_proxy_ssl_client_type client_type)
{
    if (client_type >= (sizeof(zpath_broker_proxy_ssl_client_type_strings) / sizeof (const char *))) return "INVALID";
    if (client_type < 0) return "INVALID";
    if (zpath_broker_proxy_ssl_client_type_strings[client_type] == NULL) return "INVALID";
    return zpath_broker_proxy_ssl_client_type_strings[client_type];

}

void zpath_app_set_role_based_access_control_config(int64_t instance_gid)
{
    zpath_config_override_monitor_int(CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL,
                                      &zpath_rbac_enabled,
                                      NULL,
                                      CONFIG_FEATURE_ROLE_BASED_ACCESS_CONTROL_DEFAULT,
                                      instance_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
    ZPATH_DEBUG_CURL_CMD("command-listener: Role-based access control: %s: %s",
                            zpath_rbac_supported ? "supported" : "not-supported",
                            zpath_rbac_enabled ? "enabled" : "disabled");
}

/* role-based access overrides registration and initialization */
void zpath_app_role_based_access_control_overrides_init(int64_t instance_gid)
{
    /* do not venture into the overrides if the component itself doesnt support it. */
    if (!zpath_rbac_supported) {
        return;
    }

    int res = ZPATH_RESULT_NO_ERROR;
    res = zpath_config_override_desc_register(&zpath_config_override_desc_role_based_access_control);
    if (res) {
        ZPATH_LOG(AL_ERROR, "command-listener: Unable to register role based access control override for key: %s, err: %s",
                            zpath_config_override_desc_role_based_access_control.key, zpath_result_string(res));
    }

    //Now, setup the overrides and update the global value, if needed.
    zpath_app_set_role_based_access_control_config(instance_gid);
}

/* FOHH client and server cipher suites overrides registration and initialization */
void zpath_app_fohh_cipher_suite_overrides_init(int64_t instance_gid, int64_t customer_id, int64_t client_default)
{
    int res = ZPATH_RESULT_NO_ERROR;
    res = zpath_config_override_desc_register(&zpath_config_override_desc_fohh_client_cipher_suite);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Unable to register fohh config override for key: %s, err: %s",
                            zpath_config_override_desc_fohh_client_cipher_suite.key, zpath_result_string(res));
    }
    // Override the default only for client cipher suite.
    zpath_config_override_desc_fohh_server_cipher_suite.int_default = client_default;
    res = zpath_config_override_desc_register(&zpath_config_override_desc_fohh_server_cipher_suite);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Unable to register fohh config override for key: %s, err: %s",
                            zpath_config_override_desc_fohh_server_cipher_suite.key, zpath_result_string(res));
    }
    zpath_app_set_fohh_server_cipher_configuration(instance_gid, customer_id, client_default );
    zpath_app_set_fohh_client_cipher_configuration(instance_gid, customer_id, client_default );
}

/* wally client compatibilty check feature config overrides registration and initialization */
void zpath_app_wally_client_incompatible_version_check_config_overrides_init(int64_t instance_gid)
{
    int res = ZPATH_RESULT_NO_ERROR;

    res = zpath_config_override_desc_register(&zpath_config_wally_client_incompatible_version_check_override_descriptions);
    if (res) {
        ZPATH_LOG(AL_ERROR, "command-listener: Unable to register wally client compatibility check config override for key: %s, err: %s",
				 zpath_config_wally_client_incompatible_version_check_override_descriptions.key, zpath_result_string(res));
    }

    zpath_config_override_monitor_int(WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK,
                                      &g_tmp_wally_client_incompatible_version_check,
									  wally_client_incompatible_version_check_config_override_cb,
                                      WALLY_CLIENT_INCOMPATIBLE_VERSION_CHECK_DEFAULT,
                                      instance_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

void zpath_init_common_config_overrides(int64_t instance_gid, int64_t customer_id, int64_t client_default)
{
    /* fohh cipher suites overrides */
    zpath_app_fohh_cipher_suite_overrides_init(instance_gid, customer_id, client_default);

    /* role-based access control overrides */
    zpath_app_role_based_access_control_overrides_init(instance_gid);

	/* wally client incompatiblity version check override */
	zpath_app_wally_client_incompatible_version_check_config_overrides_init(instance_gid);
}

void zpath_app_set_fohh_server_cipher_configuration(int64_t instance_gid, int64_t customer_id, int64_t client_default)
{
    if (customer_id) {
        zpath_config_override_monitor_int(CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE,
                                        &g_tmp_cipher_server_value,
                                        fohh_ssl_ctx_set_server_cipher_list_index_cb,
                                        client_default,
                                        instance_gid,
                                        customer_id,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
    } else {
        zpath_config_override_monitor_int(CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE,
                                        &g_tmp_cipher_server_value,
                                        fohh_ssl_ctx_set_server_cipher_list_index_cb,
                                        CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE_DEFAULT,
                                        instance_gid,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
    }
}

void zpath_app_set_fohh_client_cipher_configuration(int64_t instance_gid, int64_t customer_id, int64_t client_default)
{
    if ( customer_id ) {
        zpath_config_override_monitor_int(CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE,
                                        &g_tmp_cipher_client_value,
                                        fohh_ssl_ctx_set_client_cipher_list_index_cb,
                                        CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_DEFAULT,
                                        instance_gid,
                                        customer_id,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
    } else {
        zpath_config_override_monitor_int(CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE,
                                        &g_tmp_cipher_client_value,
                                        fohh_ssl_ctx_set_client_cipher_list_index_cb,
                                        CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE_DEFAULT,
                                        instance_gid,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);
    }
}

void zpath_app_init_fohh_cipher_configuration(int64_t instance_gid, int64_t customer_id, int64_t client_default) {

    int64_t cvalue = zpath_config_override_get_config_int(CONFIG_FEATURE_FOHH_CLIENT_CIPHER_SUITE,
                                                        &cvalue,
                                                        client_default,
                                                        instance_gid,
                                                        customer_id,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    fohh_ssl_ctx_set_client_cipher_list_index_cb(&cvalue, customer_id);
    int64_t svalue = zpath_config_override_get_config_int(CONFIG_FEATURE_FOHH_SERVER_CIPHER_SUITE,
                                                        &svalue,
                                                        DEFAULT_SERVER_CIPHERSUITE_INDEX,
                                                        instance_gid,
                                                        customer_id,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    fohh_ssl_ctx_set_server_cipher_list_index_cb(&svalue, customer_id);
}
