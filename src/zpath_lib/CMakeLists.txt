argo_parse_files(
    INPUT_FILES
        et_geoip_override.c
        et_geoip_override.h
        et_translate_wally.h
        zpa_cloud_config.h
        zpath_aggregate.h
        zpath_app.h
        zpath_broker_proxy_sni.h
        zpath_category.h
        zpath_cloud.h
        zpath_config_override.c
        zpath_config_override.h
        zpath_config_override_keys.h
        zpath_constellation.h
        zpath_customer.h
        zpath_customer_log_config.h
        zpath_customer_logo.h
        zpath_customer_notification.h
        zpath_customer_partition_override.h
        zpath_debug.h
        zpath_domain_lookup.h
        zpath_domainlist.h
        zpath_entity.h
        zpath_et_customer_userdb.h
        zpath_et_customer_zone.h
        zpath_et_region_zone_mapping.h
        zpath_et_service_endpoint.h
        zpath_et_userdb.h
        zpath_et_userdb_service_endpoint.h
        zpath_et_wally_userdb.c
        zpath_et_zone.h
        zpath_feature_flag_keys.h
        zpath_instance.h
        zpath_instance_group.h
        zpath_instance_group_partition.h
        zpath_instance_partition_override.h
        zpath_ip_entity.h
        zpath_ip_location.h
        zpath_limit.h
        zpath_local.h
        zpath_location.h
        zpath_log_config.h
        zpath_log_store.h
        zpath_message.h
        zpath_partition.h
        zpath_partition_common.h
        zpath_partition_profile.h
        zpath_partition_stats.h
        zpath_policy.h
        zpath_query.h
        zpath_rule.h
        zpath_service.h
        zpath_sync_pause.h
        zpath_system.h
        zpath_system_linux.h
        zpath_system_stats.h
        zpath_table.h
        zpath_tag_entity.h
        zpath_tls.h
        zpath_zurldb.h
    #zpath_zvelo_test.c)
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_library(
    zpath_lib
    STATIC
    et_geoip_override.c
    et_translate.c
    et_translate_wally.c
    radix.c
    zpa_cloud_config.c
    zpath_aggregate.c
    zpath_app_debug.c
    zpath_app_logging.c
    zpath_assert.c
    zpath_broker_proxy_sni.c
    zpath_category.c
    zpath_cidr_lookup.c
    zpath_cloud.c
    zpath_config_override.c
    zpath_config_override_desc.c
    zpath_constellation.c
    zpath_customer.c
    zpath_customer_log_config.c
    zpath_customer_logo.c
    zpath_customer_notification.c
    zpath_customer_partition_override.c
    zpath_debug.c
    zpath_debug_wally.c
    zpath_domain_lookup.c
    zpath_domainlist.c
    zpath_entity.c
    zpath_et_customer_userdb.c
    zpath_et_customer_zone.c
    zpath_et_service_endpoint.c
    zpath_et_userdb.c
    zpath_et_wally_userdb.c
    zpath_et_zone.c
    zpath_geoip.c
    zpath_instance.c
    zpath_instance_group.c
    zpath_instance_group_partition.c
    zpath_instance_partition_override.c
    zpath_ip_entity.c
    zpath_ip_location.c
    zpath_ip_table.c
    zpath_lib.c
    zpath_limit.c
    zpath_location.c
    zpath_log_config.c
    zpath_log_store.c
    zpath_match_style.c
    zpath_message.c
    zpath_partition.c
    zpath_partition_common.c
    zpath_partition_profile.c
    zpath_partition_stats.c
    zpath_query.c
    zpath_rule.c
    zpath_service.c
    zpath_sync_pause.c
    zpath_system.c
    zpath_system_linux.c
    zpath_system_stats.c
    zpath_table.c
    zpath_tag_entity.c
    zpath_tls.c
    zpath_zurldb.c
    zpath_zvelo.c
    zpath_upgrade_utils.c
    zpath_oauth_utils.c
    zpath_capability_util.c
    ${generated_headers}
)

if(CMAKE_SYSTEM_NAME STREQUAL "Linux" AND CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
    target_include_directories(zpath_lib PRIVATE $<TARGET_PROPERTY:ZVeloDB,INTERFACE_INCLUDE_DIRECTORIES>)
endif()
target_link_libraries(
    zpath_lib
    PUBLIC wally argo fohh zhash phash cshash zradix zthread zhw LDNS m maxminddb::maxminddb LibZpaCloudConfig
)

# Replacement for zpn/zpath small non small stuff, link these objects to executables instead.
add_library(zpath_app OBJECT zpath_app.c zpath_local.c)
target_link_libraries(zpath_app PRIVATE zpath_lib)
add_library(zpath_app_simple OBJECT zpath_local_nopg.c zpath_simple_app.c)
target_link_libraries(zpath_app_simple PRIVATE zpath_lib)

add_simple_apps(
    SOURCES partition_cshash_test.c et_geoip_override_test.c zpath_cidr_test.c zpath_debug_test.c zpath_geoip_test.c
    #zpath_zvelo_test.c
    DEPS zpath_lib zpath_app
)

add_simple_apps(SOURCES zpath_lib_benchmark.cpp DEPS zpath_lib benchmark::benchmark_main zpath_app)
target_compile_options(zpath_lib_benchmark PRIVATE -Wno-unused-parameter)

add_test(NAME et_geoip_override_test COMMAND et_geoip_override_test)
add_subdirectory(test)
add_subdirectory(gtests)
