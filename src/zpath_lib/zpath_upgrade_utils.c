/*
 * zpn_upgrade_utils.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <unistd.h>
#include <dirent.h>
#include <string.h>
#include <sys/stat.h>
#include "parson/parson.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_upgrade_utils.h"


const char ZPATH_SARGE_AND_OS_UPGRADE_CFG[] = "sarge_os_upgrade_cfg.json";
const char ZPATH_SARGE_AND_OS_UPGRADE_STATUS[] = "sarge_os_upgrade_status.json";
const char ZPATH_SARGE_ROLLBACK_CFG[] = "sarge_rollback_cfg.json";

static JSON_Object *zpath_upgrade_load_json(const char *file, JSON_Value **root_value) {
    *root_value = json_parse_file(file);
    if (*root_value == NULL) {
        /*create file if it does not exist*/
        *root_value = json_value_init_object();
        if (*root_value == NULL) {
            ZPATH_LOG(AL_ERROR, "Failed to initialize JSON object");
            return NULL;
        }
    }
    return json_value_get_object(*root_value);
}

JSON_Object *zpath_upgrade_load_json_if_exists(const char *file, JSON_Value **root_value) {
    *root_value = json_parse_file(file);
    if (*root_value == NULL) {
        ZPATH_LOG(AL_DEBUG, "File %s does not exist", file);
        return NULL;  // File doesn't exist or parsing failed
    }
    return json_value_get_object(*root_value);
}

static int zpath_upgrade_remove_file(const char *file) {
    if (unlink(file) != 0) {
        ZPATH_LOG(AL_NOTICE, "Failed to delete file %s", file);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

void zpath_upgrade_cfg_cleanup() {
    if (zpath_upgrade_remove_file(ZPATH_SARGE_AND_OS_UPGRADE_CFG)) {
        ZPATH_LOG(AL_NOTICE, "Failed to clean up %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        return;
    }
    ZPATH_LOG(AL_NOTICE, "Successfully cleaned up %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
}

void zpath_upgrade_cleanup_rollback_cfg() {
    if (zpath_upgrade_remove_file(ZPATH_SARGE_ROLLBACK_CFG)) {
        ZPATH_LOG(AL_NOTICE, "Failed to clean up %s", ZPATH_SARGE_ROLLBACK_CFG);
        return;
    }
    ZPATH_LOG(AL_NOTICE, "Successfully cleaned up %s", ZPATH_SARGE_ROLLBACK_CFG);
}

static int zpath_upgrade_write_cfg_int(const char *key, int64_t value)
{
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json(ZPATH_SARGE_AND_OS_UPGRADE_CFG, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_ERROR, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        goto cleanup;
    }

    if (json_object_set_number(root_object, key, value) == JSONFailure) {
        ZPATH_LOG(AL_NOTICE, "Failure in setting %s as %"PRId64" in %s", key, value, ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    } else {
        ZPATH_LOG(AL_NOTICE, "Successfully set %s as %"PRId64 " in %s", key, value, ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }

    if (json_serialize_to_file_pretty(root_value, ZPATH_SARGE_AND_OS_UPGRADE_CFG) == JSONFailure) {
        ZPATH_LOG(AL_NOTICE, "Failed to update cfg to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        res = ZPATH_RESULT_ERR;
    } else {
        ZPATH_LOG(AL_NOTICE, "Successfully updated cfg to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

static int zpath_upgrade_write_cfg_str(const char *key, const char *value)
{
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json(ZPATH_SARGE_AND_OS_UPGRADE_CFG, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    if (json_object_set_string(root_object, key, value) == JSONFailure) {
        ZPATH_LOG(AL_ERROR, "Failure in setting %s as %s in %s", key, value, ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    } else {
        ZPATH_LOG(AL_INFO, "Successfully set %s as %s in %s", key, value, ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }

    if (json_serialize_to_file_pretty(root_value, ZPATH_SARGE_AND_OS_UPGRADE_CFG) == JSONFailure) {
        ZPATH_LOG(AL_ERROR, "Failed to update cfg to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        res = ZPATH_RESULT_ERR;
    } else {
        ZPATH_LOG(AL_INFO, "Successfully updated cfg to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }

cleanup:
    if (root_value)
       json_value_free(root_value);
    return res;
}

void zpath_upgrade_set_sarge_upgrade_feature_flag_cfg(int64_t sarge_upgrade_feature_flag)
{
    if (zpath_upgrade_write_cfg_int("sarge_upgrade_feature_flag", sarge_upgrade_feature_flag) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not write sarge_upgrade_feature_flag to %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }
}

void zpath_upgrade_set_os_upgrade_feature_flag_cfg(int64_t os_upgrade_feature_flag)
{
    if (zpath_upgrade_write_cfg_int("os_upgrade_feature_flag", os_upgrade_feature_flag) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not write os_upgrade_feature_flag to %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }
}

void zpath_upgrade_set_sarge_backup_version_feature_flag(int64_t sarge_backup_version_feature_flag)
{
    if (zpath_upgrade_write_cfg_int("sarge_backup_version_feature_flag", sarge_backup_version_feature_flag) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not write sarge_backup_version_feature_flag to %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }
}

void zpath_upgrade_set_full_os_upgrade_version_feature_flag(int64_t full_os_upgrade_feature_flag)
{
    if (zpath_upgrade_write_cfg_int("full_os_upgrade_feature_flag", full_os_upgrade_feature_flag) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not write full_os_upgrade_feature_flag to %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
    }
}

int zpath_upgrade_set_sarge_version(char* expected_sarge_version, char *written_version, size_t written_version_space)
{
    if (zpath_upgrade_write_cfg_str("expected_sarge_version", expected_sarge_version) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not write expected_sarge_version to %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        return ZPATH_RESULT_ERR;
    }
    snprintf(written_version, written_version_space, "%s", expected_sarge_version);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_upgrade_set_os_upgrade_enabled_cfg(int64_t os_upgrade_enabled)
{
    if (zpath_upgrade_write_cfg_int("os_upgrade_enabled", os_upgrade_enabled) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not write os_upgrade_enabled to %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void zpath_upgrade_get_stat_from_json(const JSON_Object *root_object, const char* key, uint64_t* value)
{
    const char *stored_value;
    char *end_ptr = NULL;

    stored_value = json_object_get_string(root_object, key);

    if (!stored_value || stored_value[0] == '\0') {
        ZPATH_LOG(AL_INFO, "Could not get %s", key);
        *value = 0;
    } else {
        *value = strtoull(stored_value, &end_ptr, 10);
        if (end_ptr == stored_value || *end_ptr != '\0') {  // for invalid strings
            *value = 0;
        }
    }
    return;
}

int zpath_upgrade_get_restart_timestamps(uint64_t *last_os_upgrade_time, uint64_t *last_sarge_upgrade_time)
{
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json_if_exists(ZPATH_SARGE_AND_OS_UPGRADE_STATUS, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_DEBUG, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        goto cleanup;
    }

    zpath_upgrade_get_stat_from_json(root_object, "last_os_upgrade_time", last_os_upgrade_time);
    zpath_upgrade_get_stat_from_json(root_object, "last_sarge_upgrade_time", last_sarge_upgrade_time);

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

int zpath_upgrade_read_sarge_and_os_upgrade_cfg(int* os_upgrade_enabled, int* os_upgrade_feature_flag, int *full_os_upgrade_enabled,
                                int* sarge_upgrade_feature_flag, char *expected_sarge_version, size_t expected_sarge_version_len)
{
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json_if_exists(ZPATH_SARGE_AND_OS_UPGRADE_CFG, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_INFO, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        goto cleanup;
    }

    if (json_object_get_number(root_object, "os_upgrade_feature_flag") == 1) {
        *os_upgrade_feature_flag = 1;
    }
    if (json_object_get_number(root_object, "os_upgrade_enabled") == 1) {
        *os_upgrade_enabled = 1;
    }
    if (json_object_get_number(root_object, "full_os_upgrade_feature_flag") == 1) {
        *full_os_upgrade_enabled = 1;
    }

    /* Update only when value is set to 1. Othwerwise default is 0*/
    if (json_object_get_number(root_object, "sarge_upgrade_feature_flag") == 1) {
        /* Read sarge expected_version only when sarge_upgrade_feature_flag is set to 1*/
        const char *json_expected_version = json_object_get_string(root_object, "expected_sarge_version");
        if (!json_expected_version || json_expected_version[0] == '\0') {
            ZPATH_LOG(AL_ERROR, "Missing sarge upgrade version from %s. Skipping sarge upgrade", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
            res = ZPATH_RESULT_ERR;
            goto cleanup;
        }
        snprintf(expected_sarge_version, expected_sarge_version_len, "%s", json_expected_version);
        *sarge_upgrade_feature_flag = 1;
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

int zpath_upgrade_write_upgrade_status(int os_upgrade_status, int sarge_upgrade_status) {

    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json(ZPATH_SARGE_AND_OS_UPGRADE_STATUS, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_ERROR, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        goto cleanup;
    }

    char timestamp_str[32];
    uint64_t timestamp = epoch_us();
    snprintf(timestamp_str, sizeof(timestamp_str), "%llu", (unsigned long long)timestamp);

    if (os_upgrade_status == 1) {
        if (json_object_set_string(root_object, "last_os_upgrade_time", timestamp_str) != JSONSuccess) {
            ZPATH_LOG(AL_ERROR, "Failed to set last_os_upgrade_time in %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        }
    }

    if (sarge_upgrade_status == 1) {
        if (json_object_set_string(root_object, "last_sarge_upgrade_time", timestamp_str) != JSONSuccess) {
            ZPATH_LOG(AL_ERROR, "Failed to set last_sarge_upgrade_time in %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        }
    }

    if (json_serialize_to_file_pretty(root_value, ZPATH_SARGE_AND_OS_UPGRADE_STATUS) == JSONSuccess) {
       ZPATH_LOG(AL_INFO, "Successfully written upgrade time to JSON file written %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    } else {
        ZPATH_LOG(AL_ERROR, "Failed to write upgrade time to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        res = ZPATH_RESULT_ERR;
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

int zpath_upgrade_write_last_working_sarge_version(char *version) {

    if (version == NULL || version[0] == '\0') {
        ZPATH_LOG(AL_ERROR, "Sarge working version not valid!");
        return ZPATH_RESULT_ERR;
    }

    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json(ZPATH_SARGE_ROLLBACK_CFG, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        ZPATH_LOG(AL_ERROR, "Could not load JSON object for %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    if (json_object_set_string(root_object, "last_working_sarge_version", version) != JSONSuccess) {
        ZPATH_LOG(AL_ERROR, "Could not write last_working_sarge_version to %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
    }

    if (json_serialize_to_file_pretty(root_value, ZPATH_SARGE_ROLLBACK_CFG) == JSONSuccess) {
       ZPATH_LOG(AL_NOTICE, "Successfully wrote last sarge version to JSON file %s", ZPATH_SARGE_ROLLBACK_CFG);
    } else {
        ZPATH_LOG(AL_NOTICE, "Failed to write working version to JSON file %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}


int zpath_upgrade_get_last_working_sarge_version(char *version, size_t version_len) {

    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json_if_exists(ZPATH_SARGE_ROLLBACK_CFG, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        ZPATH_LOG(AL_ERROR, "Could not load JSON object for %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    const char* json_version = json_object_get_string(root_object, "last_working_sarge_version");
    if (!json_version || json_version[0] == '\0') {
        ZPATH_LOG(AL_ERROR, "Could not get last sarge version from %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }
    snprintf(version, version_len, "%s", json_version);

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

int zpath_upgrade_update_current_sarge_version(char *version) {

    int res = ZPATH_RESULT_NO_ERROR;

    if (version == NULL || version[0] == '\0') {
        ZPATH_LOG(AL_ERROR, "Sarge last working version not valid!");
        return ZPATH_RESULT_ERR;
    }

    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json(ZPATH_SARGE_ROLLBACK_CFG, &root_value);
    int restart_count = 0;

    if (root_object == NULL) {
        ZPATH_LOG(AL_ERROR, "Could not load JSON object for %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
        goto cleanup;
    }

    const char* json_version = json_object_get_string(root_object, "current_sarge_version");
    if (json_version && (strcmp(json_version, version) == 0)) {

        const char *last_restart_time_str = json_object_get_string(root_object, "last_restart_time");
        uint64_t last_restart_time = last_restart_time_str ? strtoll(last_restart_time_str, NULL, 10) : 0;
        double diff_t = difftime(epoch_s(), last_restart_time);

        JSON_Value *restart_value = json_object_get_value(root_object, "restart_count");
        if (restart_value && (diff_t < 60)) {
            restart_count = (int)json_object_get_number(root_object, "restart_count") + 1;
        }
    }
    json_object_set_number(root_object, "restart_count", restart_count);

    char timestamp_str[32];
    uint64_t timestamp = epoch_s();
    snprintf(timestamp_str, sizeof(timestamp_str), "%llu", (unsigned long long)timestamp);

    if (json_object_set_string(root_object, "last_restart_time", timestamp_str) != JSONSuccess) {
        ZPATH_LOG(AL_NOTICE, "Failed to set last_restart_time in %s", ZPATH_SARGE_ROLLBACK_CFG);
    }

    if (json_object_set_string(root_object, "current_sarge_version", version) != JSONSuccess) {
        ZPATH_LOG(AL_ERROR, "Could not write current_sarge_version to %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
    }

    if (json_serialize_to_file_pretty(root_value, ZPATH_SARGE_ROLLBACK_CFG) == JSONSuccess) {
       ZPATH_LOG(AL_NOTICE, "Successfully written to JSON file %s", ZPATH_SARGE_ROLLBACK_CFG);
    } else {
        ZPATH_LOG(AL_NOTICE, "Failed to write to JSON file %s", ZPATH_SARGE_ROLLBACK_CFG);
        res = ZPATH_RESULT_ERR;
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

int sarge_backup_version_feature_enabled() {
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json_if_exists(ZPATH_SARGE_AND_OS_UPGRADE_CFG, &root_value);
    int res = 0;

    if (root_object == NULL) {
        ZPATH_LOG(AL_INFO, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_CFG);
        goto cleanup;
    }

    if (json_object_get_number(root_object, "sarge_backup_version_feature_flag") == 1) {
        res = 1;
    }
cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

void zpath_upgrade_set_stat_in_json(JSON_Object *root_object, const char* key, uint64_t value)
{
    char value_str[21];
    snprintf(value_str, sizeof(value_str), "%" PRIu64, value);

    if (json_object_set_string(root_object, key, value_str) == JSONFailure) {
        ZPATH_LOG(AL_NOTICE, "Failure in setting %s as %" PRIu64 " in %s", key, value, ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    } else {
        ZPATH_LOG(AL_NOTICE, "Successfully set %s as %" PRIu64 " in %s", key, value, ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    }
}

int zpath_upgrade_write_stats(struct zpath_upgrade_stats* stats)
{
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json(ZPATH_SARGE_AND_OS_UPGRADE_STATUS, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_ERROR, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        goto cleanup;
    }

    zpath_upgrade_set_stat_in_json(root_object, "os_upgrade_fail", stats->os_upgrade_fail);
    zpath_upgrade_set_stat_in_json(root_object, "sarge_upgrade_fail", stats->sarge_upgrade_fail);
    zpath_upgrade_set_stat_in_json(root_object, "os_upgrade_success", stats->os_upgrade_success);
    zpath_upgrade_set_stat_in_json(root_object, "sarge_upgrade_success", stats->sarge_upgrade_success);
    zpath_upgrade_set_stat_in_json(root_object, "os_upgrade_timeout", stats->os_upgrade_timeout);
    zpath_upgrade_set_stat_in_json(root_object, "sarge_os_cfg_read_fail", stats->sarge_os_cfg_read_fail);
    zpath_upgrade_set_stat_in_json(root_object, "sudo_path_fail", stats->sudo_path_fail);
    zpath_upgrade_set_stat_in_json(root_object, "package_manager_path_fail", stats->package_manager_path_fail);

    if (json_serialize_to_file_pretty(root_value, ZPATH_SARGE_AND_OS_UPGRADE_STATUS) == JSONFailure) {
        ZPATH_LOG(AL_NOTICE, "Failed to update fail values to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        res = ZPATH_RESULT_ERR;
    } else {
        ZPATH_LOG(AL_NOTICE, "Successfully updated fail values to JSON file %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
    }

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}

int zpath_upgrade_read_stats(struct zpath_upgrade_stats* stats)
{
    JSON_Value *root_value = NULL;
    JSON_Object *root_object = zpath_upgrade_load_json_if_exists(ZPATH_SARGE_AND_OS_UPGRADE_STATUS, &root_value);
    int res = ZPATH_RESULT_NO_ERROR;

    if (root_object == NULL) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_DEBUG, "Could not load JSON object for %s", ZPATH_SARGE_AND_OS_UPGRADE_STATUS);
        goto cleanup;
    }

    zpath_upgrade_get_stat_from_json(root_object, "os_upgrade_fail", &(stats->os_upgrade_fail));
    zpath_upgrade_get_stat_from_json(root_object, "sarge_upgrade_fail", &(stats->sarge_upgrade_fail));
    zpath_upgrade_get_stat_from_json(root_object, "os_upgrade_success", &(stats->os_upgrade_success));
    zpath_upgrade_get_stat_from_json(root_object, "sarge_upgrade_success", &(stats->sarge_upgrade_success));
    zpath_upgrade_get_stat_from_json(root_object, "os_upgrade_timeout", &(stats->os_upgrade_timeout));
    zpath_upgrade_get_stat_from_json(root_object, "sarge_os_cfg_read_fail", &(stats->sarge_os_cfg_read_fail));
    zpath_upgrade_get_stat_from_json(root_object, "sudo_path_fail", &(stats->sudo_path_fail));
    zpath_upgrade_get_stat_from_json(root_object, "package_manager_path_fail", &(stats->package_manager_path_fail));

cleanup:
    if (root_value)
        json_value_free(root_value);
    return res;
}
