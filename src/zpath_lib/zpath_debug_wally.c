/*
 * zpath_wally_debug.c - Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 */

/*
 * Debug interface for wally, when used by zpath_lib.
 *
 * This file grants itself massive access to internal wally
 * state. Deal with it.
 */

#include <math.h>

#include "zpath_misc/zpath_misc.h"

#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_hash.h"
//#include <wally_postgres.h>
//#include <wally_postgres_compiled.h>

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_debug_wally.h"

#define ZDP(format...) zpath_debug_cb_printf_response(request_state, ##format)

struct walk_state {
    struct zpath_debug_state *request_state;
    int count;
    int show_interest;
    int show_times;
    int show_origin;
    int show_callbacks;
    int show_rows_count;
    int show_rows_pretty;
    int show_fullyload_time;
};

enum wally_debug_cleanup_cmd {
    cleanup_start,
    cleanup_stop,
    cleanup_start_auto_minseq,
    cleanup_stop_auto_minseq,
    cleanup_set_interval,
    cleanup_set_scan_rows,
    cleanup_set_clean_rows
};

struct wally_debug_cleanup_param {
    struct wally *wally;
    enum wally_debug_cleanup_cmd cmd;
    char cmd_str[32];
    char msg[128];
};

struct wally_debug_cleanup_param *cmd_start;
struct wally_debug_cleanup_param *cmd_stop;
struct wally_debug_cleanup_param *cmd_start_auto_minseq;
struct wally_debug_cleanup_param *cmd_stop_auto_minseq;
struct wally_debug_cleanup_param *cmd_set_interval;
struct wally_debug_cleanup_param *cmd_set_scan_rows;
struct wally_debug_cleanup_param *cmd_set_clean_rows;

int zpath_debug_wally_start_recovery_table_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie);

int zpath_debug_wally_stop_recovery_table_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie);

int zpath_debug_wally_status_recovery_table_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie);
int zpath_debug_wally_recovery_details(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie);


struct wally_debug_cleanup_param *create_debug_cleanup_param(struct wally *w,
                                                             enum wally_debug_cleanup_cmd cmd,
                                                             char *cmd_str,
                                                             char *msg)
{
    struct wally_debug_cleanup_param *param;
    param = ZLIB_MALLOC(sizeof(*param));
    param->wally = w;
    param->cmd  = cmd;
    snprintf(param->cmd_str, sizeof(param->cmd_str), "%s", cmd_str);
    snprintf(param->msg, sizeof(param->msg), "%s", msg);
    return param;
}



void zpath_debug_wally_dump_row(struct zpath_debug_state *request_state,
                                struct argo_object *row,
                                int verbose)
{
    char dump[ARGO_BUF_DEFAULT_SIZE];
    int res;

    res = argo_object_dump(row, dump, sizeof(dump), NULL, verbose);
    if (res) {
        ZDP("Error- Could not dump row (perhaps too large) result = %s\n", wally_error_strings[res]);
    } else {
        ZDP("%s\n", dump);
    }
}

void zpath_debug_wally_dump_table(struct zpath_debug_state *request_state,
                                  struct wally_table *table)
{
    size_t i;
    ZDP("Table %s/%s, Status:\n", table->wally->name, table->name);
    for (i = 0; i < table->origins_count; i++) {
        ZDP("  Origin %zu = %s\n", i, table->origins[i]->name);
        ZDP("    my_index = %d\n", table->origins[i]->my_index);
        ZDP("    incarnation = %d\n", table->origins[i]->incarnation);
        ZDP("    last_status = %s\n", table->origins[i]->last_status == wally_origin_status_ready ? "ready":"not ready");
        ZDP("    is_writable = %d\n", table->origins[i]->is_writable);
    }
}

void zpath_debug_wally_dump_interest(struct zpath_debug_state *request_state,
                                     struct wally_interest *interest,
                                     int show_interest,
                                     int show_times,
                                     int show_origin,
                                     int show_callbacks,
                                     int show_rows_count,
                                     int show_rows_pretty,
                                     int show_fullyload_time)
{
    struct wally_index_column *column = interest->column;
    struct wally_table *table = column->table;
    struct wally *wally = table->wally;
    int result_count;
    struct wally_row *r;
    int64_t now;
    char value[100];

    now = epoch_us();

    if (interest->column->is_null) {
        value[0] = 0;
    } else if (interest->column->data_type == argo_field_data_type_integer) {
        snprintf(value, sizeof(value), "%ld", (long)interest->key);
    } else if (interest->column->data_type == argo_field_data_type_string) {
        snprintf(value, sizeof(value), "%s", interest->key_data);
    } else if (interest->column->data_type == argo_field_data_type_binary) {
        value[0] = 0;
        if (table->argo_description->static_description[column->argo_field_index].is_inet) {
            argo_inet_generate(value, (struct argo_inet *)interest->key_data);
        }
    }

    if (show_interest) {
        ZDP("interest:%s/%s/%s/%s ",
            wally->name,
            table->name,
            column->is_null ? "NULL" : table->argo_description->static_description[column->argo_field_index].field_name,
            value);
        ZDP("  interest_list_row_count: %ld", (long)interest->interest_list_row_count);
    }

    if (show_times) {
        ZDP("  Request started = %8.3fs ago, Request took = %8.3fs, Last row = %12.3fs ago\n",
            interest->origin_db_waiting_for_response_start_us ? (double)(now - interest->origin_db_waiting_for_response_start_us) / 1000000.0 : INFINITY,
            (interest->origin_db_waiting_for_response_start_us &&
             interest->origin_db_waiting_for_response_complete_us) ? (double)(interest->origin_db_waiting_for_response_complete_us -
                                                                              interest->origin_db_waiting_for_response_start_us) / 1000000.0 : INFINITY,
            interest->last_row_us ? (double)(now - interest->last_row_us) / 1000000.0 : INFINITY);
        if (show_fullyload_time &&
            (table->fully_loaded_start_epoch_us != 0) && (table->fully_loaded_end_epoch_us != 0)) {
            ZDP("  Fully load time = %8.3fs\n", table->fully_loaded_start_epoch_us ? (double)(table->fully_loaded_end_epoch_us - table->fully_loaded_start_epoch_us) / 1000000.0 : INFINITY);
        }
    }

    if (show_origin) {
        ZDP("  origin: inc = %3d", interest->origin_incarnation);
        ZDP(", state = %-30s", wally_interest_state_str(interest->origin_db_state));
        ZDP(", complete = %d", interest->request_complete);
        ZDP(", complete_atleast_one = %d", interest->request_complete_atleast_one);
        ZDP(", index = %u = %s", interest->origin_db_index, interest->column->table->origins[interest->origin_db_index]->name);
        ZDP(", promote = %d", interest->origin_db_promote);
        ZDP(", request_id = %7ld", (long)interest->origin_db_request_id);
        ZDP(". max_seq = %ld\n", (long) interest->max_sequence_seen);
    }

    if (show_callbacks) {
        /* Need to hold a lock for this or we might get in trouble... */
        struct wally_registrant_callback *cb;
        int64_t now = epoch_us();

        ZLIST_FOREACH(cb, &(interest->registrant_callbacks), interest_list) {
            ZDP("  CB: %s:%s, %8.3fs ago, took %8.6fs, last row %8.3fs ago, seq = %10ld, o_seq = %10ld, row_c = %10ld, rows = %10ld, feeding = %d, at_least_one = %d, rq_id = %7ld: %s\n",
                ((cb->request_atleast_one && interest->request_complete_atleast_one) || ((!cb->request_atleast_one) && interest->request_complete)) ? "complete" : "waiting ",
                cb->callback ? "cb not done" : "cb done",
                (double)(now - cb->request_received_us)/1000000.0,
                cb->request_complete_us ? (double)(cb->request_complete_us - cb->request_received_us)/1000000.0 : INFINITY,
                cb->last_row_us ? (double)(now - cb->last_row_us)/1000000.0 : INFINITY,
                (long)cb->sequence,
                (long)cb->original_sequence,
                (long)cb->row_count_complete,
                (long)cb->row_count,
                cb->is_feeding, cb->request_atleast_one,
                (long)cb->callback_request_id,
                cb->registrant->debug_str);
        }
    }

    result_count = 0;
    if (show_rows_count) {
        ZLIST_FOREACH(r, &(interest->rows), index_match_list[column->my_index]) {
            if (result_count >= show_rows_count) break;
            zpath_debug_wally_dump_row(request_state, r->current_row, show_rows_pretty);
            result_count++;
        }
    }
}

#if 0
int zpath_debug_wally_postgres_f(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    char tmp_str[1000];
    int i;

    struct wally_db_connection_state *state;
    static struct argo_structure_description *desc = NULL;
    size_t count;

    if (!desc) desc = argo_register_global_structure(WALLY_POSTGRES_CONNECTION_STATE_HELPER);

    wally_postgres_get_connection_state(&state, &count);

    for (i = 0; i < count; i++) {
        argo_structure_dump(desc, &(state[i]), tmp_str, sizeof(tmp_str), NULL, 1);
        ZDP("%s\n", tmp_str);
    }
    return ZPATH_RESULT_NO_ERROR;
}
#endif // 0

/* displays minimum wally compatibility version */
int zpath_debug_wally_wally_version_min(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    int major = _WALLY_VERSION_MAJOR_MIN_;
    int minor = _WALLY_VERSION_MINOR_MIN_;
    int patch = _WALLY_VERSION_PATCH_MIN_;

    ZDP("%d.%d.%d\n", major, minor, patch);

    return ZPATH_RESULT_NO_ERROR;
}

void zpath_debug_wally_init(void)
{
#if 0
    zpath_debug_add_admin_command("Dump all the postgres connections.",
                            "/wally/postgres/connections",
                            zpath_debug_wally_postgres_f,
                            NULL,
                            NULL);
#endif // 0

    int res = zpath_debug_add_read_command("Show minimum wally compatibility version.",
                            "/wally/wally_version_min",
                            zpath_debug_wally_wally_version_min,
                            NULL,
                            NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not add /wally/wally_version_min debug command (%d)", res);
    }
}

/*
 * Walk function for walking interests. Note that read lock is held
 * for the duration here
 */
int zpath_debug_wally_interest_walk(void *cookie, void *object, void *key, size_t key_len)
{
    struct walk_state *state = cookie;
    struct wally_interest *interest = object;

    zpath_debug_wally_dump_interest(state->request_state, interest, state->show_interest, state->show_times, state->show_origin, state->show_callbacks, state->show_rows_count, state->show_rows_pretty, state->show_fullyload_time);
    state->count--;
    if (state->count == 0) return -1;
    return 0;
}

int zpath_debug_wally_set_min_sequence_f(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally_index_column *column = cookie;
    struct wally_table *table = column->table;
    int64_t min_sequence;

    /* Extract value... */
    if (query_values[0] == NULL) {
        ZDP("Require sequence argument to set min sequence\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    min_sequence = strtoull(query_values[0], NULL, 0);
    return wally_table_set_min_valid_sequence(table, min_sequence);
}

/*
 * Look up an interest to find its state within this system.
 */
int zpath_debug_wally_interest_f(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    struct wally_index_column *column = cookie;
    struct wally_table *table = column->table;
    struct wally *wally = table->wally;
    struct wally_interest *interest;
    int64_t value;
    const void *vptr;
    size_t vlen;
    int64_t walk_key = 0;
    struct walk_state state;
    int show_table = 0;
    int res;

    memset(&state, 0, sizeof(state));
    state.show_interest = 1;
    state.show_times = 1;
    state.show_origin = 1;
    state.show_callbacks = 1;
    state.show_rows_count = 0;
    state.show_rows_pretty = 0;
    state.request_state = request_state;
    state.count = 1000;
    state.show_fullyload_time = 1;

    /* Extract "rows" row count */
    if (query_values[1]) {
        if (strlen(query_values[1]) == 0) {
            state.show_rows_count = 1000;
        } else {
            state.show_rows_count = strtoul(query_values[1], NULL, 0);
        }
    }

    /* Extract "hide_interest" */
    if (query_values[2]) state.show_interest = 0;

    /* Extract "hide_times" */
    if (query_values[3]) state.show_times = 0;

    /* Extract "hide_origin" */
    if (query_values[4]) state.show_origin = 0;

    /* Extract "hide_callbacks" */
    if (query_values[5]) state.show_callbacks = 0;

    if (query_values[6]) show_table = 1;

    if (query_values[7]) state.show_rows_pretty = 1;


    /* Extract walk key */
    if (query_values[3]) {
        walk_key = strtoull(query_values[3], NULL, 0);
    } else {
        walk_key = 0;
    }

    if (show_table) {
        zpath_debug_wally_dump_table(request_state, table);
    }

    if ((column->is_null)) {
        ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);

        interest = column->null_column_interest;
    } else {
        if (query_values[0] == NULL) {
            /* Look for all interests, using walk fn */
            ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
            if (column->data_type == argo_field_data_type_integer) {
                res = zhash_table_walk(column->index_hash,
                                       &walk_key,
                                       zpath_debug_wally_interest_walk,
                                       &state);
                if (res == WALLY_RESULT_INCOMPLETE) {
                    ZDP("Did not retrieve all interests (max 100 at a time)\n"
                        "To continue, call again with key=%ld (%016lx)\n",
                        (long) walk_key,
                        (long) walk_key);
                }
            } else if ((column->data_type == argo_field_data_type_string) ||
                       (column->data_type == argo_field_data_type_binary)) {
                res = zhash_table_walk(column->string_index_hash,
                                       &walk_key,
                                       zpath_debug_wally_interest_walk,
                                       &state);
                if (res == WALLY_RESULT_INCOMPLETE) {
                    ZDP("Did not retrieve all interests (max 100 at a time)\n"
                        "To continue, call again with key=%ld (%016lx)\n",
                        (long) walk_key,
                        (long) walk_key);
                }
            } else {
                ZDP("Weird data type = %d", column->data_type);
            }
            ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

            return ZPATH_RESULT_NO_ERROR;
        }

        switch (column->data_type) {
        case argo_field_data_type_integer:
            value = strtoull(query_values[0], NULL, 0);
            vptr = &value;
            vlen = sizeof(value);
            break;
        case argo_field_data_type_string:
            vptr = query_values[0];
            vlen = strlen(query_values[0]);
            break;
        default:
            ZDP("We do not support argo_field_data_type %d- this column is not supported\n", column->data_type);
            return ZPATH_RESULT_ERR;
            /*break;*/
        }

        ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);

        if ((column->data_type == argo_field_data_type_string) || (column->data_type == argo_field_data_type_binary)) {
            interest = zhash_table_lookup(column->string_index_hash, vptr, vlen, NULL);
        } else {
            interest = zhash_table_lookup(column->index_hash, &value, sizeof(value), NULL);
        }
    }

    if (!interest) {
        ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        ZDP("No interest found matching value=%s on %s/%s/%s\n",
            query_values[0],
            wally->name,
            table->name,
            table->argo_description->static_description[column->argo_field_index].field_name);
        return ZPATH_RESULT_NO_ERROR;
    }

    zpath_debug_wally_dump_interest(state.request_state, interest, state.show_interest, state.show_times, state.show_origin, state.show_callbacks, state.show_rows_count, state.show_rows_pretty, state.show_fullyload_time);

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_wally_register_f(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    struct wally_index_column *column = cookie;
    int64_t value;
    const void *vptr;
    size_t vlen;
    int res;

    if (column->is_null) {
        ZDP("Null columns not supported yet.\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Extract value to register... */
    if (query_values[0] == NULL) {
        ZDP("Require value argument to register\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    switch (column->data_type) {
    case argo_field_data_type_integer:
        value = strtoull(query_values[0], NULL, 0);
        vptr = &value;
        vlen = sizeof(value);
        break;
    case argo_field_data_type_string:
        vptr = query_values[0];
        vlen = strlen(query_values[0]);
        break;
    default:
        ZDP("We do not support argo_field_data_type %d- this column is not supported\n", column->data_type);
        return ZPATH_RESULT_ERR;
        /*break;*/
    }

    res = wally_table_register_for_row(NULL,
                                       column,
                                       vptr,
                                       vlen,
                                       0,
                                       0,
                                       0,
                                       0,
                                       0,
                                       NULL,
                                       NULL);
    ZDP("Registration returned %s\n", wally_error_strings[res]);
    return ZPATH_RESULT_NO_ERROR;
}


int zpath_debug_wally_deregister_f(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie)
{
    struct wally_index_column *column = cookie;
    int64_t value;
    const void *vptr;
    size_t vlen;
    int res;

    if (column->is_null) {
        ZDP("Null columns not supported yet.\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Extract value to register... */
    if (query_values[0] == NULL) {
        ZDP("Require value argument to register\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    switch (column->data_type) {
    case argo_field_data_type_integer:
        value = strtoull(query_values[0], NULL, 0);
        vptr = &value;
        vlen = sizeof(value);
        break;
    case argo_field_data_type_string:
        vptr = query_values[0];
        vlen = strlen(query_values[0]);
        break;
    default:
        ZDP("We do not support argo_field_data_type %d- this column is not supported\n", column->data_type);
        return ZPATH_RESULT_ERR;
        /*break;*/
    }

    res = wally_table_deregister_for_row(NULL,
                                         column,
                                         vptr,
                                         vlen);
    ZDP("Deregistration returned %s\n", wally_error_strings[res]);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_wally_register_null_f(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    struct wally_index_column *column = cookie;
    int res;

    res = wally_table_register_for_row(NULL,
                                       column,
                                       NULL,
                                       0,
                                       0,
                                       0,
                                       0,
                                       0,
                                       0,
                                       NULL,
                                       NULL);
    ZDP("Registration returned %s\n", wally_error_strings[res]);
    return ZPATH_RESULT_NO_ERROR;
}

int zpath_debug_wally_set_min_sequence_cb(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    struct wally *wally = cookie;
    struct wally_table *table;
    int64_t min_sequence;

    if (!query_values[0] || !query_values[1]) {
        ZDP("Require table name and min_sequence argument to set min sequence\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[0], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }
    min_sequence = strtoull(query_values[1], NULL, 0);

    return wally_table_set_min_valid_sequence(table, min_sequence);
}

int zpath_debug_wally_register_null_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = cookie;
    struct wally_index_column *column;
    struct wally_table *table;
    int res;

    if (!query_values[0]) {
        ZDP("Table name is required to register for row\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[0], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    column = wally_table_get_index(table, NULL);
    if (!column) {
        ZDP("Couldn't get null index column for '%s' table in '%s' wally\n", table->name, wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpath_debug_wally_register_null_f(request_state,
                                            query_values,
                                            query_value_count,
                                            column);

    return res;
}

int zpath_debug_wally_register_cb(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    struct wally *wally = cookie;
    struct wally_table *table;
    struct wally_index_column *column;
    int res;

    if (!query_values[0] || !query_values[1] || !query_values[2]) {
        ZDP("Table name, column name and value are required to register for row\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    table = wally_table_get(wally, query_values[1]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[1], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    column = wally_table_get_index(table, query_values[2]);
    if (!column) {
        ZDP("No index column exist with column name '%s' in '%s' table in '%s' wally\n",
            query_values[2], table->name, wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpath_debug_wally_register_f(request_state,
                                       query_values,
                                       query_value_count,
                                       column);

    return res;
}

int zpath_debug_wally_interest_null_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = cookie;
    struct wally_index_column *column;
    struct wally_table *table;
    int res;

    if (!query_values[9]) {
        ZDP("Table name is required to lookup registration status\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    table = wally_table_get(wally, query_values[9]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[9], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    column = wally_table_get_index(table, NULL);
    if (!column) {
        ZDP("Couldn't get null index column for '%s' table in '%s' wally\n", table->name, wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpath_debug_wally_interest_f(request_state,
                                       query_values,
                                       query_value_count,
                                       column);

    return res;
}

int zpath_debug_wally_interest_cb(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    struct wally *wally = cookie;
    struct wally_index_column *column;
    struct wally_table *table;
    int res;

    if (!query_values[9] || !query_values[10]) {
        ZDP("Table name and column name are required to lookup row(s) from wally\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    table = wally_table_get(wally, query_values[9]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[9], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    column = wally_table_get_index(table, query_values[10]);
    if (!column) {
        ZDP("No index column exist with column name '%s' in '%s' table in '%s' wally\n",
            query_values[10], table->name, wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpath_debug_wally_interest_f(request_state,
                                       query_values,
                                       query_value_count,
                                       column);

    return res;
}

int zpath_debug_wally_deregister_cb(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    struct wally *wally = cookie;
    struct wally_index_column *column;
    struct wally_table *table;
    int res;

    if (!query_values[0] || !query_values[1] || !query_values[2]) {
        ZDP("Table name, column name and value are required to deregister row(s) from wally\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    table = wally_table_get(wally, query_values[1]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[1], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    column = wally_table_get_index(table, query_values[2]);
    if (!column) {
        ZDP("No index column exist with column name '%s' in '%s' table in '%s' wally\n",
            query_values[2], table->name, wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpath_debug_wally_deregister_f(request_state,
                                         query_values,
                                         query_value_count,
                                         column);

    return res;
}

int zpath_debug_wally_list_table_and_index_column_f(struct zpath_debug_state *request_state,
                                                    const char **query_values,
                                                    int query_value_count,
                                                    void *cookie)
{
    struct wally *wally = cookie;

    ZDP("%-50s %s\n", "Table Name", "Indexed Column");
    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    for (size_t i = 0; i < wally->tables_count; i++) {
        ZDP("%-50s: ", wally->tables[i]->name);
        for (size_t j = 0; j < wally->tables[i]->indexed_columns_count; j++) {
            if (!wally->tables[i]->indexed_columns[j] || wally->tables[i]->indexed_columns[j]->is_null) continue;
            ZDP("%s%s", wally->tables[i]->indexed_columns[j]->name,
                ((j == wally->tables[i]->indexed_columns_count - 1 ||
                (wally->tables[i]->indexed_columns[j + 1]->is_null &&
                j + 1 == wally->tables[i]->indexed_columns_count - 1)) ? "" : ", "));
        }
        ZDP("\n");
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

/* Dump interest state stats for each table in the specific wally */
int zpath_debug_wally_interest_state_table_stats(struct zpath_debug_state *request_state,
                                                 const char **query_values,
                                                 int query_value_count,
                                                 void *cookie)
{
    struct wally *wally = cookie;
    struct wally_stats stats = {0};

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    for (size_t i = 0; i < wally->tables_count; i++) {
        struct wally_table *table = wally->tables[i];
        wally_fill_interest_state_stats(table, &stats);
        ZDP("%-42s: ", table->name);
        ZDP("unreg=%-8ld ", (long)stats.unregistered);
        ZDP("reg_waitg=%-8ld ", (long)stats.reg_waiting);
        ZDP("reg=%-8ld ", (long)stats.registered);
        ZDP("reg_x_pend=%-8ld ", (long)stats.reg_xmit_pend);
        ZDP("dereg_x_pnd=%-8ld ", (long)stats.dereg_xmit_pend);
        ZDP("passive=%-8ld ", (long)stats.passive);
        ZDP("deleted=%-8ld ", (long)stats.deleted);
        ZDP("\n");
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

/* Dump total sync pause stats  in the specific wally */
int zpath_debug_wally_sync_pause_wally_stats(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    struct wally *wally = cookie;
    struct wally_sync_pause_stats stats = {0};

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);

    wally_fill_wally_level_sync_pause_stats(wally, &stats);
    ZDP("Wally: %-42s:", wally->name);
    ZDP("tbl_paused=%-8ld ", (long)stats.table_pause_count);
    ZDP("tbl_resumed=%-8ld ", (long)stats.table_resume_count);
    ZDP("rows_paused=%-8ld ", (long)stats.rows_skipped_count);
    ZDP("rows_resumed=%-8ld ", (long)stats.rows_resumed_count);
    ZDP("tbl_resumed_err=%-8ld ", (long)stats.table_resumed_err);
    ZDP("\n");

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

/* Dump sync pause stats for each table in the specific wally */
int zpath_debug_wally_sync_pause_table_stats(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    struct wally *wally = cookie;
    struct wally_sync_pause_stats stats = {0};

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    for (size_t i = 0; i < wally->tables_count; i++) {
        struct wally_table *table = wally->tables[i];
        if (!table->is_pausable)
            continue;
        wally_fill_table_level_sync_pause_stats(table, &stats);
        ZDP("Tbl: %-42s:", table->name);
        ZDP("tbl_paused=%-8ld ", (long)stats.table_pause_count);
        ZDP("tbl_resumed=%-8ld ", (long)stats.table_resume_count);
        ZDP("rows_paused=%-8ld ", (long)stats.rows_skipped_count);
        ZDP("rows_resumed=%-8ld ", (long)stats.rows_resumed_count);
        ZDP("tbl_resumed_err=%-8ld ", (long)stats.table_resumed_err);
        ZDP("\n");
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

void zpath_debug_wally_endpoints_init(struct wally *wally)
{
    char name[256];
    char desc[256];
    int res;

    snprintf(desc, sizeof(desc), "Set minimum sequence for any given table, wally:%s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/set_min_sequence", wally->name);
    res = zpath_debug_add_admin_command(desc,
                                  name,
                                  zpath_debug_wally_set_min_sequence_cb,
                                  wally,
                                  "table", "table name",
                                  "sequence", "minimum sequence to be set",
                                  NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not set minimum sequence, debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Register whole table, wally:%s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/NULL/register", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_register_null_cb,
                                  wally,
                                  "table", "table name",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Register for rows from given table and column, wally:%s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/register", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_register_cb,
                                  wally,
                                  "value", "The value of the column we are registering",
                                  "table", "table name",
                                  "column", "column name",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Look up status of whole table registration, wally:%s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/NULL/status", wally->name);
    res = zpath_debug_add_read_command(desc,
                                  name,
                                  zpath_debug_wally_interest_null_cb,
                                  wally,
                                  "value", "The value of the column we are looking up.",            // 0
                                  "rows", "The number of rows to retrieve (don't hurt yourself)",   // 1
                                  "hide_interest", "Set to hide the interest when dumping.",        // 2
                                  "hide_times", "Set to hide times when dumping.",                  // 3
                                  "hide_origin", "Set to hide origin when dumping.",                // 4
                                  "hide_callbacks", "Set to hide callbacks when dumping.",          // 5
                                  "show_table", "Set to show basic table state",                    // 6
                                  "pretty", "Set to make rows dump pretty",                         // 7
                                  "key", "Continuation key if dumping many interests.",             // 8
                                  "table", "table name",                                            // 9
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Look up row(s) for given table from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/lookup", wally->name);
    res = zpath_debug_add_read_command(desc,
                                  name,
                                  zpath_debug_wally_interest_cb,
                                  wally,
                                  "value", "The value of the column we are looking up.",            // 0
                                  "rows", "The number of rows to retrieve (don't hurt yourself)",   // 1
                                  "hide_interest", "Set to hide the interest when dumping.",        // 2
                                  "hide_times", "Set to hide times when dumping.",                  // 3
                                  "hide_origin", "Set to hide origin when dumping.",                // 4
                                  "hide_callbacks", "Set to hide callbacks when dumping.",          // 5
                                  "show_table", "Set to show basic table state",                    // 6
                                  "pretty", "Set to make rows dump pretty",                         // 7
                                  "key", "Continuation key if dumping many interests.",             // 8
                                  "table", "table name",                                            // 9
                                  "column", "column name",                                          // 10
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Deregister row(s) for given table and column from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/deregister", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_deregister_cb,
                                  wally,
                                  "value", "The value of the column we are deregistering.",
                                  "table", "table name",
                                  "column", "column name",
                                  NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Get list of tables and indexed columns from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/table", wally->name);
    res = zpath_debug_add_read_command(desc,
                                  name,
                                  zpath_debug_wally_list_table_and_index_column_f,
                                  wally,
                                  NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Get interest state count for all tables in wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/interest_state_stats", wally->name);
    res = zpath_debug_add_safe_read_command(desc,
                                  name,
                                  zpath_debug_wally_interest_state_table_stats,
                                  wally,
                                  NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Start Recovery for given table from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/recovery/start", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_start_recovery_table_cb,
                                  wally,
                                  "table_name", "table name",
                                  "max_sequence", "recovery from max sequence",
                                  "sync_missing_rows", "synchronize only missing rows from max_sequence",
                                  "recovery_timeout", "timeout for recovery (default 10 mins)",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }
    snprintf(desc, sizeof(desc), "Stop Recovery for given table from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/recovery/stop", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_stop_recovery_table_cb,
                                  wally,
                                  "table_name", "table name",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Status of Recovery for given table from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/recovery/status", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_status_recovery_table_cb,
                                  wally,
                                  "table_name", "table name",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Recovery supported clients for given table from wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/recovery/clients", wally->name);
    res = zpath_debug_add_write_command(desc,
                                  name,
                                  zpath_debug_wally_recovery_details,
                                  wally,
                                  NULL,
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", name);
    }
}

static int zpath_debug_wally_add_index(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = cookie;
    struct wally_table *table;
    struct wally_index_column *column;
    const char *col_name = query_values[1];

    if (query_values[0] == NULL) return ZPATH_RESULT_BAD_ARGUMENT;
    if (query_values[1] == NULL) col_name = "";

    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("Table <%s> not found for wally %s\n", query_values[0], wally->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    column = wally_table_get_index(table, col_name);
    if (!column) {
        ZDP("Table <%s> does not support/have a column %s\n", query_values[0], query_values[1]);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_dump_origin(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally *wally = cookie;
    char buf[90000];
    int res;

    if (query_values[0]) {
        res = wally_dump_origins(wally, buf, sizeof(buf), 0);
    } else {
        res = wally_dump_origins(wally, buf, sizeof(buf), 1);
    }
    if (res) {
    } else {
        ZDP("%s", buf);
    }
    return ZPATH_RESULT_NO_ERROR;
}

#define LARGE_OUTPUT_BUF_SIZE (64*1024)
static int zpath_debug_wally_dump_origin_write_queue(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally *wally = cookie;
    char *buf = ZLIB_MALLOC(LARGE_OUTPUT_BUF_SIZE);
    int method = 0;
    int res;

    if (query_values[0]) {
        if (!strcasecmp(query_values[0], "name"))           method = 1;
        else if (!strcasecmp(query_values[0], "index"))     method = 2;
    }
    res = wally_dump_write_queue(wally, method, buf, LARGE_OUTPUT_BUF_SIZE, query_values[1] ? atoi(query_values[1]) : -1); // -1 means everything
    if (res)    ZDP("failed with %d\n", res);
    else        ZDP("%s", buf);

    ZLIB_FREE(buf);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_set_cleanup(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie)
{
    struct wally_debug_cleanup_param *param = cookie;
    struct wally *wally = param->wally;
    enum wally_debug_cleanup_cmd cmd = param->cmd;
    char *desc = param->msg;
    int res = ZPATH_RESULT_NO_ERROR;

    struct wally_table *table;
    int val;

    if (query_values[0] == NULL) {
        ZDP("Require table argument to set_scan_rows\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (query_values[1] == NULL) {
        ZDP("%s", desc);
        return ZPATH_RESULT_NO_ERROR;
    }

    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("Table <%s> not found for wally %s\n", query_values[0], wally->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);

    if (!has_null_column(table)) {
        ZDP("The table does not have NULL column\n");

        ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    val = strtoull(query_values[1], NULL, 0);
    switch (cmd) {
        case cleanup_set_scan_rows:
            table->cleanup_param.max_scan_rows = val;
            ZDP("max_scan_rows = %"PRId64"\n", table->cleanup_param.max_scan_rows);
            break;
        case cleanup_set_clean_rows:
            table->cleanup_param.max_cleanup_rows = val;
            ZDP("max_clean_rows = %"PRId64"\n", table->cleanup_param.max_cleanup_rows);
            break;
        case cleanup_set_interval:
            table->cleanup_param.cleanup_interval_us = val;
            ZDP("cleanup_interval_us = %"PRId64"\n", table->cleanup_param.cleanup_interval_us);
            break;
        default:
            res = ZPATH_RESULT_BAD_ARGUMENT;
            break;
    }

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    return res;
}

static int zpath_debug_wally_switch_cleanup(struct zpath_debug_state *request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    struct wally_debug_cleanup_param *param = cookie;
    struct wally *wally = param->wally;
    void *wp_db = wally->origins[0]->callout_handle;
    wally_xfer_callout_cleanup_f *cleanup_callback = wally->origins[0]->cleanup;
    enum wally_debug_cleanup_cmd cmd = param->cmd;
    char *cmd_str = param->cmd_str;
    struct wally_table *table;
    int res = ZPATH_RESULT_NO_ERROR;

    if (query_values[0] == NULL) {
        ZDP("Require table argument for which table to %s\n", cmd_str);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (wally->table_cleanup_pause) {
        ZDP("No active connections to RDS, could not perform cleanup in the Wally %s\n",
                wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("Table <%s> not found for wally %s\n", query_values[0], wally->name);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);

    if (!has_null_column(table)) {
        ZDP("The table does not have NULL column\n");

        ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    switch (cmd) {
        case cleanup_start:
            table->cleanup_param.state = wally_table_cleanup_scanning;
            /* will set it when scan the forst row */
            table->cleanup_stat.start_time_us = 0;
            table->cleanup_stat.request_time_us = 0;
            ZDP("cleanup state = %d\n", table->cleanup_param.state);
            break;
        case cleanup_stop:
            table->cleanup_param.state = wally_table_cleanup_stop;
            if (table->cleanup_stat.start_time_us != 0) {
                table->cleanup_stat.elapsed_us += epoch_us() - table->cleanup_stat.start_time_us;
                table->cleanup_stat.start_time_us = 0;
                table->cleanup_stat.request_time_us = 0;
            }
            ZDP("cleanup state = %d\n", table->cleanup_param.state);
            break;

        case cleanup_start_auto_minseq:
            table->cleanup_param.min_seq_auto_update = 1;
            ZDP("cleanup min_seq_auto_update = %"PRId64"\n", table->cleanup_param.min_seq_auto_update);
            break;
        case cleanup_stop_auto_minseq:
            table->cleanup_param.min_seq_auto_update = 0;
            ZDP("cleanup min_seq_auto_update = %"PRId64"\n", table->cleanup_param.min_seq_auto_update);
            break;

        default:
            res = ZPATH_RESULT_BAD_ARGUMENT;
            break;
    }

    if (cmd == cleanup_start_auto_minseq) {
        (cleanup_callback)(wp_db,
                           table->name,
                           NULL,
                           NULL,
                           NULL,
                           0,
                           table->cleanup_param.min_seq_auto_update);
    }

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    return res;
}

static int zpath_debug_wally_stop_all_cleanup(struct zpath_debug_state *request_state,
                                              const char **query_values,
                                              int query_value_count,
                                              void *cookie)
{
    struct wally *wally = cookie;
    struct wally_table *t;
    int i;

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);
    for (i = 0; i < wally->tables_count; i++) {

        t = wally->tables[i];

        /* already stopped, so skip the table */
        if (t->cleanup_param.state == wally_table_cleanup_stop) {
            continue;
        }

        if (!has_null_column(t)) {
            continue;
        }

        if (t->fully_loaded) {

            t->cleanup_param.state = wally_table_cleanup_stop;
            if (t->cleanup_stat.start_time_us != 0) {
                t->cleanup_stat.elapsed_us += epoch_us() - t->cleanup_stat.start_time_us;
                t->cleanup_stat.start_time_us = 0;
                t->cleanup_stat.request_time_us = 0;
            }
            ZDP("table %s stops cleanup\n", t->name);
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);
    ZDP("---- All tables stop cleanup\n");

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_dump_cleanup(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    struct wally *wally = cookie;
    struct wally_table *t;
    int i;
    char *state;
    int row_expire_sec = 0;
    int unit = 1000;

    ZDP("\nTable Cleanup Status:\n\n");

    ZPATH_RWLOCK_RDLOCK(&(wally->lock), __FILE__, __LINE__);

    ZDP("%9s%9s%9s%14s%12s%12s%12s%11s%14s%10s%17s%15s    %s\n",
            "state", "rotated", "deleted", "scanned", "set_minseq", "max_retry", "clean_time", "scan_time",
            "process_rate", "interval", "max_scan, clean", "minseq_update","Table");
    /* display timestamp in usec for unit testing, in msec for product */
    if (wally->tables_count > 0 && wally->tables[0]->cleanup_unit_test) {
        unit = 1;
        ZDP("%9s%9s%9s%14s%12s%12s%12s%11s%14s%10s%17s%15s    %s\n\n",
            " ", "(rows)", "(rows)", "(rows)", "(counts)", "(counts)",
            "(usec)", "(usec)", "(rows/sec)", "(msec)", "(rows/loop)", " ", " ");
    } else {

        ZDP("%9s%9s%9s%14s%12s%12s%12s%11s%14s%10s%17s%15s    %s\n\n",
            " ", "(rows)", "(rows)", "(rows)", "(counts)", "(counts)",
            "(msec)", "(msec)", "(rows/sec)", "(msec)", "(rows/loop)", " ", " ");
    }

    for (i = 0; i < wally->tables_count; i++) {

        t = wally->tables[i];

        /* no row to delete, so skip displaying the table */
        if (t->row_count == 0 && t->cleanup_param.state == wally_table_cleanup_stop) {
            continue;
        }

        if (!has_null_column(t)) {
            continue;
        }
        row_expire_sec = t->cleanup_param.row_expire_sec;

        if (t->fully_loaded) {
            if (t->cleanup_param.state == wally_table_cleanup_stop) {
                state = "Stopped";
            } else if (t->cleanup_param.state == wally_table_cleanup_pause) {
                state = "Paused";
            } else if (t->cleanup_stat.start_time_us == 0) {
                state = "Pending";
            } else if (t->cleanup_param.state == wally_table_cleanup_cleaning) {
                state = "Cleaning";
            } else if (t->cleanup_param.state == wally_table_cleanup_scanning) {
                state = "Scanning";
            } else {
                state = "Unknown";
            }

            ZDP("%9s%9"PRId64"%9"PRId64"%14"PRId64"%12"PRId64"%12"PRId64"%12"PRId64"%11"PRId64 "%14.2f%10"PRId64"%12"PRId64",%4"PRId64"%15s    %s\n",
                state,
                t->cleanup_stat.rotated_rows,
                t->cleanup_stat.deleted_rows,
                t->cleanup_stat.scanned_rows,
                t->cleanup_stat.set_min_seq_times,
                t->cleanup_stat.max_retry_count,
                t->cleanup_stat.clean_time_us/unit,
                t->cleanup_stat.scan_time_us/unit,
                get_process_rate(t),
                t->cleanup_param.cleanup_interval_us/1000,
                t->cleanup_param.max_scan_rows,
                t->cleanup_param.max_cleanup_rows,
                t->cleanup_param.min_seq_auto_update ? "auto" : "manual",
                t->name);
        }
    }
    if (wally->tables_count > 0) {
        ZDP("-----------------------------------------------------------------------------------------"
            "-----------------------------------------------------------------------------------------\n");
        ZDP("    Origin: %s    Row Expiration Time(sec): %d\n\n", wally->origins[0]->name, row_expire_sec);
    }

    ZPATH_RWLOCK_UNLOCK(&(wally->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

static int fill_wally_stats(void *cookie, int counter, void *structure_data)
{
    struct wally *wally = cookie;
    struct wally_stats *stats = structure_data;

    wally_fill_stats(wally, stats);
    return ARGO_RESULT_NO_ERROR;
}

static int fill_wally_fohh_connection_stats(void *cookie, int counter, void *structure_data) {
    struct wally *wally = cookie;
    struct wally_fohh_connection_stats *stats = structure_data;

    wally_fill_fohh_connection_stats(wally, stats);
    return ARGO_RESULT_NO_ERROR;
}

static int fill_wally_registrant_stats(void *cookie, int counter, void *structure_data)
{
    struct wally *wally = cookie;
    struct wally_registrant_stats *stats = structure_data;
    if (wally && stats) {
        wally_fill_registrant_stats(wally, stats);
    }
    return ARGO_RESULT_NO_ERROR;
}

static int fill_wally_sync_pause_stats(void *cookie, int counter, void *structure_data)
{
    struct wally *wally = cookie;
    struct wally_sync_pause_stats *stats = structure_data;

    wally_fill_wally_level_sync_pause_stats(wally, stats);
    return ARGO_RESULT_NO_ERROR;
}

/*
 * zpath_debug_wally_register_sync_pause
 *  argo log registration for sync pause stats
 */
void zpath_debug_wally_register_sync_pause(struct wally *wally)
{
    char name[256];
    char desc[256];
    int res;

    /* If wally already registered as pausable; inits are done, return */
    if (wally->is_pausable)
        return;

    /* register for argo logging if not already */
    if (!wally->sync_pause_stats_reg) {
        /* argo registration for sync pause stats */
        struct wally_sync_pause_stats *sync_pause_stats = ZLIB_CALLOC(sizeof(*sync_pause_stats));
        snprintf(name, sizeof(name), "%s_sync_pause", wally->name);
        wally->sync_pause_stats_reg = argo_log_register_structure(zpath_stats_collection,
                                name,
                                AL_INFO,
                                60*1000*1000,       /* 1 min interval */
                                wally_sync_pause_stats_description,
                                sync_pause_stats,
                                0,
                                fill_wally_sync_pause_stats,
                                wally);
        if (!wally->sync_pause_stats_reg) {
            ZPATH_LOG(AL_ERROR, "wally_pause_debug: Could not register argo sync_pause for %s", wally->name);
        }
    }

    /* add stats commands if not added already */
    snprintf(desc, sizeof(desc), "Get sync pause stats for all tables in wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/sync_pause_stats", wally->name);
    res = zpath_debug_add_safe_read_command(desc,
                                  name,
                                  zpath_debug_wally_sync_pause_table_stats,
                                  wally,
                                  NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Could not register debug command for %s", name);
    }

    snprintf(desc, sizeof(desc), "Get total sync pause stats in wally: %s", wally->name);
    snprintf(name, sizeof(name), "/wally/%s/sync_pause_stats_total", wally->name);
    res = zpath_debug_add_safe_read_command(desc,
                                  name,
                                  zpath_debug_wally_sync_pause_wally_stats,
                                  wally,
                                  NULL);

    if (res) {
        ZPATH_LOG(AL_ERROR, "wally_pause_debug: Could not register debug command for %s", name);
    }

    /* Finally, set wally as registered for sync pause */
    wally->is_pausable = 1;
}

void zpath_debug_wally_register_registrant_stats(struct wally *wally)
{
    struct wally_registrant_stats *this_wally_registrant_stats = ZLIB_CALLOC(sizeof(*this_wally_registrant_stats));
    if (this_wally_registrant_stats) {
        char name[200];
        snprintf(name, sizeof(name), "%s_registrant_stats", wally->name);
        argo_log_register_structure(zpath_stats_collection,
                                    name,
                                    AL_INFO,
                                    60 * 1000 * 1000,
                                    wally_registrant_stats_description,
                                    this_wally_registrant_stats,
                                    0,
                                    fill_wally_registrant_stats,
                                    wally);
    }
}

int zpath_debug_wally_add(struct wally *wally, int64_t stats_interval_us)
{
    char path[200];
    int res;

    if (stats_interval_us) {
        struct wally_stats *this_wally_stats = ZLIB_CALLOC(sizeof(*this_wally_stats));
        char name[200];
        snprintf(name, sizeof(name), "%s_1s", wally->name);
        argo_log_register_structure(zpath_stats_collection,
                                    name,
                                    AL_INFO,
                                    stats_interval_us,
                                    wally_stats_description,
                                    this_wally_stats,
                                    0,
                                    fill_wally_stats,
                                    wally);
    }

    struct wally_fohh_connection_stats *this_wally_fohh_connection_stats = ZLIB_CALLOC(sizeof(*this_wally_fohh_connection_stats));

    argo_log_register_structure(zpath_stats_collection,
                                wally->name,
                                AL_INFO,
                                60 * 1000 * 1000,
                                wally_fohh_connection_stats_description,
                                this_wally_fohh_connection_stats,
                                0,
                                fill_wally_fohh_connection_stats,
                                wally);


    snprintf(path, sizeof(path), "/wally/%s/create_table_index", wally->name);
    res = zpath_debug_add_write_command("Create an indexed table for accessing the specified table via a specific indexed column.",
                                  path,
                                  zpath_debug_wally_add_index,
                                  wally,
                                  "table", "Table to which to add an index",
                                  "column", "Column to use as the index",
                                  NULL);
    if (res) {
        /* This can fail from duplicates. Ignore it */
        //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
    }
    snprintf(path, sizeof(path), "/wally/%s/origins", wally->name);
    res = zpath_debug_add_read_command("Dump origin state for wally.",
                                  path,
                                  zpath_debug_wally_dump_origin,
                                  wally,
                                  "verbose", "Set to dump more verbosely",
                                  NULL);
    if (res) {
        /* This can fail from duplicates. Ignore it */
        //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
    }

    snprintf(path, sizeof(path), "/wally/%s/write_q", wally->name);
    (void)zpath_debug_add_read_command("Dump write queues for wally.",
                                  path,
                                  zpath_debug_wally_dump_origin_write_queue,
                                  wally,
                                  "sort", "count, name or index, default is by count descending",
                                  "limit", "number of write queues (each corresponding to a table) to show",
                                  NULL);

    if (wally->origins_count==1 && wally->origins[0]->callout_handle && wally->origins[0]->cleanup) {
        snprintf(path, sizeof(path), "/wally/%s/cleanup/status", wally->name);
        res = zpath_debug_add_admin_command("Dump table cleanup status for wally.",
                                      path,
                                      zpath_debug_wally_dump_cleanup,
                                      wally,
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        snprintf(path, sizeof(path), "/wally/%s/cleanup/stop/all", wally->name);
        res = zpath_debug_add_admin_command("Stop trimming all tables.",
                                      path,
                                      zpath_debug_wally_stop_all_cleanup,
                                      wally,
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_start) cmd_start = create_debug_cleanup_param(wally, cleanup_start, "start", "");
        snprintf(path, sizeof(path), "/wally/%s/cleanup/start", wally->name);
        res = zpath_debug_add_admin_command("Start cleanup table.",
                                      path,
                                      zpath_debug_wally_switch_cleanup,
                                      cmd_start,
                                      "table", "table name",
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_stop) cmd_stop = create_debug_cleanup_param(wally, cleanup_stop, "stop", "");
        snprintf(path, sizeof(path), "/wally/%s/cleanup/stop", wally->name);
        res = zpath_debug_add_admin_command("Stop cleanup table.",
                                      path,
                                      zpath_debug_wally_switch_cleanup,
                                      cmd_stop,
                                      "table", "table name",
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_start_auto_minseq) cmd_start_auto_minseq
                              = create_debug_cleanup_param(wally, cleanup_start_auto_minseq, "start_auto_minseq", "");
        snprintf(path, sizeof(path), "/wally/%s/cleanup/auto_minseq_update/on", wally->name);
        res = zpath_debug_add_admin_command("Start updating min sequence automatically on zpath_table.",
                                      path,
                                      zpath_debug_wally_switch_cleanup,
                                      cmd_start_auto_minseq,
                                      "table", "table name",
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_stop_auto_minseq) cmd_stop_auto_minseq
                             = create_debug_cleanup_param(wally, cleanup_stop_auto_minseq, "stop_auto_minseq", "");
        snprintf(path, sizeof(path), "/wally/%s/cleanup/auto_minseq_update/off", wally->name);
        res = zpath_debug_add_admin_command("Stop updating min sequence automatically on zpath_table.",
                                      path,
                                      zpath_debug_wally_switch_cleanup,
                                      cmd_stop_auto_minseq,
                                      "table", "table name",
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_set_interval) {
            cmd_set_interval = create_debug_cleanup_param(wally,
                                                          cleanup_set_interval,
                                                          "set_interval",
                                                          "Require interval argument to set cleanup interval in usec\n");
        };
        snprintf(path, sizeof(path), "/wally/%s/cleanup/set_interval", wally->name);
        res = zpath_debug_add_admin_command("Set cleanup interval (in usec) for table.",
                                      path,
                                      zpath_debug_wally_set_cleanup,
                                      cmd_set_interval,
                                      "table", "table name",
                                      "interval", "cleanup interval in usec",
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_set_scan_rows) {
            cmd_set_scan_rows = create_debug_cleanup_param(wally,
                                                           cleanup_set_scan_rows,
                                                           "set_scan_rows",
                                                           "Require rows argument to set max scan rows per interval\n");
        };

        snprintf(path, sizeof(path), "/wally/%s/cleanup/set_scan_rows", wally->name);
        res = zpath_debug_add_admin_command("Set max scanning rows per interval for table.",
                                      path,
                                      zpath_debug_wally_set_cleanup,
                                      cmd_set_scan_rows,
                                      "table", "table name",
                                      "rows",  "max scanning rows per interval",
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        if (!cmd_set_clean_rows) {
            cmd_set_clean_rows = create_debug_cleanup_param(wally,
                                                            cleanup_set_clean_rows,
                                                            "set_clean_rows",
                                                            "Require rows argument to set max clean rows per interval\n");
        };
        snprintf(path, sizeof(path), "/wally/%s/cleanup/set_clean_rows", wally->name);
        res = zpath_debug_add_admin_command("Set max clean rows per interval for table.",
                                      path,
                                      zpath_debug_wally_set_cleanup,
                                      cmd_set_clean_rows,
                                      "table", "table name",
                                      "rows",  "max clean rows per interval",
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }
    }

    return res;
}

int zpath_debug_wally_recovery_details(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = (struct wally *) cookie;
    char buf[WALLY_CMD_CLIENT_BUFFER_SIZE] = {0};
    int res = ZPATH_RESULT_NO_ERROR;

    zpath_debug_cb_printf_response(request_state, "Unsupported Clients:\n");
    res = wally_retrieve_unsupported_clients(wally, buf, sizeof(buf));
    if (res != ZPATH_RESULT_ERR)
    {
        zpath_debug_cb_printf_response(request_state, "%s\n", buf);
    } else {
        ZDP("Failed to retrieve unsupported clients ");
    }
    return res;
}

/* Function: zpath_debug_wally_status_recovery_table_cb
 * Arg: request_state       : itascurl request state
 *      query_values        : parameter with fixed indexes
 *      query_value_count   : number of query parameters
 *      cookie              : wally object
 * Ret: 0 if success, non-zero values if failure
 * Desc:  This function receives the command from itascurl command
 *  in fohh thread and validates the parameters before passing them to
 *  wally layer
 */
int zpath_debug_wally_status_recovery_table_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = (struct wally *) cookie;
    struct wally_table *table = NULL;

    /* 1. Syntax Errors  */
    if (!query_values[0]) {
        ZDP("Table name is required for the recovery\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    /* If table does not exists, then error returned */
    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[0], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }
    ZPATH_RWLOCK_RDLOCK(&(table->lock), __FILE__, __LINE__);
    ZDP("Recovery State = %s\n", wally_recovery_state_string(table->prev_recovery_state.state));
    if (table->prev_recovery_state.state != recovery_state_noactive) {
        ZDP("Recovery Start Time = %"PRId64"\n", table->prev_recovery_state.start_time);
        ZDP("Clients  = %"PRId64"\n", table->prev_recovery_state.clients);
        if (table->recovery_state.state != recovery_state_noactive) {
            ZDP("Rows Received = %"PRId64"\n", table->recovery_state.received_rows);
            ZDP("Recovered Rows = %"PRId64"\n", table->recovery_state.recovered_rows);
            if (table->recovery_state.start_time) {
                ZDP("Duration = %"PRId64"\n", (epoch_us() - table->recovery_state.start_time));
            }
        } else {
            ZDP("Rows Received = %"PRId64"\n", table->prev_recovery_state.received_rows);
            ZDP("Recovered Rows = %"PRId64"\n", table->prev_recovery_state.recovered_rows);
            ZDP("Duration = %"PRId64"\n", table->prev_recovery_state.duration);
        }
        ZDP("Pending Clients Response = %"PRId64 "\n", table->prev_recovery_state.pending_responses);
    }
    ZPATH_RWLOCK_UNLOCK(&(table->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

/* Function: zpath_debug_wally_stop_recovery_table_cb
 * Arg: request_state       : itascurl request state
 *      query_values        : parameter with fixed indexes
 *      query_value_count   : number of query parameters
 *      cookie              : wally object
 * Ret: 0 if success, non-zero values if failure
 * Desc:  This function receives the command from itascurl command
 *  in fohh thread and validates the parameters before passing them to
 *  wally layer
 */
int zpath_debug_wally_stop_recovery_table_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = (struct wally *) cookie;
    struct wally_table *table = NULL;

    /* 1. Syntax Errors  */
    if (!query_values[0]) {
        ZDP("Table name is required for the recovery\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    /* If table does not exists, then error returned */
    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[0], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }
    wally_recovery_handle_stop_cmd_handler(wally, query_values[0]);
    return ZPATH_RESULT_NO_ERROR;
}

/* Function: zpath_debug_wally_start_recovery_table_cb
 * Arg: request_state       : itascurl request state
 *      query_values        : parameter with fixed indexes
 *      query_value_count   : number of query parameters
 *      cookie              : wally object
 * Ret: 0 if success, non-zero values if failure
 * Desc:  This function receives the command from itascurl command
 *  in fohh thread and validates the parameters before passing them to
 *  wally layer
 */
int zpath_debug_wally_start_recovery_table_cb(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    struct wally *wally = (struct wally *) cookie;
    struct wally_table *table = NULL;
    int64_t recovery_sequence = 0, recovery_timeout = WALLY_DEFAULT_RECOVERY_TIMEOUT_SEC;
    int res = ZPATH_RESULT_NO_ERROR, sync_missing_rows = 0;
    char *endptr = NULL;
    char buf[WALLY_CMD_ERROR_BUFFER_SIZE] = {0};

    /* 1. Syntax Errors  */
    if (!query_values[0]) {
        ZDP("Table name is required for the recovery\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!query_values[1]) {
        ZDP("Max sequence is required for the table recovery\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (query_values[2]) {
        sync_missing_rows = 1;
    }

    if (query_values[3]) {
        recovery_timeout = strtoull(query_values[3], &endptr, 0);
        if ((errno == ERANGE) ||  (*endptr != '\0') || (recovery_timeout <= 0)) {
            ZDP("Invalid recovery timeout '%s' with '%s' table for '%s' wally \n",
                query_values[3], query_values[0], wally->name);
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    /* Convert seconds into microseconds */
    recovery_timeout = recovery_timeout * WALLY_DURATION_US_IN_S;

    /*  out of range, invalid characters, lower boundary */
    recovery_sequence = strtoull(query_values[1], &endptr, 0);
    if (errno == ERANGE || *endptr != '\0' || recovery_sequence < 0) {
        ZDP("Incorrect max sequence value '%s' with '%s' table for '%s' wally\n",
                    query_values[1], query_values[0], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }
    /* If table does not exists, then error returned */
    table = wally_table_get(wally, query_values[0]);
    if (!table) {
        ZDP("No table exist with name '%s' in '%s' wally\n", query_values[0], wally->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Send request wally layer for command handling */
    res = wally_table_recovery_cmd_handler(buf, sizeof(buf), wally, query_values[0], recovery_sequence, sync_missing_rows, recovery_timeout);
    switch(res)
    {
        case WALLY_RESULT_NO_ERROR:
        case WALLY_RESULT_ASYNCHRONOUS:
        {
            ZDP("Recovery Initiated for %"PRId64" recovery sequence in %s table in %s wally. Result = %s \n",
                    recovery_sequence, query_values[0], wally->name, wally_error_strings[res]);
        }
        break;
        default:
        {
            zpath_debug_cb_printf_response(request_state, "%s\n", buf);
            ZDP("Recovery Failed for %"PRId64" recovery sequence in %s table in %s wally. Result = %s \n",
                    recovery_sequence, query_values[0], wally->name, wally_error_strings[res]);
        }
        break;
    }
    return ZPATH_RESULT_NO_ERROR;
}
