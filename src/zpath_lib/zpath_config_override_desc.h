/*
 * zpath_config_override_desc.h. Copyright (C) 2023 Zscaler Inc, All Rights Reserved
 *
 * Description of a config-override.
 */

#ifndef _ZPATH_CONFIG_OVERRIDE_DESC_H_
#define _ZPATH_CONFIG_OVERRIDE_DESC_H_

#include <inttypes.h>
#include <stddef.h>

/* Logging Utilities for Config Override */
#define CONFIG_OVERRIDE_MISCONFIG_LOG(format...) ZPATH_LOG(AL_CRITICAL, "Config Override Misconfig: " format)

#define ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(f)\
         .str_valid_cb = (f),                                \
         .str_valid_cb_name= (#f)

#define ZPATH_CONFIG_OVERRIDE_BOOL_FALSE 0
#define ZPATH_CONFIG_OVERRIDE_BOOL_TRUE 1
#define ZPATH_CONFIG_OVERRIDE_BOOL_RANGE .int_range_lo = ZPATH_CONFIG_OVERRIDE_BOOL_FALSE, .int_range_hi = ZPATH_CONFIG_OVERRIDE_BOOL_TRUE

typedef int (zpath_config_override_desc_str_validate_f)(const char *cfg_str);

enum config_value_type {
    config_type_missing = 0,
    config_type_int = 1,
    config_type_str = 2,
};

// enum allows us to detect misconfigs instead of assuming 0 is false
enum config_toggle_indicator {
    config_binary_setting_unconfigured = 0,
    config_binary_setting_false = 1,
    config_binary_setting_true = 2,
};

enum config_value_traits {
  config_value_traits_invalid = 0,
  config_value_traits_feature_enablement = 1,
  config_value_traits_systemwide = 2,
  config_value_traits_normal = 3,

  /* MUST BE LAST */
  config_value_traits_max
};

/*
 * When adding a new type here you MUST add it to the decoders
 * and adjust the max string length accordingly
 */
enum zpath_config_override_component_type {
    config_component_wally = 0x1,
    config_component_broker = 0x2,
    config_component_pbroker = 0x4,
    config_component_appc = 0x8,
    config_component_exporter = 0x10,
    config_component_slogger = 0x20,
    config_component_cdispatcher = 0x40,
    config_component_ipars = 0x80,
    config_component_l4proxy = 0x100,
    config_component_uatu = 0x200,
    config_component_ot_broker = 0x400,
    config_component_ot_exporter = 0x800,
    config_component_sitec = 0x1000,
    config_component_np_gateway = 0x2000,
    config_component_npc = 0x4000,
};

enum config_target_gid_type {
    config_target_gid_type_global = 0x01,
    config_target_gid_type_zsroot = 0x02,
    config_target_gid_type_inst = 0x04,
    config_target_gid_type_inst_grp = 0x08,
    config_target_gid_type_cust = 0x10,
    config_target_gid_type_partition_cust = 0x20,
    config_target_gid_type_partition_inst = 0x40,
    config_target_gid_type_domain = 0x80
};

/*
 * When updating this you MUST also do the following
 * 1. Update zpath_config_override_desc_category_types_decode with a new string value
 * 2. Update zpath_config_override_max_category_type_str if your new str value is longer than 'Disaster Recovery'
 * 3. Update this documentation if you modified zpath_config_override_max_category_type_str with new max string
 */

/*
 * Usage:
 * 1. Statically define a set of zpath_config_override_desc structures,
 *    one for each new/existing config-override.
 * 2. A registry of these structures will exist when config_override
 *    module is initialized.
 * 3. When a configuration is requested or monitored, validate input
 *    value against int_range_lo/hi defined for the config, and use
 *    int_default if value falls outside of the range.
 *    If a different default is provided when requesting the config,
 *    the default provided through the API will be used instead
 *    (this is only for API compatibility).
 */
struct zpath_config_override_desc {
    /* Name/key of the feature/config */
    const char *key;

    /*
     * Tentatively DEPRECATED
     * Grouping of configs (1 level now due to the flat structure of
     * existing keys)
     */
    /*
     * Name used for project integration with other teams
     * Name must be agreed on by all teams involved (API, UX, Itasca)
     * Will link features together in UI
     */
    const char *feature_group;

    /* Description of the config - what does it do? */
    const char *desc;

    /* Detailed description on how the config is to be used, including:
     *   components that this apply to;
     *   value type, range and default;
     *   per-instance, customer, or global
     * ... everything you would've put in a comment.
     * For documentation and UI assistance (on Atlantic) */
    const char *details;

    enum config_value_type val_type;

    /* A bitflag encoding the types of components using this config
     * (e.g. broker, pbroker, connector) */
    int component_types;

    /* A bit flag encoding the types of GIDs that this config supports
     * (e.g. per instance, per customer, per partition, global) */
    int target_gid_types;

    /* For input validation. If provided value is outside of declared range,
     * int_default will be used instead. */
    int64_t int_range_lo;
    int64_t int_range_hi;
    int64_t int_default;
    enum config_value_traits value_traits;

    /* For input validation.
     * If the callback is provided it will be forwarded the current value for the key requested
     * If the callback returns !0 it will use the value from the config override as the return value
     * If the callback returns 0 it will use the str_default value
     *
     * Setting the name can be done using the macro ZPATH_CONFIG_OVERRIDE_DESC_SET_VALIDATOR to ensure its consistent
     */
    char *str_default;
    zpath_config_override_desc_str_validate_f *str_valid_cb;
    const char *const str_valid_cb_name;
};

int zpath_config_override_desc_init(enum zpath_config_override_component_type sys_type);

struct zpath_config_override_desc *zpath_config_override_desc_get_int_description(const char *key);

struct zpath_config_override_desc *zpath_config_override_desc_get_str_description(const char *key);

enum config_value_type zpath_config_override_desc_get_description_type(const char *key);

int zpath_config_override_desc_register(struct zpath_config_override_desc *desc);

#endif /* _ZPATH_CONFIG_OVERRIDE_DESC_H_ */
