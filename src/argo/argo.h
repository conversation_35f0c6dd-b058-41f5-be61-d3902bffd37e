/*
 * argo.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Argo
 *
 * Argo was the ship which carried <PERSON> and the Argonauts on their
 * voyage...
 *
 * JSON (JavaScript Object Notation) is a very simple format for
 * encoding data as text objects. See www.json.org.
 *
 * Here, Argo is a means of serializing/deserializing "C" structures
 * as JSON objects in one of two forms: As text JSON objects, or as
 * binary encoded/compressed JSON objects. Argo, when binary
 * compressing, is optimized for the case where a very large number of
 * objects of very similar nature are transferred or stored. If single
 * objects only are stored, argo will work fine, but you will not
 * really get any compression.
 *
 * Furthermore, due to the manner in which argo serializes data, it
 * provides a nice interface for providing abstract structure data
 * manipulation in 'C'. There are many utility functions that can be
 * used to help in this regard. Though not the primary purpose of
 * argo, it may well turn out that this functionality becomes its most
 * useful feature.
 *
 * Goals:
 *
 *   1. Achieve JSON-like data abstraction for transferring "C" data
 *      structures from one system to another. JSON is particularly
 *      good at this because it is relatively self-describing. Every
 *      piece of data can be named within JSON. This naming of all
 *      data allows for significant flexibility when managing
 *      independent systems that must communicate with each other.
 *
 *   2. Improve performance compared to performing simple
 *      structure-to-text JSON serializing and deserializing:
 *
 *      A. Encoding performance: integer<-->string translations make
 *         JSONifying binary data relatively slow. ARGO improves on
 *         this by 4-6x.
 *
 *      B. Data efficiency: JSON is a bit wordy, particularly when
 *         almost all data within it is named. (to achieve goal
 *         #1). Argo in binary-compressed mode achieves serialization
 *         that usually results in encoded objects smaller than their
 *         original structure size, and vastly smaller than their
 *         JSON-text size.
 *
 *   3. Easy to use. It should be relatively easy to take "C"
 *      structures and serialize them into this form.
 *
 *   4. Easy to debug. ARGO should be able to run in either its
 *      binary-encoding mode or its text-JSON mode, and switch between
 *      them, at any time.
 *
 *   5. Reasonable for data storage in large data systems. (HDFS, etc)
 *
 * Non-Goals:
 *
 *   1. Maximum performance. If exceptionally high performance is
 *      required, a mechanism such as Google's protocol buffers or
 *      apache Avro might be more desirable.
 *
 *   2. Maximum compression. This code is intended to be "pretty good"
 *      performance and "pretty good" compression, while keeping code
 *      maintainability in mind.
 *
 *
 * Break JSON: It would be very handy to support JSON perfectly.
 *   However, JSON has a couple characteristics that don't lend
 *   themselves to "C" representation quite as well as would be
 *   desired.
 *
 * - An argo stream is basically a never-ending object.
 *
 * - Argo streams include stream descriptions for describing the
 *   stream and its format. The name "argo_description" is the name of
 *   an object that is reserved within argo; any user that collides
 *   with this name will probably end up angry.
 *
 * - the maximum (only) integer size in argo is signed 64 bit. 8, 16,
 *   32, and 64 bit integer sizes are supported within the "C"
 *   structures being serialized- but be careful, as they will be
 *   sign-extended. (i.e. changing the size of an integer that is
 *   negative will sign extend it... This can be dangerous with
 *   "flags" that are being treated as integers.)
 *
 * - True, False, NULL, and Floating point values are currently not
 *   supported in argo. They may be supported some day, but not right
 *   now.
 *
 * - A binary data type is supported. The binary type is explicitly
 *   supported when using compressed serialization. It is supported
 *   via base64 encoding when using JSON serialization. When reading
 *   from text serialization, Argo cannot, without the help of a "C"
 *   structure description, differentiate between a string and a
 *   binary value.
 *
 * - Argo handles all required string escaping on its own- the user
 *   need not be concerned with it.
 *
 * - A true JSON object can have multiple elements (values) with the
 *   same name, but the "C" API does not allow this except for at the
 *   "root" level. This library will not crash in this scenario. Which
 *   of the multiple values will "win" and end up reflected in the 'C'
 *   structure will be non-deterministic.
 *
 * - Block encoded, when binary. The compression format uses data
 *   accumulated throughout a "block" to aid in compression. That
 *   means, in order to read the n'th object in a block, you must read
 *   the first n-1 objects in the block. However, any given block can
 *   be read completely independently of any other block. This block
 *   encoding scheme is designed explicitly to match well with
 *   distributed data systems. (HDFS, etc). It should be noted that
 *   even when serializing as text, argo will not allow objects to
 *   cross a block boundary.
 *
 * - Names are limited to 256 bytes. (Values can be much larger).
 *
 *
 *
 * A stream consists of an arbitrary number of blocks.
 *
 * A block consists of the following three elements:
 *
 * 1. Block description object: An object named "argo_description"
 *    containing name/value pairs for describing this argo
 *    block. These include version, encoding style, compression,
 *    etc. This description also includes a "block length" which
 *    indicates how many bytes (including this description) after
 *    which this block will end, and another block will begin. Block
 *    description objects are ALWAYS text representations of
 *    objects. Block description objects can occur anywhere in the
 *    block, but usually only occur on block boundaries. (mid-stream
 *    block descrptions can be used to switch to/from text/binary
 *    serialization for debugging, for instance.)
 *
 * 2. A sequence of name/value pairs. (much like an object, but
 *    without end, and in-order.) No name/value pair will cross a
 *    block boundary. All values will be objects at the "root" level.
 *
 * 3. Padding of 0 or more bytes, to the re-description length.
 *
 * A stream is only ever open for reading or for writing, never for
 * both. It is possible to "append" to a stream- that is, there are
 * interfaces for reading an existing stream (i.e. a file) to learn
 * the context needed in order to append to it. (Cheap appending can
 * be performed by beginning with a block description...)
 *
 *
 * Using Argo:
 *
 * 1. Decide whether or not Argo makes sense to use. Like any system,
 *    Argo has significant benefits as well as drawbacks. Do not use
 *    it blindly.
 *
 * 2. Choose/configure the structures you wish to use with Argo, and
 *    use the (simple, sorry) markups on them. Remember that when argo
 *    is running, it gets field names and types from its description.
 *
 *    A. Be Clear. You might have a field in your structure named
 *       'int32_t tm' for 'time'. You would probably be better off
 *       being more descriptive, by calling it 'time_in_seconds' or
 *       maybe 'unix_epoch_at_start' or the like. Note that in its
 *       binary/compressed form, the size of the names you use will
 *       have basically no impact on the size of the encoded stream.
 *
 *    B. Don't change your mind. It is very important to note that
 *       changing the NAME of a field has serious side-effects- namely
 *       you will likely lose compatibility with old files/systems
 *       that use the "old" name. If you want new functionality, it is
 *       best done by adding a new field, and carefully deprecating
 *       the old field. This exercise is actually handy insofar as it
 *       aids in backwards compatibility. I do not currently plan to
 *       have the concept of "synonyms" for element names, but if they
 *       ever come into being, then this becomes maybe a lesser
 *       issue. (Or maybe just defers the headache- thus me reluctance
 *       to implement it.)
 *
 *    C. Take initialization into consideration, particularly for
 *       compatibility. If argo deserializes a structure for you, it
 *       will first ask you to initialize the structure. Then
 *       deserialization takes place, filling in the structure with
 *       the deserialized values from the stream. However, it is
 *       explicitly NOT required for argo to fill in all fields of a
 *       structure- the stream may not contain values for all fields
 *       of a structure. (usually because of a versioning mismatch.)
 *       It is the responsibility of the user to ensure that, during
 *       initialization, these potentially unfilled fields are
 *       initialized and used in a manner such that the software works
 *       correctly.
 *
 *    E. Don't worry about structure description arrays, if you can
 *       help it. They only really exist to make it possible for argo
 *       to understand structure layout. The generated structure
 *       descriptions should be adequate in all cases.
 *
 *    F. If you are really concerned with compression performance,
 *       consider the following:
 *
 *       i. Group often-unchanging (low chaos) fields together.
 *
 *       ii. Use enumerated encoding for integer values that come from
 *       a small set.
 *
 *       iii. Smaller enumerated "sizes" are often more efficient than
 *       large ones, particularly if there is very strong locality of
 *       reference in the encoded data.
 *
 *       iv. "array_string" type was really created to store URLs and
 *       paths somewhat efficiently. It might work for others;
 *       experiment before assuming it is a good way to store strings.
 *
 * 3. struct argo_state: This structure is used to maintain
 *    serialization state for exactly one direction of exactly one
 *    stream. It cannot be used for multiple streams
 *    simultaneously. If you are performing bidirectional
 *    communication on a STREAM socket, for example, you will need two
 *    of these- one for the receive state, and one for the transmit
 *    state.
 *
 * 4. struct argo_structure_description: This is a description of a
 *    structure. It is created globally, once, and can thereafter be
 *    used against any argo_state. (Its information, while private, is
 *    const). The system will warn you if you attempt to describe the
 *    same structure twice by the same name.
 *
 * 5. struct argo_object: This is a generic container for a structure.
 *    It will contain a registered structure data as well as "excess"
 *    data that is not described by the registered structure. This is
 *    exceptionally handy for dealing with abstract structure data.
 *
 * 6. argo_structure_description registration. For any individual
 *    argo_state to be able to understand a structure, that structure
 *    description must be registered with it. This registration must
 *    happen for every argo_state that wishes to understand the
 *    structure. This registration allows the argo_state to keep track
 *    of compression state (if needed), as well as translation state
 *    to allow data to be serialized and deserialized in the
 *    structure.
 *
 *    A. When registering a description, you choose a callback
 *       function for that structure to call when a structure of that
 *       type is deserialized. The structure exists on the stack only-
 *       use one of the structure copying routines (or copy the
 *       structure yourself) in order to maintain a copy of this
 *       state. The structure is cleared to zero on allocation.
 *       Note that if an argo is only used for serialization, no
 *       callback is required.
 *
 *    B. The callback function returns both a void * pointing at an
 *       instance of the registered structure as well as a generic
 *       container structure. The generic container structure can be
 *       used to access/move data that is not described by the
 *       registered structure.
 *
 *    C. You choose a structure name when registering the
 *       structure. This name is very important- it is used to match
 *       the "type" of sub-structures, as well as the "name" of ROOT
 *       structures. The name should be the name of the structure
 *       without the "struct" keyword. But if required, you can really
 *       choose anything you want... just make sure you understand the
 *       ramifications. You can register a structure multiple times
 *       with different names if that for some reason suits your
 *       fancy. I don't see a good reason to do so, but it will work.
 *
 *    D. You should probably just use the markup-created auto
 *       generated field-description array when compiling your
 *       structures. It really is much easier.
 *
 *    E. Sub-structures do not necessarily have to be predefined- the
 *       system can 'figure out' most of them on its own. The only
 *       type for which this is currently not the case (bug) is arrays
 *       of strings.
 *
 * 7. Neither serializing nor deserializing directly block. If their
 *    callbacks block, that is their affair.
 *
 *
 * Members of a structure are fields. Each field has a type and a
 * name. The field name is explicitly called "field_name" whenever it
 * is used in argo in order to clearly differentiate it from other
 * uses of "name".
 *
 * A structure has a "type." All descriptions of structures are
 * referred to by type.
 *
 * Notes on sub-structures:
 *
 * Sub-structures are structures within structures in
 * 'C'. Substructures are often void* in C, and argo must be able to
 * deal with these dynamically. Consider the following:
 *
 * struct my_struct {
 *     void *my_substructure_1;
 *     void *my_substructure_2;
 * };
 *
 * In this case, if you don't 'help' argo at all, it will assume that
 * my_substructure_1 and my_substructure_2 are described by the
 * argo_structure_description of that name. However, consider the case
 * where you want these two substructures to have the same type- or
 * perhaps a type that varies over time. In this case, it is
 * impossible for argo to know implicitly what type these structures
 * have. For this reason, argo has the ability to define the type of
 * this object explicitly, in run-time code- the 'type_offset' field
 * of an argo_field_description. The type offset field is simply a
 * run-time string encoding the 'type' to apply to this
 * substructure. (Note that the 'type' field is implicit in JSON-
 * these structure descriptions/names are used for the 'C'
 * encoding/decoding only, really)
 *
 * Furthermore, it is possible to apply a name to the
 * substructure. The name may be used by argo to perform better
 * compression (same name key compression) when transmitting many
 * objects of the same type from different sources. (This name is
 * pulled run-time from the 'name_offset' field of the argo structure
 * description)
 *
 * A precise example of this taking place is as follows. Consider an
 * application keeping statistics for a number of TCP connections. It
 * may want to keep the following stats for each TCP connection:
 *
 * (see argo_parse.c for markup description)
 *
 * Note: Take out the '*' when making real code, as this file gets
 * parsed...
 *
 * struct tcp_stats {      // _*ARGO: object_definition
 *     uint64_t  tx_bytes; // _*ARGO: integer
 *     uint64_t  rx_bytes; // _*ARGO: integer
 * };
 *
 * Now, you may want a logging system for logging these
 * statistics. Such a logging system will probably have a standard
 * header for each log entry, such as:
 *
 * struct log_entry {   // _*ARGO: object_definition
 *     char *log_name;  // _*ARGO: string, name:log_data
 *     char *log_type;  // _*ARGO: string, type:log_data
 *     void *log_data;  // _*ARGO: object
 * };
 *
 * At run-time, ARGO will use log_name and log_type to encode/decode
 * log_data correctly. If a log_entry contains, in 'C':
 * { "127.0.0.1:80", "tcp_stats", 0xSomepointer }
 *
 * Then argo can serialize this as (json):
 *
 * "log_entry" : {
 *    "log_name":"127.0.0.1:80",
 *    "log_type":"tcp_stats",
 *    "log_data": {
 *       "tx_bytes":100,
 *       "rx_bytes":200
 *    }
 * }
 *
 * When deserializing, the name/type fields allow argo to understand
 * how to encode log_data into C structures again.
 *
 * If either name or type are left out of the structure description,
 * they are taken from the name/type of the 'C' field itself. (In this
 * case, the substructure name would have been 'log_data', and the
 * substructure type would have been 'void')
 *
 * For the time being, for ease of development, please make sure
 * name/type occur earlier in serialized streams than the void
 * *data. The system can't quite interpret them correctly otherwise.
 */

#ifndef __ARGO_H
#define __ARGO_H

#include <stddef.h>
#include <stdio.h>
#include <inttypes.h>
#include <netinet/in.h> /* for INET6_ADDRSTRLEN */

/*
 * Version- used to communicate what binary encoding format is being used.
 */
#define ARGO_VERSION_MAJOR 3
#define ARGO_VERSION_MINOR 0

/*
 * Return error codes.
 */
#define ARGO_RESULT_NO_ERROR          0   /* AKA success */
#define ARGO_RESULT_ERR               1   /* Generic failure: when
                                           * none of the below
                                           * apply. */
#define ARGO_RESULT_NOT_FOUND         2   /* Could not find what was
                                           * requested. */
#define ARGO_RESULT_NO_MEMORY         3   /* Could not allocate
                                           * memory */
#define ARGO_RESULT_CANT_WRITE        4   /* Failure to write (output
                                           * callback failed?) */
#define ARGO_RESULT_ERR_TOO_LARGE     5   /* Requested data doesn't
                                           * fit in space provided */
#define ARGO_RESULT_BAD_ARGUMENT      6   /* Asked for something
                                           * wrong. */
#define ARGO_RESULT_INSUFFICIENT_DATA 7   /* Was not provided enough
                                           * data to perform
                                           * operation */
#define ARGO_RESULT_NOT_IMPLEMENTED   8   /* Yes, for features that
                                           * are not yet
                                           * implemented. */
#define ARGO_RESULT_BAD_DATA          9   /* Tried to parse data, but
                                           * format seemed wrong. */
#define ARGO_RESULT_REPARSE          10   /* Tried to parse data, but
                                           * doing so changed state,
                                           * requiring us to
                                           * re-parse. This should
                                           * NEVER be seen in
                                           * user-code. It is used
                                           * internally. */
#define ARGO_RESULT_WOULD_BLOCK      11   /* Attempting operation
                                           * would result in
                                           * blocking. Bad, naughty
                                           * blocking. */
#define ARGO_RESULT_BAD_STATE        12   /* Encountered some bad
                                           * internal state */
#define ARGO_RESULT_INCOMPLETE       13   /* Encountered some bad
                                           * internal state */
#define ARGO_RESULT_ASYNCHRONOUS     14   /* Asynchronous result
                                           * expected later */
#define ARGO_RESULT_EXCESS_DYN_FIELDS 15  /* The RPC has too many dynamic fields */
#define ARGO_RESULT_VALIDATION_ERR   16   /* Indicates non-critical yet major failure during validation of some state/args/params at runtime */
#define ARGO_RESULT_MAX              16   /* Max error number */

/*
 * A couple handy token sizes.
 */
 /* Don't use this one. It's silly. */
#define ARGO_DEFAULT_MAX_TOKENS_HUGE  (128*128*128*32)
/* Use rarely, and only if you know what you're doing. Outrageous
 * token footprint. */
#define ARGO_DEFAULT_MAX_TOKENS_LARGE (128*128*32)
/* Reasonable to use for efficient token compression */
#define ARGO_DEFAULT_MAX_TOKENS_SMALL (128*32)
/* Default, and works pretty good for most cases, with small memory
 * footprint. */
#define ARGO_DEFAULT_MAX_TOKENS_TINY  (32)
#define ARGO_ABSOLUTE_MAX_TOKENS      ARGO_DEFAULT_MAX_TOKENS_HUGE

/*
 * The maximum length of any object's name within argo, including NULL.
 */
#define ARGO_MAX_NAME_LENGTH 256

/*
 * There are two different styles for encoding objects in a binary
 * serialization stream.
 *
 * explicit encoding: Explicit encoding encodes the name and value of
 *    every structure element that is serialized. There are no
 *    assumptions made about the data being encoded. This is the most
 *    basic encoding style. It is not particularly efficient, as the
 *    names of the name/value pairs are repeated (even though
 *    compressed) in the serialization stream over and over for each
 *    record written.
 *
 * repeating encoding: Repeating encoding tells the encoder that, for
 *    every record with this name (type), there will be exactly the
 *    same number of name/value pairs. Furthermore, these name/value
 *    pairs will occur in the same order. In this case, the encoder
 *    encodes all the name/value pairs for this record name (type) the
 *    first time it is seen, then it assumes those names for each
 *    record thereafter. This encoding style is extremely efficient
 *    when storing a large number of nearly identical records, the
 *    majority of whose data does not change from record to record.
 *
 * In general, there is no reason whatsoever to use any encoding other
 * than repeating.
 */
enum argo_structure_encoding_style {
    argo_encoding_explicit = 1,
    argo_encoding_repeating
};

/*
 * Serialization modes available.
 *
 * Serialization modes can be specified when creating an argo instance
 * and when sending structures to argo to be serialized. The
 * deserializer correctly deals with either, dynamically.
 */
enum argo_serialize_mode {
    argo_serialize_dont_care = 0,
    argo_serialize_json_pretty,
    argo_serialize_json_no_newline,
    argo_serialize_binary
};

/*
 * The different types argo supports. If you need another type, feel
 * free to use a binary type.
 *
 * type_dictionary and type_object are special in that they are fields
 * understood by the argo library to represent argo_dictionary and
 * argo_object types. When encountering these types within objects,
 * the argo library copies by reference rather than making true
 * copies.
 *
 * XXXXX WARNING: The order of these encodings directly maps to bits
 * in a binary stream. Do NOT change the order of these fields. Adding
 * new field data types is fine.
 */
enum argo_field_data_type {
    argo_field_data_type_invalid = 0,
    argo_field_data_type_object,       // 1
    argo_field_data_type_null,         // 2
    argo_field_data_type_boolean,      // 3
    argo_field_data_type_integer,      // 4
    argo_field_data_type_epoch,        // 5
    argo_field_data_type_float,        // 6
    argo_field_data_type_double,       // 7
    argo_field_data_type_string,       // 8
    argo_field_data_type_inet,         // 9
    argo_field_data_type_binary,       // 10
    argo_field_data_type_argo_object,  // 11

    /* Below are 'specials' that cannot be binary encoded (yet) */
    argo_field_data_type_dictionary,   // 12
    argo_field_data_type_list,         // 13
};

/*
 * The hints are used in structure descriptions, and are usually only
 * autogenerated by argo_parse markup.
 */
enum argo_field_caching_hint {
    argo_field_caching_one = 1, /* Only cache one value    */
    argo_field_caching_tiny,    /* Only cache 32 values    */
    argo_field_caching_small,   /* Cache 32*128 values     */
    argo_field_caching_large,   /* Cache 32*128*128 values */
    argo_field_caching_huge,    /* Cache 32*128*128*128 values. That's
                                 * BIG memory. */
};
enum argo_field_encoding_hint {
    argo_field_encoding_differential = 1,
    argo_field_encoding_cached,
    argo_field_encoding_chunked
};

/*
 * The public and private versions of structure configuration.
 */

/*
 * Structure description.
 *
 * Argo needs a description of a structure in order to serialize
 * it. An array of the following structures is used describe a
 * structure. Each element of the array describes one field of the
 * structure. (Sometimes with an auxiliary count)
 *
 * *** NOTE ***
 *
 * This structure is usually generated automatically. See argo_parse.h
 *
 * *** NOTE 2 ***
 *
 * wally (the database distribution library) uses argo internal state
 * to access/maintain/modify POSTGRES tables. In order to do this more
 * easily, some markup has been added to argo_descriptions that aids
 * in postgres access. This isn't clean design, but is done to allow
 * creation of argo_objects directly from rows of database tables, and
 * vice-versa.
 *
 * The structure description is passed to argo to be compiled into an
 * argo_compiled_structure_description.
 *
 * argo_field_type - The type of the field, as argo knows it. This
 *          field must fairly closely match the real C type. For
 *          binary and string types, the field referred to by the
 *          offset is assumed to be a pointer. It is acceptable for
 *          the pointer to be NULL. If the argo_type is a substructure
 *          (either explicit or referenced)
 *
 * argo_caching_hint - Used to control how large of a token cache argo
 *          will use when tokenizing the structure into a binary stream.
 *
 * argo_encoding_hint - Used to control how argo encodes data. For
 *          strings, generally cached. For Integers, generally chunked.
 *
 * offset - The offset within the structure where the field described
 *          by this array element lives. Handy to use the offsetof()
 *          compiler builtin function for this. (argo_parse does this)
 *
 * size - The size of the field, in bytes. (Or the size of the target
 *          referenced)
 *
 * count - The number of elements. (Only used for arrays, and only if
 *          have_dynamic_count_index is zero)
 *
 * reference_count - Number of dereferences (pointers) i.e. char **foo
 *          is 2, char foo[10] is 0
 *
 * hard_count - The absolute number of elements for which there is
 *          space in the structure itself. (Generally same as count)
 *
 * write_value - Indicates whether or not this field should be
 *          (de)serializable by argo. (Sometimes counts/names/types
 *          may not want to be serialized, even if they exist, since
 *          JSON has some amount of implicit numbering, and is
 *          type-free.)
 *
 * is_array - Whether or not this field is an array.
 *
 * is_reference - Whether or not the offset is a pointer to the
 *          specified data.
 *
 * is_index - Postgres Hint. Whether or not this field is to be
 *          indexed for fast lookups.
 *
 * is_sequence - Postgres Hint. Whether or not this field is a
 *          sequence.
 *
 * is_key - Postgres Hint. Whether or not this field represents a
 *          unique nonchanging key representing the structure data.
 *
 * is_inet - Whether or not this field represents an internet
 *          address. Internet addresses (IPv4 and IPv6) are encoded in
 *          text when using JSON, and as binary objects when using
 *          compressed binary encoding.
 *
 * is_double - Whether or not this field represents a double (floating
 *          point value. This is encoding using i86 floating point
 *          format
 *
 * is_unique - Postgres Hint. Indication whether or not the field is
 *          unique within a table.
 *
 * dont_write_origin - Postgres Hint. Indicates that the column should
 *          never be written when writing to the true origin DB. (Used
 *          for sequences, date fields, etc, when feeding data back
 *          into postgres). However, the field IS written when writing
 *          to a slave DB. (See wally for details of true origin/slave
 *          databases)
 *
 * dont_read_origin - Postgres Hint. Indicates that we shouldn't read
 *          this field from any origin database.
 *
 * dont_db - Explicitly ensure this field is not in the DB. (removes
 *          column if it was there)
 *
 * is_reqid - An indication that this field is used for passing
 *          request ID's. This is used by wally-fohh.
 *
 * is_nozero - Whether or not to serialize this field when sending
 *          text-JSON form if the type is integer and the value is
 *          zero. (Can save a lot of space if you don't want to see
 *          fields whose values are zero)
 *
 * is_deleted - Whether or not this field is an indication that a row
 *          is deleted. Used to assist postgres/database abstraction.
 *
 * have_sub_name_index - Whether or not this substructure has its name
 *          represented dynamically in this structure.
 *
 * have_sub_type_index - Whether or not this substructure has its type
 *          represented dynamically in this structure.
 *
 * have_dynamic_count_index - Whether or not this field has its
 *          element count represented dynamically in this structure.
 *
 * is_runtime_defined - Should always be set to zero.
 *
 * stringify - If set, then when serializing to JSON, this (probably
 *          integer) field will be written as a string.
 *
 * json_old - If set, then use 'old style' sub-structures when
 *          serializing to JSON.
 *
 * sub_name_index - The index of the field within the structure
 *          describing the name of this field (which must be a
 *          sub-structure)
 *
 * sub_type_index - The index of the field within the structure describing
 *          the type of this field (which must be a sub-structure)
 *
 * dynamic_count_index - The index of the field within the structure
 *          describing the number of elements of this field (which
 *          must be an array)
 *
 * references_other - Indicates that this field only exits because it
 *          is controls some other field for the sake of type clarity
 *          or array size.
 *
 * field_name - The name of the field. This is a text name that is
 *          used by argo to identify the structure field- it is not
 *          directly related to the name of the field as it is known
 *          to the C compiler. This is the name as it is used by argo
 *          to identify structure fields. An actual text match takes
 *          place in order to match data going in/out of a serialized
 *          stream with a structure field. Argo is very efficient at
 *          encoding names- it does not impact argo's 'C' performance
 *          if the name is quite descriptive. However, it may have
 *          some effect if the name is exceptionally long when running
 *          the java version of argo.
 *
 * field_name - The name of the field.
 *
 * field_type_name - The type of the named field. Only valid for
 *          objects that are direct typed sub-structures.
 *
 * synonym - Synonym for field_name
 *
 * description - A description of the field. This is used primarily as
 *          a means of extra documentation. It is not encoded in the
 *          serialized stream. (ToDo: Add markup for this in
 *          argo_parse)
 */
#define ARGO_MAX_SYNONYMS 3
struct argo_field_description {                                  /* _ARGO: object_definition */
    enum argo_field_data_type       argo_field_type;             /* _ARGO: integer */
    enum argo_field_data_type       argo_new_field_type;         /* _ARGO: integer */
    enum argo_field_caching_hint    argo_caching_hint;           /* _ARGO: integer */
    enum argo_field_encoding_hint   argo_encoding_hint;          /* _ARGO: integer */
    size_t                          offset;                      /* _ARGO: integer */
    size_t                          size;                        /* _ARGO: integer */
    size_t                          count;                       /* _ARGO: integer */
    size_t                          reference_count;             /* _ARGO: integer */
    size_t                          hard_count;                  /* _ARGO: integer */
    int8_t                          tokenize;                    /* _ARGO: integer */
    int8_t                          write_value;                 /* _ARGO: integer */
    int8_t                          is_array;                    /* _ARGO: integer */
    int8_t                          is_reference;                /* _ARGO: integer */
    int8_t                          is_index;                    /* _ARGO: integer */
    int8_t                          is_sequence;                 /* _ARGO: integer */
    int8_t                          is_key;                      /* _ARGO: integer */
    int8_t                          is_inet;                     /* _ARGO: integer */
    int8_t                          is_double;                   /* _ARGO: integer */
    int8_t                          is_unique;                   /* _ARGO: integer */
    int8_t                          dont_write_origin;           /* _ARGO: integer */
    int8_t                          dont_read_origin;            /* _ARGO: integer */
    int8_t                          dont_db;                     /* _ARGO: integer */
    int8_t                          is_reqid;                    /* _ARGO: integer */
    int8_t                          is_nozero;                   /* _ARGO: integer */
    int8_t                          is_deleted;                  /* _ARGO: integer */
    int8_t                          have_sub_name_index;         /* _ARGO: integer */
    int8_t                          have_sub_type_index;         /* _ARGO: integer */
    int8_t                          have_dynamic_count_index;    /* _ARGO: integer */
    int8_t                          references_other;            /* _ARGO: integer */
    int8_t                          is_runtime_defined;          /* _ARGO: integer */
    int8_t                          stringify;                   /* _ARGO: integer */
    int8_t                          json_old;                    /* _ARGO: integer */
    int8_t                          adj_time;                    /* _ARGO: integer */
    size_t                          sub_name_index;              /* _ARGO: integer */
    size_t                          sub_type_index;              /* _ARGO: integer */
    size_t                          dynamic_count_index;         /* _ARGO: integer */
    char                            *field_name;                 /* _ARGO: string */
    size_t                          field_name_length;           /* _ARGO: integer */
    char                            *field_type_name;            /* _ARGO: string */
    size_t                          field_type_name_length;      /* _ARGO: integer */
    const char                      *description;                /* _ARGO: string */
    char                            *synonym[ARGO_MAX_SYNONYMS]; /* _ARGO: string */
    size_t                           synonym_count;              /* _ARGO: integer */
};


/*
 * excess_field -
 *
 * An individual excess field- always encoded as a pointer and a
 * count. (Because that can store about anything)
 *
 * If field_value is NULL, or if count is zero, then this field
 * "doesn't exist"
 *
 * Remember that all integers in argo are 64 bit integers.
 *
 * Other portions of the system keep track of what type this field is.
 *
 * excess fields cannot store arrays of strings. (Yet. Good one for
 * the to-do list)
 */
struct argo_excess_field {
    void *field_value;
    int64_t count;
};

/*
 * argo_inet -
 *
 * A data structure used to store internet addresses- either IPv4 or
 * IPv6. This data structure largely mimics the postgres inet data
 * type. This type is used in order to perform serialization correctly
 * into text formats, and to easily interface with postgres inet
 * types.
 *
 * argo_inet isn't simply a sockaddr type because sockaddr's are
 * annoying, and because sockaddrs include port numbers, which we
 * explicitly don't want to include in the definition of an IP
 * address. There are routines for converting to/from argo_inet,
 * strings, sockaddrs.
 *
 * JSON doesn't have an inet type. When serializing an inet into json
 * (text) form, argo uses simple text of the form inet_ntop, with a
 * trailing /netmask. (Unless the netmask is /128 for IPv6 or /32 for
 * IPv4)
 *
 * When serializing an inet into json (binary) form, this packed
 * structure is simply sent as binary data. Note that this is the only
 * complex data type argo uses internally (so far). These complex data
 * types must be exceptionally static in order to ensure forward and
 * backward compatibility.
 *
 * When using an argo_inet for IPv4, it is a good idea to ensure the
 * 'excess' address space is zero. It will probably save you from a
 * lot of bugs.
 *
 * Currently, the valid lenghts for addresses are 0, 4, and 16. A
 * length 0 address is indicative of no address/NULL address.
 */
struct argo_inet {
    uint8_t length;        /* Generally 4 or 16- the length of the address, in bytes. */
    uint8_t netmask;       /* The number of significant bits in the address. */
    uint8_t address[16];   /* The address itself. */
} __attribute__((packed));

/* And argo_inet in string form can be at least 4 bytes longer than a
 * normal inet6 address, since the argo_inet can include a trailing
 * netmask, i.e. /120) */
#define ARGO_INET_ADDRSTRLEN (INET6_ADDRSTRLEN + 4)

/*
 * argo_object -
 *
 * Object used for argo callbacks. Can (optionally) be used for
 * encoding, as well. This data type exists primarily to pass
 * opaque-ish objects between argo instances.
 *
 * argo_object is used to deal with objects containing fields that are
 * not specified at runtime.
 *
 * Most of these fields are relatively opaque- they are not meant for
 * direct user access- with the exception of base_structure_*
 *
 * base_structure_index is the index of the globally registered
 *   structure description with which this object is associated. This
 *   index + excess_field_count can give you each excess field type,
 *   whether it exists, etc. (Generally not used by library users)
 *
 * excess_description_count is the number of "extra" fields this
 *   object contains beyond those defined by the globally registered
 *   structure. Note that this is 2 * the number of excess_fields that
 *   have been added, as each excess field includes a count that is
 *   not serialized
 *
 * reference count - Completely ordinary reference count.
 *
 * total_size - The length, in bytes, of this object, including this
 *   structure, all excess fields, and all "heap" data used to
 *   represent this object. (strings, etc)
 *
 * base_structure_data is a pointer to registered structure
 *   data. I.e. this is the expected data held in this object.
 *
 * base_structure_void is the same thing. Just void form for easier
 *   typecasting.
 *
 * excess_fields is an array describing the excess fields.
 *
 * When an argo_object is given, it exists linearly in memory as
 * follows: (This is provided for informational use, it should not be
 * necessary to know this to use argo)
 *
 * 1. A struct argo_object. 'base_structure_*' points at #2
 * 2. Structure data
 * 3. excess_fields array- for unrecognized fields from decoding.
 * 4. object "heap", containing all referenced data.
 *
 * It should be remembered that ALL object data is controlled by the
 * object, and should not be freed except by argo_ routines.
 */
struct argo_object {
    int32_t base_structure_index;
    int32_t excess_description_count;
    int32_t reference_count;
    int32_t total_size;
    union {
        uint8_t *base_structure_data;
        void *base_structure_void;
    };
    struct argo_excess_field *excess_fields;
};


/*
 * argo_structure_description contains compiled state associated with
 * a structure. When encoding a structure, you pass an
 * argo_structure_description with the structure so that argo knows
 * how to do the encoding.
 *
 * argo_structure_description is created from an array of
 * argo_field_descriptions via argo_register_global_structure.
 *
 * argo_structure_description can be used with any argo context.
 */
struct argo_structure_description;

/*
 * argo_state is a handle on a single serialization XOR
 * deserialization engine.
 */
struct argo_state;

/*
 * Buffering structure
 */
struct argo_buf;

/*
 * Callback routines used by this library.
 */

/*
 * argo_structure_callback_f - Called with a populated structure from
 * decoding. Note that only the "root" structure's callback is
 * called. Callbacks for sub-structures are not called.
 *
 * Note that the object referred to by this callback exists on the
 * stack only. If you wish to keep it, you must make a copy of it.
 *
 * This routine should return an ARGO_RESULT type, of which NO_ERROR
 * is the only one that will allow decoding to continue.
 */
typedef int (argo_structure_callback_f)(void *argo_cookie_ptr,
                                        void *argo_structure_cookie_ptr,
                                        struct argo_object *object);

/*
 * You can set a callback to be called whenever a version is received
 * in the deserialization stream
 */
typedef void (argo_encoding_version_callback_f)(void *cookie,
                                                int major_version,
                                                int minor_version);
void argo_set_version_callback(struct argo_state *argo, argo_encoding_version_callback_f *callback);

/*
 * argo_write_callback_f - When encoding a stream, this callback is
 * called to write to the stream.
 */
typedef int (argo_write_callback_f)(void *argo_cookie_ptr, const uint8_t *data, size_t length);

/*
 * argo_bulk_write_callback_f - An alternate write callback form. This
 * form is used if the output routines require a single callback for
 * each object serialized. The bulk_write_callback_f is called exactly
 * once for each seralized object. This callback can make successive
 * calls to argo_bulk_read in order to retrieve the "pieces" of data
 * comprising the serialized object to be written. This allows the
 * callback to aggregate a complete object at a time rather than
 * having an object spread over (potentially) multiple
 * argo_write_callback calls.
 */
typedef int (argo_bulk_write_callback_f)(void *data_reference, void *argo_cookie_ptr, int64_t callback_count);

/*
 * argo_bulk_read is used to read data during a bulk_write_callback_f.
 * It should repeatedly be called until no data is returned, or an
 * error condition occurs.
 *
 * data_reference - The data reference passed to the argo_bulk_write_callback_f.
 *
 * out_data - Pointer to serialized data to send.
 *
 * out_data_len - Length of that data. Will be zero if no more data is available.
 */
int argo_bulk_read(void *data_reference, uint8_t **out_data, size_t *out_data_len);

/*
 * argo_bulk_size - Returns the total size of data available to read
 * in the data_reference.
 */
size_t argo_bulk_size(void *data_reference);

/*
 * Initialize the argo library. Should only be called once.
 *
 * max_descriptions is the maximum number of different descriptions
 * that will ever exist across all argo instances on this system.
 */
int argo_library_init(size_t max_descriptions);
size_t argo_library_get_max_descriptions(void);

/*
 * DO NOT USE IN PRODUCTION CODE
 *
 * argo_library_reinit reinitializes argo to a clean slate and memory.
 * It is NOT thread safe.
 */
int argo_library_reinit(size_t max_descriptions);
/*
 * Configure logging for the library. Not required; handy to do a bit
 * later in initialization, thus separated from argo_library_init.
 */
int argo_library_log(const char *log_name);

/*
 * Get a string representation of an argo result.
 */
const char *argo_result_string(int argo_result);

/*
 * Allocate state for using argo. Remember that a single argo is only
 * used exclusively for serialization or exclusively for
 * deserialization. Thus you only need to specify some of the
 * callbacks, depending on what purpose the allocation is for.
 *
 * write_callback, cookie_ptr - These are variables describing the
 *     callback that is to be made by argo when it is writing
 *     serialized data. The serialized data is passed to this function
 *     along with cookie_ptr. (argo_write_callback_f)
 *
 * bulk_write_callback, cookie_ptr - This callback can be used instead
 *     of the normal write callback. (Used for slightly different
 *     access to the transmit data) If specified, this callback will
 *     be used instead of write_callback, for all write callback
 *     cases.
 *
 * default_read_callback - A default callback that can be called for
 *     any structure read for which we do not have a registered
 *     type. (Allows some amount of dynamic structure discovery, for
 *     almost all objects except those containing arrays of strings)
 *
 * block_size - When serializing in binar mode, this is the number of
 *     bytes argo will stream before it resets its compression
 *     state. Resetting compression state allows for more random
 *     access to files. Larger block sizes result in slightly better
 *     compression. When argo is reading instead of writing, it
 *     inherits the block size of the data stream being read, and this
 *     value is not used. If block size is zero, then compression is
 *     never reset.
 *
 * mode - This is the serialization mode that will be used by default
 *     for serializing structures. This value can be changed during
 *     operation. This mode can be explicitly overriden as an argument
 *     to the serialization routine.
 *
 * max_types - This controls the maximum number of structure types
 *     this argo instance is likely to encounter. This is mostly a
 *     hint, as space is reallocated if more is needed.
 *
 * strict_version_check - This controls whether or not strict version
 *     checking is done when parsing an argo stream. If set
 *     (non-zero), then a major version mismatch will be an error
 *     condition. Otherwise, it attempts to parse correctly regardless
 *     of versioning. Strict version check only really happens on
 *     receive data, of course.
 *
 * accept_binary - An indication whether or not binary encoded argo
 *     data is allowed to be received.
 *
 * Returns NULL on failure.
 */
struct argo_state *argo_alloc(argo_write_callback_f *write_callback,
                              argo_bulk_write_callback_f *bulk_write_callback,
                              argo_structure_callback_f *default_read_callback,
                              void *cookie_ptr,
                              uint64_t block_size,
                              enum argo_serialize_mode mode,
                              size_t max_types,
                              int strict_version_check,
                              int accept_binary);

/*
 * Set/Get the encoding version. Only needed if you need to set to an
 * older major version.
 *
 * Can be used to get the encoding version used by a remote host
 * before setting the encoding version when transmitting to that
 * host... (If you are FOHH)
 */
void argo_set_encoding(struct argo_state *state, int version);
int argo_get_encoding(struct argo_state *state);

/*
 * Free an argo structure.
 *
 * This frees all state associated with the structure. It does NOT
 * free compiled structure descriptions. Those are basically
 * permanent.
 */
void argo_free(struct argo_state *argo);

/*
 * Set the argo specified to not ever transmit a version, nor perform
 * any block padding. This will be effective so long as it is called
 * before any transmission occurs on the argo.
 */
void argo_no_version_xmit(struct argo_state *argo);


/*
 * Globally register a structure description.
 *
 * ALL fields used to register this function are autogenerated by the
 * structure parsing module. The parsing module generates a macro that
 * can replace the first four arguments- the macro is simply an
 * upper-case name of the structure with a _HELPER appended. For
 * example, if you happen to have a "struct my_struct" that is
 * compiled, you will then have a "MY_STRUCT_HELPER" which can replace
 * the first four arguments of this registration routine- you could
 * call:
 *
 * foo = argo_register_global_structure(MY_STRUCT_HELPER);
 *
 * If this routine is called multiple times, only the first "does
 * anything"- the other calls are all ignored and the same description
 * as the first call is returned.
 *
 * structure_type - The type of the structure as it is known by
 *     argo. (string)
 *
 * structure_size - sizeof(struct structure_name)
 *
 * description - An array describing all the fields of a structure
 *     which we wish argo to be able to encode or decode. (serialize
 *     or deserialize). This code assumes this data is constant and
 *     available at all times- i.e. the compiled description keeps a
 *     reference to the uncompiled description, and uses it, without
 *     copying it.
 *
 * description_count - The number of elements in the description array.
 *
 * It should be noted that it is possible to register structures
 * "generically", in a manner allowing for arbitrary contents. In this
 * case, structure_size should be zero, description should be NULL,
 * and description_count should be zero.
 */
#ifndef MOCK_ARGO_REGISTER_GLOBAL_STRUCTURE
struct argo_structure_description *
argo_register_global_structure(const char *structure_type,
                               size_t structure_size,
                               const struct argo_field_description *description,
                               size_t description_count);
#endif //MOCK_ARGO_REGISTER_GLOBAL_STRUCTURE

/*
 * Rename a global structure. Note that this is generally done for
 * testing purposes only, to generate serialized state for some other
 * structure to try to interpret.
 */
int argo_rename_global_structure(struct argo_structure_description *desc,
                                 const char *new_name);

/*
 * Globally register a name used for storing an argo_dictionary. This
 * is similar to argo_register_global_structure except that the type
 * of structure being registered is fully understood by argo, so only
 * the name is needed.
 *
 * Multiple registrations are allowed, and return the same description
 * object.
 */
struct argo_structure_description *
argo_register_global_dictionary(const char *structure_type);


/*
 * Register a structure description with an argo_state.  In
 * particular, this routine allocates the state used for serialization
 * and deserialization of the stream, and registers callbacks.
 *
 * argo - The argo_state to which to register the structure.
 *
 * description - The argo_structure_description to register.
 *
 * callback_f - The callback to make when completing deserialization
 *    of this structure.
 *
 * callback_cookie - Cookie to be passed back with the callback
 *    function
 */
int argo_register_structure(struct argo_state *argo,
                            struct argo_structure_description *description,
                            argo_structure_callback_f *callback_f,
                            void *callback_cookie);

/* unit test used, not tested in prod */
int argo_update_registration(struct argo_state *argo,
                             struct argo_structure_description *description,
                             argo_structure_callback_f *callback_f,
                             void *callback_cookie);
/*
 * Get a structure description by name. Returns NULL if not found.
 */
struct argo_structure_description * argo_get_structure_description(const char *structure_type);
struct argo_structure_description * argo_get_object_description(struct argo_object *object);

/*
 * Get the type of an argo description.
 */
char *argo_description_get_type(struct argo_structure_description *desc);



/*
 * Serialize a structure based on its description. This will result in
 * callbacks to the write callback.
 *
 * argo - The argo context to use for serialization.
 *
 * description - The compiled description that describes the data
 *      passed in.
 *
 * data - The data to serialized. Described by 'description'
 */
int argo_serialize_structure(struct argo_state *argo,
                             struct argo_structure_description *description,
                             void *structure_data,
                             enum argo_serialize_mode mode);

/*
 * Serialize an object. This will result in callbacks to the write
 * callback. This is very similar to argo_serialize, except that it
 * operates on an argo_object rather than a void * to a structure.
 *
 * argo - The argo context to use for serialization.
 *
 * object - The object to serialize.
 *
 * data - The data to serialized. Described by 'description'
 */
int argo_serialize_object(struct argo_state *argo,
                          struct argo_object *object,
                          enum argo_serialize_mode mode);

/*
 * Send argo_description header. This is needed if you are running
 * argo_tlv mode and want to use binary encoding in tag 0. (Should be
 * the first thing you do on connect)
 */
int argo_send_version(struct argo_state *argo);

/*
 * Deserialize a stream.
 *
 * This routine is used to decode an argo-encoded stream.
 *
 * Bytes from the stream are passed to the deserializer. Once the
 * deserializer returns an error, it will never recover, and must be
 * reinitialized.
 *
 * The deserializer will perform callbacks for each successfully
 * recognized structure. If there is no registered structure for data
 * that is read, then a structure is created (in order to keep
 * deserialization state correct).
 *
 * The deserializer returns (optionally) how many objects were
 * successfully deserialized as a result of this call.
 */
int argo_deserialize(struct argo_state *argo,
                     const void *data,
                     size_t data_len,
                     size_t *objects_found);


void argo_dump_description(struct argo_state *argo,
                           struct argo_structure_description *description);
void argo_dump_all_descriptions(struct argo_state *argo);
/*
 * Deserialize string containing a JSON object into an object. One
 * object max in the string. Uses passed in argo's parse state.
 */
struct argo_object *argo_deserialize_json(const void *data, size_t data_len);


/*
 * Up reference count on object.
 *
 * Note: argo_object reference counting uses atomic
 * increment/decrement, so locking not required. (Still pretty
 * expensive, being atomic)
 */
void argo_object_hold(struct argo_object *object);
/* For calling when referring to a object's structure... Very
 * dangerous, because it uses pointer arithmetic to figure out where
 * the struct argo_object is! Use with EXTREME care! */
void argo_structure_hold(void *structure);

/*
 * Get an argo object reference from a structure that is wholly
 * contained by the argo object. This is just pointer manimupation-
 * use with caution!
 */
static __inline__ struct argo_object *argo_structure_get_object(void *structure)
{
    return (struct argo_object *)(((uint8_t *)structure) - offsetof(struct argo_object, base_structure_void));
}

/*
 * Decrement reference count on object.
 */
void argo_object_release(struct argo_object *object);
/*
 * Decrement reference count on object, return error if
 * expected_end_reference is not equal to reference count after it is
 * dropped.
 */
int argo_object_release_expected(struct argo_object *object, int32_t expected_end_reference);
/* For calling when referring to a object's structure... (Again, Use
 * with EXTREME caution) */
void argo_structure_release(void *structure);

/*
 * Copy an argo object. Returns NULL on failure, or an object with
 * reference count 1. (i.e. one call to release will free the object)
 *
 * This routine only works for objects that have not been changed in
 * size. (i.e. no fields within the object have been lengthened or
 * shortened) It's primary use is for changing a stack-based object
 * into a heap based object. If the object is changed and you want to
 * make a copy of the changed object, use
 * argo_object_create_from_object.
 *
 * This behavior is due to the common programming practice of
 * receiving an object and changing a field (often a string) from one
 * value to another in order to respond to an RPC or some such. It's
 * safe from a memory standpoint to change an object and release it
 * (because that object is a single allocation, with the exception of
 * dictionaries, sub-objects, and lists) But it is not safe to copy a
 * changed object, as the size has now changed. (If the size didn't
 * change then copying is fine...)
 *
 * This sort of boils down to it probably being better to deprecate
 * this routine and always using argo_object_create_from_object, as it
 * is safer. But slower.
 */
struct argo_object *argo_object_copy(struct argo_object *source_object);

/*
 * Create an argo object as a copy of a structure. Returns NULL on
 * failure, or an object with reference count 1.
 */
struct argo_object *argo_object_create(struct argo_structure_description *description,
                                       void *structure);

/*
 * Compare two structures. Only compares the static structure portion,
 * not learned (excess field count) portion
 */
int argo_structure_compare(struct argo_structure_description *d,
                           void *a_structure_data,
                           void *b_structure_data);

/*
 * Create an argo object from another argo object. This copy routine
 * is safe to use if the original object has been modified in it's
 * size- otherwise it's behavior is the same as argo_object_copy. See
 * argo_object_copy for details.
 */
struct argo_object *argo_object_create_from_object(struct argo_object *object);

/* Extract object field */
int argo_object_read_int_by_column_name(struct argo_object *o, char *column_name, int64_t *value);
int argo_structure_read_int_by_column_index(struct argo_structure_description *d, const void *structure_data, int column_index, int64_t *value);
int argo_structure_read_string_by_column_index(struct argo_structure_description *description, void *structure_data, int column_index, char **value);
int argo_object_read_int_by_column_index(struct argo_object *o, int column_index, int64_t *value);
int argo_object_read_string_by_column_name(struct argo_object *o, char *column_name, char **value);
int argo_object_read_string_by_column_index(struct argo_object *o, int column_index, char **value);
int argo_object_read_binary_by_column_name(struct argo_object *o, char *column_name, uint8_t **value, size_t *length);
int argo_object_read_binary_by_column_index(struct argo_object *o, int column_index, uint8_t **value, size_t *length);
int argo_object_get_column_index(struct argo_object *o, char *column_name, int *column_index);
int argo_object_get_column_type_by_index(struct argo_object *o, int column_index, enum argo_field_data_type *data_type);
int argo_object_get_column_is_array_by_index(struct argo_object *o, int column_index, int *is_array);
int argo_object_get_column_count_by_index(struct argo_object *o, int column_index, int *count);
enum argo_field_data_type argo_object_get_column_type_by_name(struct argo_object *o, const char *column_name);
char *argo_object_get_type(struct argo_object *o);

int argo_object_read_array_int_by_column_index(struct argo_object *o, unsigned column_index, unsigned array_index, int64_t *value);
int argo_object_read_array_string_by_column_index(struct argo_object *o, unsigned column_index, unsigned array_index, char **value);
int argo_object_read_array_binary_by_column_index(struct argo_object *o, unsigned column_index, unsigned array_index, uint8_t **value, size_t *length);

/* Extract request id object field. Returns 0 if not there */
int64_t argo_object_read_request_id(struct argo_object *o);
void argo_object_set_request_id(struct argo_object *o, int64_t value);

/* Boolean check if the object is 'deleted' */
int argo_object_is_deleted(struct argo_object *o);

/* Write object field. (ints only, atm) */
int argo_object_write_int_by_column_index(struct argo_object *o, int column_index, int64_t value);
void argo_structure_write_int_by_column_index(struct argo_structure_description *description,
                                              void *structure_data,
                                              int column_index,
                                              int64_t value);

/* Get object sequence. Returns 0 if there is no sequence in the object. */
int64_t argo_object_get_sequence(struct argo_object *o);
int argo_object_set_sequence(struct argo_object *o, int64_t sequence);

/* Get object value for key attribute. Returns 0 if there is no key in the object. */
/* caller must make sure the column type is int, not string */
int64_t argo_object_get_key_int(struct argo_object *o);

/*
 * written_size does not include terminating null character, which is
 * written if there is room, and which is not written if there is not
 * room.
 */
int argo_object_allocate_and_dump(struct argo_object *o, char **destination, size_t *written_size, int pretty);
void argo_object_deallocate_dump(char *mem);
int argo_object_dump(struct argo_object *o, char *destination, size_t destination_length, size_t *written_size, int pretty);
/* dump_allocated returns a string allocated with ARGO_ALLOC. Free it with ARGO_FREE */
char *argo_object_dump_allocated(struct argo_object *o, int pretty);
char *argo_object_dump_inline(struct argo_object *o, char *destination, size_t destination_length, int pretty);
int argo_structure_dump(struct argo_structure_description *description,
                        void *structure_data,
                        char *destination,
                        size_t destination_size,
                        size_t *written_size,
                        int pretty);

/* Returns a string allocated with ARGO_ALLOC. Free it with ARGO_FREE
 * Does not dump any structure (or substructure) names */
char *argo_structure_dump_allocated_no_header(struct argo_structure_description *description,
                                              void *structure_data,
                                              int pretty);

/*
 * @brief	Function to add more than one structures. Addition in done
 *			field wise i.e. struct1.field1 + struct2.field1 + ...
 *			The result is stored at an address provided by
 *			caller of this function.
 *			Structure type must have been registered with argo through
 *			an earlier call to argo_register_global_structure()
 *
 * @param[in] description	argo structure description describing the structures
 *							to be added
 * @param[in] objs			starting address of array of structures to be added
 * @param[in] obj_cnt		number of structure objects in the array
 * @param[out] result_obj	address of the result object
 * @param[in] results_size	size of the memory pointed to by result_obj
 *
 * @return	On success, returns ARGO_RESULT_NO_ERROR
 *			On failure, returns a value > 0 representing error code.
 *
 * */
int argo_structure_array_add(struct argo_structure_description *description,
                             void *objs,
                             uint32_t obj_cnt,
                             void *result_obj,
                             size_t result_size);

/* Dump hex to stderr. */
void argo_hexdump(const uint8_t *data, size_t len, size_t newline_pad);
void argo_hexdump_file(FILE *fp, const uint8_t *data, size_t len, size_t newline_pad);
void argo_hexdump_buf(const uint8_t *data, size_t len, size_t newline_pad, char *dest_buf, size_t dest_buf_len);
void argo_hexdump_buf_with_offset(const uint8_t *data, size_t len, int64_t offset, size_t newline_pad, char *dest_buf, size_t dest_buf_len);

/* Convert null terminated string to inet. */
int argo_string_to_inet(const char *str, struct argo_inet *inet);

/* Convert binsry ip address to inet */
int argo_binary_to_inet(struct argo_inet *dst_inet, uint8_t *addr, size_t addr_len);

/* Convert an inet to a JSON string, optionally with the string's quotes. */
int argo_json_inet_generate(char **out, size_t out_len, const struct argo_inet *inet, int include_quotes);

/* Generate in IP address string in dest_str. Returns
 * dest_str. Caller's responsibility to ensure dest_str is at least
 * INET6_ADDRSTRLEN+4 long!! (REQUIRED, EVEN IF ADDRESS IS 'SHORT
 * ENOUGH' OTHERWISE) This is basically argo_json_inet_generate,
 * without being as safe. */
char *argo_inet_generate(char *dest_str, const struct argo_inet *inet);
/* Serialize a string to JSON. Escapes characters, etc, reasonably well */
int argo_json_string_generate(char **buf, int len, const char *str);
/* Serialize a string to JSON into an argo_buf. Escapes characters, etc, reasonably well */
int argo_json_string_generate_argo_buf(struct argo_buf **buf, const char *str, int have_quotes);
/* Serialize a string to JSON. Escapes characters, etc, reasonably well, but does not add the leading and trailing quotes to the string */
int argo_json_string_generate_no_quotes(char **buf, int len, const char *str);
/* Serialize a string to POSTGRES escaped string. (just escapes '"', nothing else) */
int argo_postgres_string_generate(char **buf, int len, const char *str);
/* Serialize binary to a JSON base-64 string. */
int argo_json_binary_generate(char **buf, int len, const void *bin, size_t bin_len);

/* Returns string containing argo version. */
const char *argo_version(void);

/* Convert sockaddr to argo_inets and back. Caller is responsible for
 * ensuring space correctness. Converting inet to sockaddr requires
 * including a protocol port number, yay. (In network byte order) */
void argo_inet_to_sockaddr(struct argo_inet *inet, struct sockaddr *sock, socklen_t *len, uint16_t port_ne);
void argo_sockaddr_to_inet(struct sockaddr *sock, struct argo_inet *inet, uint16_t *port_ne);

/* Check to see if an inet is a private or publich address */
int argo_inet_is_private(struct argo_inet *inet);

/* Check to see if an inet is in CIDR format */
int argo_inet_is_cidr(const struct argo_inet *inet);

/* Check to see if an inet is a private or publich address */
int argo_inet_is_ipv4_any(struct argo_inet *inet);
int argo_inet_is_ipv6_any(struct argo_inet *inet);

/* Return 1 when child is contained within or equal to parent, 0 otherwise */
int argo_inet_is_contained(struct argo_inet *parent, struct argo_inet *child);

/* Return 1 when inet1 is same as inet2. 0 otherwise */
int argo_inet_is_same(const struct argo_inet *inet1, const struct argo_inet *inet2);

/* Return 1 when lhs < rhs. 0 otherwise */
int argo_inet_is_lt(const struct argo_inet *lhs, const struct argo_inet *rhs);

/* Return 1 when lhs <= rhs. 0 otherwise */
int argo_inet_is_le(const struct argo_inet *lhs, const struct argo_inet *rhs);

/* Return 1 when lhs > rhs. 0 otherwise */
int argo_inet_is_gt(const struct argo_inet *lhs, const struct argo_inet *rhs);

/* Return 1 when lhs >= rhs. 0 otherwise */
int argo_inet_is_ge(const struct argo_inet *lhs, const struct argo_inet *rhs);

/* Return 1 when begin <= inet <= end. 0 otherwise */
static inline int argo_inet_is_in_range(const struct argo_inet *inet,
                                        const struct argo_inet *begin,
                                        const struct argo_inet *end)
{
    return argo_inet_is_ge(inet, begin) && argo_inet_is_le(inet, end);
}

/* Return 1 if all the bytes in inet->address are 0xff. 0 otherwise */
int argo_inet_is_max(const struct argo_inet *inet);

/* Return 1 if all the bytes in inet->address are 0x00. 0 otherwise */
int argo_inet_is_min(const struct argo_inet *inet);

/* Increment inet->address */
int argo_inet_inc(struct argo_inet *inet);

/* Decrement inet->address */
int argo_inet_dec(struct argo_inet *inet);

/* Return the number of argo structures that have been defined */
int argo_structures_defined(void);

/* Return data about the specified structure. */
void argo_structure_info(int argo_structure_index,
                         char **name,
                         int64_t *bytes_allocated,
                         int64_t *allocations,
                         int64_t *bytes_freed,
                         int64_t *frees);

int argo2_binary_serialize_structure(struct argo_state *argo,
                                     struct argo_structure_description *description,
                                     void *struct_data,
                                     struct argo_buf **buf,
                                     size_t total_fields);

/*
 * If successful, returns an argo_object with reference count 1
 */
int argo2_binary_deserialize_to_argo_object(struct argo_state *argo,
                                            struct argo_buf **read_buf,
                                            struct argo_object **object);

/*
 * Deserialize into space already available for deserialization. The
 * description is just used for type verification
 *
 * Will never deserialize into more memory than is
 */
int argo2_binary_deserialize_to_structure(struct argo_state *argo,
                                          struct argo_buf **read_buf,
                                          struct argo_structure_description *description,
                                          void *struct_data);

int argo2_binary_deserialize_argo_object(struct argo_state *argo,
                                         struct argo_buf **read_buf,
                                         struct argo_object **object,
                                         struct argo_buf *heap_buf);

char *argo_field_data_type_to_str(enum argo_field_data_type t);

/* Check if the argo transmits binary. Returns true/false. If argo is NULL, returns false */
int argo_transmits_binary(struct argo_state *argo);

/*
 * This will set a value (GLOBALLY) in argo that will cause it to
 * adjust any integer value marked up with 'adj_time' to have time_us
 * added to it during serialization. (i.e. does not change data in
 * place- just the serialized data is adjusted)
 */
void argo_set_adj_time(int64_t time_us);

void argo_structure_dump_dynamic_fields(char *buf, size_t len);

/*
 * When set (non-0): Tells argo that global structure registration should no longer happen.
 * - Argo prints an error message if you register global structure afterwards.
 * - Optionally takes a callback function if the program wants customized behavior, e.g. assert.
 * Note: This is used to catch initialization sequence issues, for example, started listening
 * for client events before argo structures are registered.
 */
typedef void (argo_structure_registration_barrier_f)(const char *structure_type,
                                                                int is_dictionary,
                                                                size_t structure_size,
                                                                const struct argo_field_description *description,
                                                                size_t description_count);
void argo_structure_set_registration_barrier(int set, argo_structure_registration_barrier_f* cb);
extern uint64_t argo_debug;
extern uint64_t argo_debug_catch_defaults;
extern const char *argo_debug_names[];

struct st_argo_holder {
    struct argo_object *object;
    void *cookie;
};

#define Z_TRUE (1)
#define Z_FALSE (0)

#endif /* __ARGO_H */
