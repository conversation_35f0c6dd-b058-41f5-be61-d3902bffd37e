/*
 * argo_buf.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Simple buffering scheme for argo encoding/decoding.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include "base64/base64.h"
#include "argo/argo_encode.h"
#include "argo/argo_buf.h"
#include "zpath_misc/zpath_misc.h"

int argo_buf_dump_append = 0;

//#define DUMP_LOTS

static __inline__ int skip_char(struct argo_buf **buf, uint8_t **walk);
static int skip_whitespace(struct argo_buf **buf, uint8_t **walk);
static int skip_string(struct argo_buf **buf, uint8_t **walk, size_t *length);
static int skip_array(struct argo_buf **buf, uint8_t **walk);
static int skip_object(struct argo_buf **buf, uint8_t **walk);
static int skip_number(struct argo_buf **buf, uint8_t **walk);
static int skip_value(struct argo_buf **buf, uint8_t **walk);

void argo_buf_dump(struct argo_buf *buf, int pad, ssize_t max)
{
    // skip irrelevant bufs...
    if (max) while (buf && buf->next && (buf->current == buf->last)) buf = buf->next;

    if (!max) max = 0x7ffffff;

    while (buf && (max > 0)) {
        const uint8_t *s = buf->start;
        const uint8_t *e = buf->last;
        int width;
        int i;

        s = (uint8_t *)(((uintptr_t)s) & (~0xf));

        if (s == e) fprintf(stderr, "\n");

        while ((s < e) && (max > 0)) {
            width = 16;
            if ((s + width) > e) width = e-s;


            for (i = 0; i < width; i++) {
                max--;
                if (((s + i) < buf->start) || ((s + i) > buf->last)) {
                    fprintf(stderr, "   ");
                } else {
                    if ((s + i) >= buf->current) {
                        fprintf(stderr, "\033[32;3m");
                    }
                    fprintf(stderr, "%02x ", s[i]);
                }
                if (i == 7) fprintf(stderr, " ");
            }
            fprintf(stderr, "\033[0m");
            for (;i < 16; i++) {
                fprintf(stderr, "   ");
                if (i == 7) fprintf(stderr, " ");
            }

            for (i = 0; i < width; i++) {
                if (((s + i) < buf->start) || ((s + i) > buf->last)) {
                    fprintf(stderr, " ");
                } else {
                    if ((s + i) >= buf->current) {
                        fprintf(stderr, "\033[32;3m");
                    }
                    fprintf(stderr, "%c", isprint(s[i]) ? s[i]:'.');
                }
            }
            fprintf(stderr, "\033[0m");
            fprintf(stderr, "\n");
            s += width;
            if (s < e) {
                fprintf(stderr, "%*.s", pad, " ");
            }
        }
        buf = buf->next;
    }
    fprintf(stderr, "\033[0m");
}

void argo_buf_dump_o(struct argo_buf *buf, int pad, size_t max)
{
    const uint8_t *s = buf->start;
    const uint8_t *e = buf->last;
    int width;
    int i;

    s = (uint8_t *)(((uintptr_t)s) & (~0xf));

    if ((e - s) > max) e = s + max;

    if (s == e) fprintf(stderr, "\n");

    while (s < e) {
        width = 16;
        if ((s + width) > e) width = e-s;


        for (i = 0; i < width; i++) {
            if (((s + i) < buf->start) || ((s + i) > buf->last)) {
                fprintf(stderr, "   ");
            } else {
                if ((s + i) >= buf->current) {
                    fprintf(stderr, "\033[32;3m");
                }
                fprintf(stderr, "%02x ", s[i]);
            }
            if (i == 7) fprintf(stderr, " ");
        }
        fprintf(stderr, "\033[0m");
        for (;i < 16; i++) {
            fprintf(stderr, "   ");
            if (i == 7) fprintf(stderr, " ");
        }

        for (i = 0; i < width; i++) {
            if (((s + i) < buf->start) || ((s + i) > buf->last)) {
                fprintf(stderr, " ");
            } else {
                if ((s + i) >= buf->current) {
                    fprintf(stderr, "\033[32;3m");
                }
                fprintf(stderr, "%c", isprint(s[i]) ? s[i]:'.');
            }
        }
        fprintf(stderr, "\033[0m");
        fprintf(stderr, "\n");
        s += width;
        if (s < e) {
            fprintf(stderr, "%*.s", pad, " ");
        }
    }
    fprintf(stderr, "\033[0m");
}

void argo_buf_dump_straight(struct argo_buf *buf, size_t max)
{
    uint8_t *w;

    while(buf && max) {
        fprintf(stdout, "\033[31m"); // Red for old stuff
        for (w = buf->start; max && (w < buf->current); w++) {
            fprintf(stdout, " %02x", *w);
            max--;
        }
        fprintf(stdout, "\033[32m"); // Green for new stuff
        for (w = buf->current; max && (w < buf->last); w++) {
            fprintf(stdout, " %02x", *w);
            max--;
        }
        buf = buf->next;
    }
    fprintf(stdout, "\033[0m");
}

void argo_buf_dump_near(struct argo_buf *buf, int pad, size_t max)
{
    const uint8_t *s = buf->start;
    const uint8_t *e = buf->last;
    int width;
    int i;

    s = (uint8_t *)(((uintptr_t)buf->current - max) & (~0xf));
    e = (uint8_t *)(((uintptr_t)buf->current + max + 16) & (~0xf));

    if (s == e) fprintf(stderr, "\n");

    while (s < e) {
        width = 16;
        if ((s + width) > e) width = e-s;


        for (i = 0; i < width; i++) {
            if (((s + i) < buf->start) || ((s + i) > buf->last)) {
                fprintf(stderr, "   ");
            } else {
                if ((s + i) >= buf->current) {
                    fprintf(stderr, "\033[32;3m");
                }
                fprintf(stderr, "%02x ", s[i]);
            }
            if (i == 7) fprintf(stderr, " ");
        }
        fprintf(stderr, "\033[0m");
        for (;i < 16; i++) {
            fprintf(stderr, "   ");
            if (i == 7) fprintf(stderr, " ");
        }

        for (i = 0; i < width; i++) {
            if (((s + i) < buf->start) || ((s + i) > buf->last)) {
                fprintf(stderr, " ");
            } else {
                if ((s + i) >= buf->current) {
                    fprintf(stderr, "\033[32;3m");
                }
                fprintf(stderr, "%c", isprint(s[i]) ? s[i]:'.');
            }
        }
        fprintf(stderr, "\033[0m");
        fprintf(stderr, "\n");
        s += width;
        if (s < e) {
            fprintf(stderr, "%*.s", pad, " ");
        }
    }
    fprintf(stderr, "\033[0m");
}

/*
 * Read one character out of buffer.
 *
 * returns char, or < 0 if no data available.
 */
int argo_buf_read_char(struct argo_buf **buf)
{
    int c;

    while ((*buf)->current == (*buf)->last) {
        if (!(*buf)->next) {
            return -1;
        }
        (*buf) = (*buf)->next;
        (*buf)->current = (*buf)->start;
    }
    c = *((*buf)->current);
    ((*buf)->current)++;
    while ((*buf)->current == (*buf)->last) {
        if (!(*buf)->next) {
            return c;
        }
        (*buf) = (*buf)->next;
        (*buf)->current = (*buf)->start;
    }
    return c;
}

int argo_decode_uint64(uint8_t **data, uint8_t *data_end, int v_bit, uint64_t *value)
{
    uint8_t *walk = *data;
    uint64_t result = 0;
    uint8_t bit;
    int offset = 0;

    bit = 1 << v_bit;
    while (walk < data_end) {
        result |= ((uint64_t)(*walk) & ((uint64_t)bit - 1)) << (uint64_t)offset;
        if (!(*walk & bit)) {
            *data = walk + 1;
            *value = result;
            return ARGO_RESULT_NO_ERROR;
        }
        walk++;
        offset += v_bit;
        if (offset > 63) return ARGO_RESULT_BAD_ARGUMENT;
        v_bit = 7;
        bit = 0x80;
    }
    return ARGO_RESULT_INSUFFICIENT_DATA;
}

extern struct zpath_allocator argo_allocator;

void argo_aligned_count_stats(size_t sz, int alloc)
{
    if (alloc) {
      __sync_add_and_fetch_8(&(argo_allocator.stats.allocations), 1);
      __sync_add_and_fetch_8(&(argo_allocator.stats.allocation_bytes), sz);
    } else {
      __sync_add_and_fetch_8(&(argo_allocator.stats.frees), 1);
      __sync_add_and_fetch_8(&(argo_allocator.stats.free_bytes), sz);
    }
}

void *argo_malloc_aligned(size_t sz, size_t *allocated)
{
    size_t total_mem = 0;
    if (!zpath_dont_dump_get()) {
      if (allocated)
         *allocated = sz;
      return zpath_malloc(&argo_allocator, sz, __LINE__, __FILE__);
    }
    void *data = zpath_malloc_aligned(sz, allocated, &total_mem);
    assert(data);
    argo_aligned_count_stats(total_mem, 1);
    return data;
}

void argo_free_aligned(void *data)
{
    if (!data) return;
    size_t total_mem = 0;
    zpath_free_aligned(data, &total_mem);
    if (total_mem)
      argo_aligned_count_stats(total_mem, 0);
}

/*
 * Create (sometimes allocating) a single buf.
 */
struct argo_buf *argo_buf_create(size_t padding, struct argo_buf *buf, uint8_t *buffer, size_t bytes)
{
    if (buf) {
        buf->allocated_buf = 0;
    } else {
        buf = (struct argo_buf *) ARGO_ALLOC(sizeof(struct argo_buf));
        if (!buf) return NULL;
        buf->allocated_buf = 1;
    }
    if (buffer) {
        buf->buffer = buffer;
        buf->allocated_buffer = 0;
    } else {
        buf->buffer = argo_malloc_aligned(bytes, NULL);
        buf->allocated_buffer = 1;
        if (!buf->buffer) {
            ARGO_FREE(buf);
            return NULL;
        }
    }
    buf->buffer_len = bytes;
    buf->next = NULL;
    buf->last = buf->start = buf->current = &(buf->buffer[padding]);

    //fprintf(stderr, "buf_alloc: %p\n", buf);
    return buf;
}


/*
 * Simple initializer- note: Doesn't need to clear buffer memory.
 */
void argo_buf_init_xx(struct argo_buf *buf, size_t padding)
{
    buf->next = NULL;
    buf->last = buf->start = buf->current = &(buf->buffer[padding]);
}

int argo_buf_prepend(struct argo_buf *buf, const void *data, size_t len)
{
    if (len > (size_t)(buf->start - &(buf->buffer[0]))) {
        fprintf(stderr, "%s:%s:%d: Err, too large\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    buf->start -= len;
    memcpy(buf->start, data, len);
    return ARGO_RESULT_NO_ERROR;
}

/*
 * Append data to the buffer. This routine allocates more buffer space as necessary.
 */
int argo_buf_append(struct argo_buf **buf, const void *data, size_t len)
{
    uint8_t *data8 = (uint8_t *)data;
    uint8_t *end_of_buffer = &((*buf)->buffer[(*buf)->buffer_len]);

#if 0
    if (argo_buf_dump_append) {
        fprintf(stderr, "Appending: ");
        fprintf(stderr, "\033[31;3m");
        argo_hexdump(data, len, 11);
        fprintf(stderr, "\033[0m");
    }
#endif // 0

    do {
        if (((*buf)->last + len) < end_of_buffer) {
            memcpy((*buf)->last, data8, len);
            (*buf)->last += len;
            return ARGO_RESULT_NO_ERROR;
        } else {
            int i = end_of_buffer - (*buf)->last;
            if (i == 0) {
                if (!(*buf)->next) {
                    (*buf)->next = argo_buf_create(0, NULL, NULL, ARGO_BUF_DEFAULT_SIZE);
                    if (!(*buf)->next) {
                        return ARGO_RESULT_NO_MEMORY;
                    }
                }
                *buf = (*buf)->next;
                end_of_buffer = &((*buf)->buffer[(*buf)->buffer_len]);
            } else {
                memcpy((*buf)->last, data8, i);
                data8 += i;
                len -= i;
                (*buf)->last += i;
            }
        }
    } while (len);
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_append_printf(struct argo_buf **buf, const char *format, ...)
{
    va_list list;
    va_start(list, format);
    char tmp_buf[100];
    char *alloc_buf;
    int len;

    len = vsnprintf(tmp_buf, sizeof(tmp_buf), format, list);
    if (len < 0) {
        va_end(list);
        return ARGO_RESULT_ERR;
    }

    if (len >= sizeof(tmp_buf)) {
        /* need moar buf! */
        va_end(list);
        va_start(list, format);
        alloc_buf = ARGO_ALLOC(len + 1);
        if (vsnprintf(alloc_buf, len + 1, format, list) != len) {
            /* WTF? */
            ARGO_FREE(alloc_buf);
            va_end(list);
            return ARGO_RESULT_ERR;
        }
        argo_buf_append(buf, alloc_buf, len);
        ARGO_FREE(alloc_buf);
    } else {
        argo_buf_append(buf, tmp_buf, len);
    }

    va_end(list);
    return ARGO_RESULT_NO_ERROR;
}


int argo_buf_append_v_uint(struct argo_buf **buf, uint8_t varint_byte, uint8_t v_bit, uint64_t i)
{
    uint8_t tmp[12];
    uint8_t *w = tmp;
    tmp[0] = varint_byte;
    argo_encode_uint64(&w, v_bit, i);
    return argo_buf_append(buf, tmp, w - tmp);
}

int argo_buf_append_v_int(struct argo_buf **buf, uint8_t varint_byte, uint8_t v_bit, int64_t i)
{
    uint8_t tmp[12];
    uint8_t *w = tmp;
    tmp[0] = varint_byte;
    argo_encode_int64(&w, v_bit, i);
    return argo_buf_append(buf, tmp, w - tmp);
}


/*
 * See header
 */
void argo_buf_reset(struct argo_buf *buf)
{
    buf->current = buf->start;
}


/*
 * See header
 */
int argo_buf_peek(struct argo_buf *buf)
{
    while (buf) {
        if (buf->current == buf->last) {
            buf = buf->next;
            continue;
        }
#ifdef DUMP_LOTS
        fprintf(stderr, "Peek char: \033[31;3m %02x \033[0m\n", *(buf->current));
#endif
        return *(buf->current);
    }
#ifdef DUMP_LOTS
    fprintf(stderr, "Peek char: \033[31;3m EOF \033[0m\n");
#endif
    return -1;
}

/*
 * See header
 */
int argo_buf_get(struct argo_buf **buf)
{
    uint8_t ret;

    struct argo_buf *walk = *buf;
    if (walk->current == walk->last) {
#ifdef DUMP_LOTS
        fprintf(stderr, "Get char: \033[31;3m EOF \033[0m\n");
#endif
        return -1;
    }

    ret = *(walk->current);
    walk->current++;
    if (walk->current == walk->last) {
        if (walk->next) {
            *buf = walk->next;
            (*buf)->current = (*buf)->start;
        }
    }
#ifdef DUMP_LOTS
    fprintf(stderr, "Get char: \033[31;3m %02x \033[0m\n", ret);
#endif
    return ret;
}

/*
 * See header.
 */
int argo_buf_bulk_get(struct argo_buf **buf, uint8_t **out_data, size_t *out_data_len)
{
    while (*buf) {
        if ((*buf)->last - (*buf)->start) {
            *out_data = (*buf)->start;
            *out_data_len = (*buf)->last - (*buf)->start;
            *buf = (*buf)->next;
            return ARGO_RESULT_NO_ERROR;
        }
        *buf = (*buf)->next;
    }
    *out_data_len = 0;
    return ARGO_RESULT_NO_ERROR;
}


/*
 * See header
 */
int argo_buf_remove_from_front(struct argo_buf **buf, size_t len)
{
    struct argo_buf *walk;
    struct argo_buf *tmp;

    walk = *buf;

    while (len) {
        if (!walk) return ARGO_RESULT_INSUFFICIENT_DATA;
        if ((size_t)(walk->last - walk->start) >= len) {
            walk->start += len;
            if (walk->last == walk->start) {
                if (walk->next) {
                    tmp = walk;
                    walk = walk->next;
                    argo_buf_free(tmp);
                    *buf = walk;
                } else {
                    walk->start = walk->last = walk->current = &(walk->buffer[0]);
                    *buf = walk;
                }
            }
            if (walk->current < walk->start) {
                walk->current = walk->start;
            }
            return ARGO_RESULT_NO_ERROR;
        }
        len -= walk->last - walk->start;
        if (!walk->next) {
            walk->current = walk->start = walk->last = &(walk->buffer[0]);
            if (len) return ARGO_RESULT_INSUFFICIENT_DATA;
            break;
        }
        tmp = walk;
        walk = walk->next;
        *buf = walk;
        argo_buf_free(tmp);
    }
    return ARGO_RESULT_NO_ERROR;
}


/*
 * See header
 */
int argo_buf_collapse_front(struct argo_buf **buf)
{
    struct argo_buf *tmp;
    int count = 0;

    while (*buf) {
        if ((*buf)->current == (*buf)->start) return count;
        count += ((*buf)->current - (*buf)->start);
        (*buf)->start = (*buf)->current;
        if ((*buf)->current != (*buf)->last) return count;
        if ((*buf)->next) {
            tmp = *buf;
            *buf = tmp->next;
            argo_buf_free(tmp);
        } else {
            /* Always leave some space. If this is the only buffer and
             * it's empty, reset it to the front. We can only get here
             * if current == last, and there is no next buffer. */
            (*buf)->last = (*buf)->current = (*buf)->start = &((*buf)->buffer[0]);
        }
    }
    return count;
}



/*
 * Flush buffer(s) out, using the specified write function.
 */
int argo_buf_write(struct argo_state *argo, struct argo_buf *buf)
{
    int res;
    if (argo->bulk_callback) {
        res = (*argo->bulk_callback)(&buf, argo->callback_cookie, argo->callback_count);
        argo->callback_count++;
    } else {
        while (buf) {
            res = argo_write(argo, buf->start, buf->last - buf->start);
            if (res) return res;
            buf = buf->next;
        }
    }
    return ARGO_RESULT_NO_ERROR;
}


/*
 * Free the specified buffer.
 *
 * BE VERY CAREFUL- this frees just the buffer, not any buffers it
 * links to.
 */
void argo_buf_free(struct argo_buf *buf)
{
    //fprintf(stderr, "buf_free : %p\n", buf);
    if (!buf) return;

    if (buf->allocated_buffer) {
        argo_free_aligned(buf->buffer);
    }
    if (buf->allocated_buf) {
        ARGO_FREE(buf);
    }
}

/*
 * Free the specified buffers.
 */
void argo_buf_free_all(struct argo_buf *buf)
{
    struct argo_buf *tmp;
    while (buf) {
        tmp = buf->next;
        //fprintf(stderr, "buf_free : %p\n", buf);
        argo_buf_free(buf);
        buf = tmp;
    }
}

/*
 * Get the size of all the buffers. Holy linear-search, batman!
 */
size_t argo_buf_size(struct argo_buf *buf)
{
    size_t count = 0;
    while (buf) {
        count += buf->last - buf->start;
        buf = buf->next;
    }
    return count;
}

/*
 * Read in integer out of a buffer.
 *
 * This routine advances the buffer read state as it parses.
 */
int argo_buf_read_encoded_uint64(struct argo_buf **buf,
                                 int v_bit,
                                 uint64_t *bytes_read,
                                 uint64_t *value_read)
{
    uint64_t result = 0;
    uint8_t bit;
    int offset = 0;
    uint64_t count = 0;

    bit = 1 << v_bit;
    while (((*buf)->current == (*buf)->last) && (*buf)->next) {
        (*buf) = (*buf)->next;
    }
    while ((*buf)->current < (*buf)->last) {
        result |= ((uint64_t)(*((*buf)->current)) & ((uint64_t)bit - 1)) << (uint64_t)offset;
        if (!(*((*buf)->current) & bit)) {
            (*buf)->current++;
            count++;
            if ((*buf)->current == (*buf)->last) {
                if ((*buf)->next) {
                    (*buf) = (*buf)->next;
                    (*buf)->current = (*buf)->start;
                }
            }
            *value_read = result;
            if (bytes_read) *bytes_read = count;
#ifdef DUMP_LOTS
            fprintf(stderr, "Read uint64: \033[31;3m %ld \033[0m\n", (long)result);
#endif
            return ARGO_RESULT_NO_ERROR;
        }
        (*buf)->current++;
        count++;
        offset += v_bit;
        if (offset > 63) return ARGO_RESULT_BAD_ARGUMENT;
        v_bit = 7;
        bit = 0x80;
        if ((*buf)->current == (*buf)->last) {
            if (!((*buf)->next)) {
                return ARGO_RESULT_INSUFFICIENT_DATA;
            }
            if ((*buf)->next) {
                (*buf) = (*buf)->next;
                (*buf)->current = (*buf)->start;
            }
        }
    }
    return ARGO_RESULT_INSUFFICIENT_DATA;
}

/*
 * Read in integer out of a buffer.
 *
 * This routine advances the buffer read state as it parses.
 */
int argo_buf_peek_encoded_uint64(struct argo_buf *buf,
                                 int64_t skips,
                                 int v_bit,
                                 uint64_t *bytes_read,
                                 uint64_t *value_read)
{
    uint64_t result = 0;
    uint8_t bit;
    int offset = 0;
    uint64_t count = 0;

    uint8_t *current = buf->current;

    while (skips) {
        if (current < buf->last) {
            if (skips < (buf->last - current)) {
                current += skips;
                skips = 0;
                break;
            } else {
                skips -= (buf->last - current);
                if (buf->next) {
                    buf = buf->next;
                    current = buf->start;
                } else {
                    return ARGO_RESULT_INSUFFICIENT_DATA;
                }
            }
        }
    }


    bit = 1 << v_bit;
    while (current < buf->last) {
        result |= ((uint64_t)(*current) & ((uint64_t)bit - 1)) << (uint64_t)offset;
        if (!((*current) & bit)) {
            current++;
            count++;
            if (current == buf->last) {
                if (buf->next) {
                    buf = buf->next;
                    current = buf->start;
                }
            }
            *value_read = result;
            if (bytes_read) *bytes_read = count;
            //fprintf(stderr, "Read uint64: %lld\n", *value_read);
            return ARGO_RESULT_NO_ERROR;
        }
        current++;
        count++;
        offset += v_bit;
        if (offset > 63) return ARGO_RESULT_BAD_ARGUMENT;
        v_bit = 7;
        bit = 0x80;
        if (current == buf->last) {
            if (!buf->next) {
                return ARGO_RESULT_INSUFFICIENT_DATA;
            } else {
                buf = buf->next;
                current = buf->start;
            }
        }
    }
    return ARGO_RESULT_INSUFFICIENT_DATA;
}


/*
 * Read a signed integer out of a buffer.
 */
int argo_buf_read_encoded_int64(struct argo_buf **buf, int v_bit, uint64_t *bytes_read, int64_t *value_read)
{
    int is_negative = 0;
    int res;

    if (*((*buf)->current) & (1 << v_bit)) {
        is_negative = 1;
    }
    v_bit--;

    res = argo_buf_read_encoded_uint64(buf, v_bit, bytes_read, (uint64_t *)value_read);
    if (res) return res;

    if (is_negative) *value_read = 0 - (*value_read);

    //fprintf(stderr, "Read  int64: %lld\n", *value_read);

    return res;
}

/*
 * See header
 */
int argo_buf_read_encoded_string(struct argo_buf **buf, void *data, size_t *data_len)
{
    uint8_t *walk = (*buf)->current;
    uint8_t *end = (*buf)->last;
    uint8_t *out = (uint8_t *) data;
    uint8_t *out_end = out + *data_len;

    while ((walk < end) && (out < out_end)) {
        *out = *walk;
        if (*out & 0x80) {
            /* Terminal. */
            *out &= 0x7f;

            /* Special case- if NULL, don't append last byte. This
               allows for clean empty string encoding. But we still
               consume it from input buffer. */
            walk++;
            if (*out) {
                out++;
            }
            if (walk == end) {
                if ((*buf)->next) {
                    (*buf)->current = walk;
                    *buf = (*buf)->next;
                    walk = (*buf)->current = (*buf)->start;
                }
            }
            *data_len = out - (uint8_t *) data;
            (*buf)->current = walk;
            //fprintf(stderr, "Read string: <%.*s>\n", (int)*data_len, (char *)data);
            return ARGO_RESULT_NO_ERROR;
        }
        out++;
        walk++;
        if (walk == end) {
            if ((*buf)->next) {
                (*buf)->current = walk;
                *buf = (*buf)->next;
                walk = (*buf)->current = (*buf)->start;
                end = (*buf)->last;
            }
        }
    }
    if (walk >= end) {
        return ARGO_RESULT_INSUFFICIENT_DATA;
    } else {
        argo_buf_dump_near(*buf, 0, 64);
        fprintf(stderr, "%s:%s:%d: Err, too large\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
}


/*
 * See header
 */
int argo_buf_read_binary(struct argo_buf **buf, void *data, size_t data_len)
{
    uint8_t *walk = (*buf)->current;
    uint8_t *end = (*buf)->last;
    uint8_t *out = (uint8_t *) data;
    uint8_t *out_end = out + data_len;

    while ((walk < end) && (out < out_end)) {
        *out = *walk;
        out++;
        walk++;
        if (walk == end) {
            if ((*buf)->next) {
                (*buf)->current = walk;
                *buf = (*buf)->next;
                walk = (*buf)->current = (*buf)->start;
                end = (*buf)->last;
            }
        }
    }
    if (out < out_end) {
        return ARGO_RESULT_INSUFFICIENT_DATA;
    }
    (*buf)->current = walk;
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_peek_binary(struct argo_buf *buf, uint64_t offset, void *data, size_t data_len)
{
    uint8_t *walk = buf->current;
    uint8_t *end = buf->last;
    uint8_t *out = (uint8_t *) data;
    uint8_t *out_end = out + data_len;

    /* Search to offset */
    while (offset) {
        if (walk < end) {
            if (offset < (end - walk)) {
                walk += offset;
                offset = 0;
                break;
            } else {
                offset -= (end - walk);
                if (buf->next) {
                    buf = buf->next;
                    walk = buf->start;
                    end = buf->last;
                } else {
                    return ARGO_RESULT_INSUFFICIENT_DATA;
                }
            }
        }
    }

    /* Copy out. */
    while ((walk < end) && (out < out_end)) {
        *out = *walk;
        out++;
        walk++;
        if (walk == end) {
            if (buf->next) {
                buf = buf->next;
                walk = buf->start;
                end = buf->last;
            } else {
                break;
            }
        }
    }
    if (out < out_end) {
        return ARGO_RESULT_INSUFFICIENT_DATA;
    }
    return ARGO_RESULT_NO_ERROR;
}


/* Must return reference to a valid character, else insufficient data */
static __inline__ int skip_char(struct argo_buf **buf, uint8_t **walk)
{
    if ((*walk) < ((*buf)->last - 1)) {
        (*walk)++;
    } else {
        if ((*buf)->next) {
            *buf = (*buf)->next;
            *walk = (*buf)->start;
            if ((*walk) >= ((*buf)->last)) {
                return ARGO_RESULT_INSUFFICIENT_DATA;
            }
        } else {
            return ARGO_RESULT_INSUFFICIENT_DATA;
        }
    }
    return ARGO_RESULT_NO_ERROR;
}

static int skip_whitespace(struct argo_buf **buf, uint8_t **walk)
{
    while (isspace(**walk) || ((**walk) == ',') || ((**walk) == ':')) {
        if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    }
    return ARGO_RESULT_NO_ERROR;
}

static int skip_string(struct argo_buf **buf, uint8_t **walk, size_t *length)
{
    size_t len = 0;
    if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    while ((**walk) != '"') {
        if ((**walk) == '\\') {
            if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
            if ((**walk) == 'u') {
                if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
                if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
                if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
                if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
                len += 3; /* XXX : This is an overestimate of the length for many UTF characters!!! */
            }
            if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        } else {
            /* Track length correctly for characters in JSON that were
             * NOT escaped, that when written as JSON WILL be escaped
             * by our code */
            switch (**walk) {
            case '\b':
            case '\f':
            case '\n':
            case '\r':
            case '\t':
                len++;
            default:
                break;
            }
            if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        }
        len++;
    }
    if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    if (length) *length = len;
    return ARGO_RESULT_NO_ERROR;
}

static int skip_array(struct argo_buf **buf, uint8_t **walk)
{
    if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    while ((**walk) != ']') {
        if (skip_whitespace(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        if ((**walk) == ']') break;
        if (skip_value(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (skip_whitespace(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    }
    if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    return ARGO_RESULT_NO_ERROR;
}

static int skip_object(struct argo_buf **buf, uint8_t **walk)
{
    if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    while ((**walk) != '}') {
        if (skip_whitespace(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        if ((**walk) == '}') break;
        if (skip_string(buf, walk, NULL)) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (skip_whitespace(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (skip_value(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (skip_whitespace(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    }
    if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    return ARGO_RESULT_NO_ERROR;
}

static int skip_number(struct argo_buf **buf, uint8_t **walk)
{
    while (isalnum(**walk) ||
           ((**walk) == '.') ||
           ((**walk) == '+') ||
           ((**walk) == '-')) {
        if (skip_char(buf, walk)) return ARGO_RESULT_INSUFFICIENT_DATA;
    }
    return ARGO_RESULT_NO_ERROR;
}

static int skip_value(struct argo_buf **buf, uint8_t **walk)
{
    uint8_t *old_walk = *walk;
    int res;

    if ((**walk) == '"') {
        res = skip_string(buf, walk, NULL);
    } else if ((**walk) == '[') {
        res = skip_array(buf, walk);
    } else if ((**walk) == '{') {
        res = skip_object(buf, walk);
    } else {
        res = skip_number(buf, walk);
    }

    if (res) {
        return res;
    } else if (old_walk == *walk) {
        return ARGO_RESULT_BAD_DATA;
    } else {
        return ARGO_RESULT_NO_ERROR;
    }
}


int argo_buf_peek_json_array_count(struct argo_buf *buf, size_t *count)
{
    uint8_t *walk = buf->current;
    int res;
    size_t cnt = 0;

    /* We might start right at the end of a buf... */
    if (buf->current >= buf->last) {
        res = skip_char(&buf, &walk);
        if (res) return res;
    }

    /* The first '[' has already been read... */
    while ((*walk) != ']') {
        res = skip_whitespace(&buf, &walk);
        if (res) return res;
        if ((*walk) == ']') break;
        res = skip_value(&buf, &walk);
        if (res) return res;
        cnt++;
    }

    *count = cnt;
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_peek_json_string_length(struct argo_buf *buf, size_t *count)
{
    uint8_t *walk = buf->current;
    int res;

    res = skip_whitespace(&buf, &walk);
    if (res) return res;
    return skip_string(&buf, &walk, count);
}

/*
 * See header
 */
int argo_buf_peek_json_type(struct argo_buf **buf, enum argo_field_data_type *data_type, int *is_array_initiator, int *is_array_terminator, int *is_null_data)
{
    struct argo_buf *buf_walk = *buf;
    uint8_t *walk;

    *is_array_initiator = 0;
    *is_array_terminator = 0;
    *is_null_data = 0;

    walk = buf_walk->current;

 reread_type:
    do {
        while (walk == buf_walk->last) {
            if (!buf_walk->next) {
                return ARGO_RESULT_INSUFFICIENT_DATA;
            }
            buf_walk = buf_walk->next;
            walk = buf_walk->start;
        }
        if (isspace(*walk) || ((*walk) == 0) || ((*walk) == ',')) {
            walk++;
        } else {
            break;
        }
    } while (1);
    switch (*walk) {
    case '[':
        /* XXX Choose a better data type. */
        *is_array_initiator = 1;
        walk++;
        goto reread_type;
    case ']':
        *data_type = argo_field_data_type_integer;
        *is_array_terminator = 1;
        break;
    case '{':
    case '}':
        *data_type = argo_field_data_type_argo_object;
        break;
    case 'n':
        *is_null_data = 1;
        /* Fall Through */
    case '"':
        *data_type = argo_field_data_type_string;
        break;
    case '-':
    case '0':
    case '1':
    case '2':
    case '3':
    case '4':
    case '5':
    case '6':
    case '7':
    case '8':
    case '9':
    case 't':
    case 'f':
        *data_type = argo_field_data_type_integer;
        break;
    default:
        fprintf(stdout, "Bad data, file %s, line %d, value = %02x:<%c>\n", __FILE__, __LINE__, *walk, *walk);
        return ARGO_RESULT_BAD_DATA;
    }
    return ARGO_RESULT_NO_ERROR;
}

/*
 * See header
 */
int argo_buf_require_char(struct argo_buf **buf, char required)
{
    int c;
    while (1) {
        c = argo_buf_peek(*buf);
        if (c < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (isspace(c) || (c == 0) || (c == ',')) {
            c = argo_buf_read_char(buf);
            continue;
        }
        if (c != required) {
            return ARGO_RESULT_NOT_FOUND;
        }
        c = argo_buf_read_char(buf);
        return ARGO_RESULT_NO_ERROR;
    }
}

/*
 * Assumes we have read "\u" of the json utf. Note that json UTF must
 * support supplementary planes, thus it is looking for up to two
 * utf16 words.
 *
 * This code is largely adapted from parson- it just reads from
 * argo_buf instead.
 */
static int argo_buf_read_json_string_utf(struct argo_buf **buf, char *dest, size_t dest_length, size_t *written_length)
{
    uint8_t data[5];
    int res;
    int cp1, cp2;

    res = argo_buf_read_binary(buf, data, 4);
    if (res) return res;
    data[4] = 0;
    if (!isxdigit(data[0]) ||
        !isxdigit(data[1]) ||
        !isxdigit(data[2]) ||
        !isxdigit(data[3])) {
        return ARGO_RESULT_BAD_DATA;
    }
    sscanf((const char *)&(data[0]), "%4x", &cp1);

    if (cp1 < 0x80) {
        if (dest_length < 1) return ARGO_RESULT_ERR_TOO_LARGE;
        dest[0] = cp1; /* 0xxxxxxx */
        *written_length = 1;
    } else if (cp1 < 0x800) {
        if (dest_length < 2) return ARGO_RESULT_ERR_TOO_LARGE;
        dest[0] = ((cp1 >> 6) & 0x1F) | 0xC0; /* 110xxxxx */
        dest[1] = ((cp1     ) & 0x3F) | 0x80; /* 10xxxxxx */
        *written_length = 2;
    } else if (cp1 < 0xD800 || cp1 > 0xDFFF) {
        if (dest_length < 3) return ARGO_RESULT_ERR_TOO_LARGE;
        dest[0] = ((cp1 >> 12) & 0x0F) | 0xE0; /* 1110xxxx */
        dest[1] = ((cp1 >> 6)  & 0x3F) | 0x80; /* 10xxxxxx */
        dest[2] = ((cp1     )  & 0x3F) | 0x80; /* 10xxxxxx */
        *written_length = 3;
    } else if (cp1 >= 0xD800 && cp1 <= 0xDBFF) { /* lead surrogate (0xD800..0xDBFF) */
        if (dest_length < 4) return ARGO_RESULT_ERR_TOO_LARGE;

        /* Skip '\u' */
        res = argo_buf_get(buf);
        if (res < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (res != '\\') return ARGO_RESULT_BAD_DATA;
        res = argo_buf_get(buf);
        if (res < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
        if (res != 'u') return ARGO_RESULT_BAD_DATA;

        res = argo_buf_read_binary(buf, data, 4);
        if (res) return res;
        data[4] = 0;
        if (!isxdigit(data[0]) ||
            !isxdigit(data[1]) ||
            !isxdigit(data[2]) ||
            !isxdigit(data[3])) {
            return ARGO_RESULT_BAD_DATA;
        }
        sscanf((const char *)&(data[0]), "%4x", &cp2);

        cp2 = ((((cp1-0xD800)&0x3FF)<<10)|((cp2-0xDC00)&0x3FF))+0x010000;
        dest[0] = (((cp2 >> 18) & 0x07) | 0xF0); /* 11110xxx */
        dest[1] = (((cp2 >> 12) & 0x3F) | 0x80); /* 10xxxxxx */
        dest[2] = (((cp2 >> 6)  & 0x3F) | 0x80); /* 10xxxxxx */
        dest[3] = (((cp2     )  & 0x3F) | 0x80); /* 10xxxxxx */
        *written_length = 4;
    } else { /* trail surrogate before lead surrogate */
        return ARGO_RESULT_BAD_DATA;
    }
    return ARGO_RESULT_NO_ERROR;
}

/*
 * See header
 */
int argo_buf_read_json_string(struct argo_buf **buf, char *dest, size_t *dest_length)
{
    char *out = dest;
    char *dest_end = out + *dest_length;
    int res;
    int c;
    int c2;

    res = argo_buf_require_char(buf, '"');
    if (res) return res;

    while (out < dest_end) {
        c = argo_buf_read_char(buf);
        if (c < 0) return ARGO_RESULT_INSUFFICIENT_DATA;

        if (c == '\\') {
            c2 = argo_buf_read_char(buf);
            if (c2 < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
            switch (c2) {
            case '"':
                *out = '"';
                break;
            case '\\':
                *out = '\\';
                break;
            case '/':
                *out = '/';
                break;
            case 'b':
                *out = '\b';
                break;
            case 'f':
                *out = '\f';
                break;
            case 'n':
                *out = '\n';
                break;
            case 'r':
                *out = '\r';
                break;
            case 't':
                *out = '\t';
                break;
            case 'u':
                {
                    size_t written;
                    res = argo_buf_read_json_string_utf(buf, out, dest_end - out, &written);
                    if (res) {
                        _ARGO_LOG(AL_ERROR, "Could not parse utf encoding in JSON string");
                        return res;
                    }
                    if (written == 1 && *out == 0) return ARGO_RESULT_BAD_DATA; /* Special case: Found a unicode NULL byte */
                    out += written - 1; /* -1 because of the ++ immediately after break */
                }
                break;
            default:
                _ARGO_LOG(AL_ERROR, "Not implemented");
                return ARGO_RESULT_NOT_IMPLEMENTED;
            }
            out++;
        } else if (c != '"') {
            if (c == 0) return ARGO_RESULT_BAD_DATA;
            *out = c;
            out++;
        } else {
            /* Length doesn't include null */
            *dest_length = out - dest;
            /* Terminal '"' */
            *out = 0;
            out++;
            return ARGO_RESULT_NO_ERROR;
        }
    }
    fprintf(stderr, "%s:%s:%d: Err, too large\n", __FILE__, __FUNCTION__, __LINE__);
    return ARGO_RESULT_ERR_TOO_LARGE;
}

int argo_buf_peek_json_binary_length(struct argo_buf *buf, size_t *base64_len, size_t *binary_len)
{
    int res;
    res = argo_buf_peek_json_string_length(buf, base64_len);
    if (res) return res;
    if (*base64_len) {
        /* Don't forget NULL if base64 is not empty */
        (*base64_len)++;
    }
    *binary_len = ((*base64_len) / 4) * 3;
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_read_json_binary(struct argo_buf **buf, uint8_t *dest, size_t *dest_length, char *scratch, size_t scratch_length)
{
    size_t out_len;
    int res;
    size_t str_length;

    res = argo_buf_read_json_string(buf, scratch, &scratch_length);
    if (res) return res;
    /* NOTE: This validation is not needed here strictly based on the other operations being done below...
     * But, there is still no reason to have any NULL characters in this case as well...
     * So, adding this validation here for completeness and consistency with other areas of the code where this check if necessary
     */
    str_length = strnlen(scratch, scratch_length + 1);
    if (scratch_length != str_length) {
        res = ARGO_RESULT_BAD_DATA;
        _ARGO_LOG(AL_ERROR, "Bad input detected: In RPC [%s] for field [%.32s], parse_len %zu, str_len %zu..aborting with error %s",
                              "UNKNOWN", scratch, scratch_length, str_length, argo_result_string(res));
        return res;
    }

    out_len = base64_decoded_size(scratch, scratch_length);
    if (out_len > *dest_length) {
        fprintf(stderr, "%s:%s:%d: Err, too large\n", __FILE__, __FUNCTION__, __LINE__);
        return ARGO_RESULT_ERR_TOO_LARGE;
    }

    if (base64_decode_binary(dest, scratch, scratch_length) != out_len) {
        fprintf(stdout, "%s:%d: Base64 length mismatch\n", __FILE__, __LINE__);
        return ARGO_RESULT_BAD_DATA;
    }

    *dest_length = out_len;
    return ARGO_RESULT_NO_ERROR;
}


/*
 * See header
 */
int argo_buf_read_json_integer(struct argo_buf **buf, uint64_t *value)
{
    char str_buf[100];
    char *s, *e;
    int c;

    s = str_buf;
    e = s + sizeof(str_buf);

    /* Skip whitespace... */
    do {
        c = argo_buf_read_char(buf);
        if (c < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
    } while (isspace(c) || (c == ',') || (c == 0));

    while(s < (e - 1)) {
        *s = c;
        s++;
        c = argo_buf_peek(*buf);
        if ((c == '}') ||
            (c == ',') ||
            (c == ']') ||
            isspace(c)) {
            *s = 0;
            break;
        }
        c = argo_buf_read_char(buf);
        if (c < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
    }

    if (s >= e) {
        return ARGO_RESULT_BAD_DATA;
    }

    if (s == str_buf) {
        return ARGO_RESULT_BAD_DATA;
    }

    if ((str_buf[0] == '0') && (str_buf[1] == 'x')) {
        *value = strtoull(str_buf, NULL, 16);
    } else {
        *value = strtoull(str_buf, NULL, 10);
    }

    return ARGO_RESULT_NO_ERROR;
}


int argo_buf_read_json_double(struct argo_buf **buf, double *value)
{
    char str_buf[100];
    char *s, *e;
    int c;

    s = str_buf;
    e = s + sizeof(str_buf);

    /* Skip whitespace... */
    do {
        c = argo_buf_read_char(buf);
        if (c < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
    } while (isspace(c) || (c == ',') || (c == 0));

    while(s < (e - 1)) {
        *s = c;
        s++;
        c = argo_buf_peek(*buf);
        if ((c == '}') ||
            (c == ',') ||
            (c == ']') ||
            isspace(c)) {
            *s = 0;
            break;
        }
        c = argo_buf_read_char(buf);
        if (c < 0) return ARGO_RESULT_INSUFFICIENT_DATA;
    }

    if (s >= e) {
        return ARGO_RESULT_BAD_DATA;
    }

    *value = strtod(str_buf, &s);
    if (s == str_buf) {
        return ARGO_RESULT_BAD_DATA;
    }

    return ARGO_RESULT_NO_ERROR;
}


/*
 * Copy buffer(s) out.
 */
int argo_buf_copy_out(struct argo_buf *buf, void *destination, size_t destination_size, size_t *written)
{
    uint8_t *d = (uint8_t *)destination;
    int res = ARGO_RESULT_NO_ERROR;

    while (buf) {
        if ((buf->last - buf->start) > (destination_size - (d - (uint8_t *)destination))) {
            //fprintf(stderr, "%s:%s:%d: Err, too large\n", __FILE__, __FUNCTION__, __LINE__);
            res = ARGO_RESULT_ERR_TOO_LARGE;
            break;
        }
        memcpy(d, buf->start, buf->last - buf->start);
        d += buf->last - buf->start;
        buf = buf->next;
    }
    if (written) *written = d - (uint8_t *)destination;
    return res;
}

int argo_buf_add_buffer(struct argo_buf **buf, size_t len)
{
    if ((*buf)->next) return ARGO_RESULT_ERR;
    if (len > ARGO_BUF_DEFAULT_SIZE) {
        (*buf)->next = argo_buf_create(0, NULL, NULL, len);
    } else {
        (*buf)->next = argo_buf_create(0, NULL, NULL, ARGO_BUF_DEFAULT_SIZE);
    }
    if (!(*buf)->next) return ARGO_RESULT_NO_MEMORY;
    *buf = (*buf)->next;
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_align(struct argo_buf **buf, size_t align)
{
    uintptr_t current_heap = (uintptr_t) (*buf)->last;
    uintptr_t new_heap;
    uintptr_t mask = align - 1;
    uintptr_t end;
    int res;

    if (current_heap & mask) {
        new_heap = current_heap;
        new_heap |= mask;
        new_heap++;
        end = (uintptr_t)((*buf)->buffer + (*buf)->buffer_len);
        if (new_heap >= end) {
            /* Add a buffer, since alignment doesn't fit */
            res = argo_buf_add_buffer(buf, ARGO_BUF_DEFAULT_SIZE);
            if (res) return res;
            /* Make sure it actually got aligned... */
            return argo_buf_align(buf, align);
        } else {
            (*buf)->last = (uint8_t *)new_heap;
        }
    }
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_get_space(struct argo_buf **buf, size_t len, void **where)
{
    int res;
    size_t len_remaining = (*buf)->buffer_len - ((*buf)->last - (*buf)->buffer);
    if (len_remaining < len) {
        res = argo_buf_add_buffer(buf, len);
        if (res) return res;
    }
    *where = (*buf)->last;
    (*buf)->last += len;
    return ARGO_RESULT_NO_ERROR;
}

int argo_buf_grow_space(struct argo_buf **buf, size_t len, void **where)
{
    size_t len_remaining = (*buf)->buffer_len - ((*buf)->last - (*buf)->buffer);
    if (len_remaining < len) {
        return ARGO_RESULT_ERR_TOO_LARGE;
    }
    *where = (*buf)->last;
    (*buf)->last += len;
    return ARGO_RESULT_NO_ERROR;
}
