/*
 * argo_log.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 */

#ifndef __ARGO_LOG_H__
#define __ARGO_LOG_H__

/*
 * This is a memory-based logging infrastructure.
 */

#include "argo/argo.h"
#include <syslog.h>

/*
 * Model:
 *
 * An 'argo_log_collection' is a collection of log messages in the form of
 * argo objects. All argo logs contain a common header, which can be
 * seen in the structure argo_log.
 *
 * Anything can add log messages to an argo_log_collection.
 *
 * An argo_log can have multiple consumers. (readers)
 *
 * Each log collection can store a specific number of log
 * messages. This number of messages to log must be chosen
 * intelligently by the user to ensure that memory and time
 * requirements are satisfied. Once too many log messages are being
 * stored, oldest log messages are thrown out to make space.
 *
 * There are two built-in consumers:
 *
 * - Syslog. Be a little careful with this one- syslog isn't always
 *   prepared to receive logs of the size argo_log can deal
 *   with. (argo_log will happily send them to syslog though)
 *
 * - Filelog. This is simply a file store of the logging data. You can
 *   specify maximum file length and the like. The filelog can be
 *   stored in either json format or compressed binary json (argo)
 *   format.
 *
 * It is easy to build external log consumers for other
 * purposes. (Like sending to a big data warehouse...)
 *
 * Argo_log collects data in two ways: Either from structures (either
 * directly or periodically) or in a manner much like syslog.
 *
 * For direct logging, a simple function call causes data to be
 * logged. This method of logging is most often used for
 * informational, debug, and state logging.
 *
 * For periodic logging, argo_log will periodically generate a log
 * message on its own based on data registered to log. This method of
 * logging is most often used for statistics that are to be
 * monitored/kept periodically.
 *
 * Argo log data is consumed via an arbitrary number of
 * readers. (expected to be a small number, but it's dynamic) The
 * readers of argo_logs can be fully asynchronous. The operation of a
 * reader is in one of three states:
 *
 * 1. Active reading. This is the "data transfer" state. When an
 *    active read is engaged by a reader, ALL the data in the log that
 *    the reader is requesting will be sent to a reader callback. This
 *    operation will always be done in the context of the reader
 *    thread (when it asks for the data originally, and when it
 *    resumes reading after being blocked) The active read
 *    will only pause/stop on three conditions:
 *
 *    A. Error condition returned by reader callback. In this case,
 *       the reading context is destroyed, released, and (perhaps)
 *       freed.
 *
 *    B. Would_block condition returned by reader callback. In this
 *       case, the reading context goes into blocked reading state.
 *
 *    C. Out of data condition encountered trying to feed data to
 *       reader callback. In this case, the reading context goes into
 *       passive reading state and simply waits for more log messages
 *       to arrive.
 *
 * 2. Blocked reading. This is the state where the reading context
 *    cannot take any more data or it would block. In this state the
 *    reading context will never receive more data until it is
 *    'awakened' by a call to reader_resume_xmit.
 *
 * 3. Passive Reading. In this state, the reading context has already
 *    read all the log messages queued on the log collection. When new
 *    log messages arrive, they are immediately sent to the reading
 *    context callbacks that are in passive reading state. The same
 *    state changes available in active_reading in response to the
 *    callback are possible here.
 *
 * argo_log relies on pthread, pipe, and poll.
 *
 * A lot of argo_log users expect one of three collections to exist:
 *
 * "event_log" - A log of events. Used very much like syslog.
 * "statistics_log" - A log for statistics.
 * "data_log" - An information (data) log- generally a very high
 *     volume logging subsystem.
 */

/*
 * l_inst, l_id, l_us comprise unique identification for this log,
 * globally.
 *
 * Note that l_data, l_prio, and l_name are optional, and may not
 * exist for a log.
 */
struct argo_log {         /* _ARGO: object_definition */
    /* Globally unique instance name for the running of this
     * program. This is generic text, but operationally configured to
     * be unique amongst all systems and programs worldwide.  This
     * name generally does NOT include the cloud name of the instance-
     * just it's simple name. i.e. dns1.sjc3, not
     * dns1.sjc3.dev.zpath.net */
    const char *l_inst;   /* _ARGO: string */

    /*
     * l_inst's GID.
     */
    int64_t l_inst_gid;   /* _ARGO: integer, stringify */

    /* epoch, in microseconds. This is the time when the object is _about_ to the written to the disk. Say for example
     * if assistant is sending a log message to broker, l_us represents the time the broker is about to write to disk*/
    int64_t l_us;         /* _ARGO: integer */

    /* ID of this log message. Monotonically increasing from 1 from
     * application start time. Unique to this program for each running
     * of the program. Used in a manner such that l_inst + l_us + l_id
     * guarantee the uniqueness of this specific log. */
    int64_t l_id;         /* _ARGO: integer */

    /* Same time as epoch_us, except in date format, including local
     * timezone.
     *
     * YYYY-MM-DD HH:MM:SS,SSS ZZZZZ
     * 01234567890123456789012345678
     *
     * Where SSS is miliseconds, and ZZZZZ is [+-]DDDD for timezone-
     * i.e. +0800
     */
    char *l_date;         /* _ARGO: string */

    /* The RFC 5424 priority of the log message, text form, all lower
     * case */
    const char *l_prio;   /* _ARGO: string */

    /* The role that generated this traffic. Generally only used for
     * statistics logs, in order to do simple role based
     * statistics. */
    const char *l_role;   /* _ARGO: string */

    /* Fields only used by zpn_event module to send event message (log) to zpn_event topic.
     * l_disp - Identify the event disposition(good/bad/info)
     * l_sys - This is to capture the system the log is about like assistant, pb, broker, etc.
     * l_ctg - A high level category this event name belongs to like Usage Metrics, Enrollment, Connectivity and Upgrade. */
    const char *l_disp; /* _ARGO: string */
    const char *l_sys;  /* _ARGO: string */
    const char *l_ctg;  /* _ARGO: string */

    /* The name of whatever is being logged. */
    const char *l_name;   /* _ARGO: string, nozero, name:l_obj */

    /* The type of the object being logged. This should be C structure name which is going to be sent as the data in
     * this log. Say for example zthread_rusage is C structure and so l_otyp=zthread_ruage. Now this can be used for
     * all threads or one thread, based on that we can have l_name = rusage_thread_all or l_name = rusage_thread_x*/
    const char *l_otyp;   /* _ARGO: string, type:l_obj */
    /* This is the data being logged. */
    struct argo_object *l_obj; /* _ARGO: argo_object, json_old */

    /* The type of the object being logged. */
    const char *l_atyp;   /* _ARGO: string, type:l_arr */
    /* This is the data being logged. */
    struct argo_list *l_arr; /* _ARGO: list */

    /* Some deprecated fields... These will go away at some point. */
    const char *log_name;/* _ARGO: string, name:log_data */
    char *log_type;      /* _ARGO: string, type:log_data */
    void *log_data;      /* _ARGO: object */
    int64_t epoch_us;    /* _ARGO: integer, nozero */

    int8_t priority_int; /* Integer form of priority. */

    /* l_pt - identifies the partition the instance belongs to
     * If there is no partition, populate as 'Unpartitioned'.
     * This is used only for our internal monitoring
     */
    const char *l_pt;   /* _ARGO: string */
};


/*
 * This is a standard structure for a simple event log.
 */
struct argo_log_text {    /* _ARGO: object_definition */
    const char *thread;   /* _ARGO: string */
    const char *file;     /* _ARGO: string */
    const char *function; /* _ARGO: string */
    int line;             /* _ARGO: integer, cached */
    const char *message;  /* _ARGO: string, small, chunked */
};

/*
 * This is a standard structure for a dumping the contents of a file into event/statistics log
 */
struct argo_log_text_from_file {              /* _ARGO: object_definition */
    const char *file_path;                    /* _ARGO: string */
    const char *file_contents;                /* _ARGO: string */
    int         file_contents_size;           /* _ARGO: integer */
    int         file_contents_not_fully_read; /* _ARGO: integer */
};

/*
 * These are the statistics reported by argo_readers
 */
struct argo_reader_stats { /* _ARGO: object_definition */
    const char *state;     /* _ARGO: string */
    uint64_t head_index;    /* _ARGO: integer */
    uint64_t read_index;    /* _ARGO: integer */
    // uint64_t write_index;   /* deprecated, but still here so no one changes type if it is recreated */
    uint64_t backlog;       /* _ARGO: integer */
    uint64_t in_flight;     /* _ARGO: integer */
    uint64_t total_skips;   /* _ARGO: integer */
	uint64_t seek_cnt;		/* _ARGO: integer */
    uint64_t ack_index;     /* _ARGO: integer */
    uint64_t retransmits;   /* _ARGO: integer */
    uint64_t cur_memory;    /* _ARGO: integer */
    uint64_t max_memory;    /* _ARGO: integer */
    uint64_t blocks;        /* _ARGO: integer */
    uint64_t errors;        /* _ARGO: integer */
};


/* argo_log_collection: A collection of log data. */
struct argo_log_collection;

/* argo_log_reader: Reader state associated with consuming log data. */
struct argo_log_reader;

/* argo_log_registered_structure: A structure that is periodically
 * snapshot and logged. (generally used for statistics) */
struct argo_log_registered_structure;

enum argo_log_priority {
    argo_log_priority_emergency=0,
    argo_log_priority_alert,
    argo_log_priority_critical,
    argo_log_priority_error,
    argo_log_priority_warning,
    argo_log_priority_notice,
    argo_log_priority_info,
    argo_log_priority_debug,
    argo_log_priority_none  // Use to not log priority
};

extern struct argo_structure_description *global_argo_log_desc;
extern struct argo_structure_description *global_argo_log_text_desc;

/* Shorter names */
#define AL_EMERGENCY argo_log_priority_emergency
#define AL_ALERT     argo_log_priority_alert
#define AL_CRITICAL  argo_log_priority_critical
#define AL_ERROR     argo_log_priority_error
#define AL_WARNING   argo_log_priority_warning
#define AL_NOTICE    argo_log_priority_notice
#define AL_INFO      argo_log_priority_info
#define AL_DEBUG     argo_log_priority_debug
#define AL_NONE      argo_log_priority_none


/*
 * Function prototypes.
 *
 * All functions that return a pointer to a structure only do so on
 * success. A null return value in these cases is an error.
 *
 * All functions that return an integer return an ARGO_RESULT
 * indicator. (see argo.h) (This includes function callbacks!)
 */


/*************************************************************
 *
 * Utility functions/macros
 *
 * These macros are intended to assist module writers with making
 * consistent logging/debug events in their code.
 */
#ifndef __FILE_NAME__
#define __FILE_NAME__ __FILE__
#endif

#define ARGO_LOG(log, priority, module, format...)\
    argo_log_text(log, priority, module, __FILE_NAME__, __FUNCTION__, __LINE__, ##format)

#define ARGO_DEBUG_LOG(condition, log, priority, module, format...)\
    if (condition) argo_log_text(log, priority, module, __FILE_NAME__, __FUNCTION__, __LINE__, ##format)

#define ARGO_LOG_FILE_CONTENTS(log, priority, module, file_to_read)\
    argo_log_text_from_file(log, priority, 0, module, file_to_read, __FILE_NAME__, __FUNCTION__, __LINE__)

#define ARGO_DEBUG_LOG_FILE_CONTENTS(condition, log, priority, module, file_to_read)\
    if (condition) argo_log_text_from_file(log, priority, 0, module, file_to_read, __FILE_NAME__, __FUNCTION__, __LINE__)

#define ARGO_STATS_LOG_FILE_CONTENTS(log, module, file_to_read)\
    argo_log_text_from_file(log, AL_NONE, 1, module, file_to_read, __FILE_NAME__, __FUNCTION__, __LINE__)

const char *argo_log_priority_string(enum argo_log_priority priority);
const char *argo_log_short_priority_string(enum argo_log_priority priority);
enum argo_log_priority argo_log_rfc_priority_string_to_priority(const char *prio_str);


/*************************************************************
 *
 * Initialization functions
 *
 * Note: There are no destruction functions for global argo_log state
 * or for individual argo_logs.
 *
 * argo_log creates a single extra thread to use for grabbing periodic
 * statistics.
 *
 * All data feeding into argo_log and all data pulling out of argo_log
 * is done on demand by external routines.
 */


/*
 * Initialize argo_log for the current running program. This must be
 * run once before any other argo_log call is made. It is permissible
 * to run this routine multiple times to change the instance, role,
 * and build names as they are "learned" It is NOT recommended to call
 * this routine excessively.
 *
 * This routine must be called after argo_init.
 *
 * The arguments are generic references to the name, role, and
 * software build of this running program. These are expected to never
 * change for the duration of the running of this program.
 *
 * is_thin_app - when set to non-zero value is an indication to argo log to use
 * lesser memory.
 *
 * max_logging_mb indicates the maximum megabytes of logging to use
 * for argo_log. If set to zero, the program automatically chooses
 * based on system memory and is_thin_app.
 */
int
argo_log_init(const char *instance_name, int64_t instance_gid, const char *instance_role, const char *instance_build, int is_thin_app);
int
argo_log_init_with_maxmem(const char *instance_name,
                          int64_t instance_gid,
                          const char *instance_role,
                          const char *instance_build,
                          int is_thin_app,
                          int max_logging_mb);

void
argo_log_update_instance_name(const char *instance_name);

void
argo_log_update_partition_name(const char *partition_name, int reset);

/*
 * Argo memory high tide related function APIs.
 * ############################################
 * argo_log_global_set_mem_threshold_testing is to be only called from
 * UT file. It is not thread safe and is only for UTs.
 */
#define ARGO_MEM_THRESHOLD_DEFAULT 80
void argo_log_global_set_mem_threshold(uint64_t total_ram, int threshold_percent);
void argo_log_global_set_mem_threshold_testing(int8_t on);
void argo_log_dump_logging_lib_params(char *str, size_t str_len);
void argo_log_dump_registered_structs(char *str, size_t str_len);
void argo_log_logging_lib_params_reset(void);
int64_t argo_log_purge_collections(int all, const char *name, uint64_t unused);
void argo_log_global_set_mem_threshold_rest_endpoint(uint64_t size_in_MB);
void argo_log_global_set_mem_difference_threshold_rest_endpoint(uint64_t size_in_MB);
void argo_log_global_set_memory_thread_interval_rest_endpoint(uint64_t interval_s);
void argo_disable_memory_hightide(uint64_t disable);


/*
 * Set global max mem(in MB) for an argo log instance.
 * Default value for global max mem is 4096 MB. This value can be changed
 * to user provided one by calling this routine.
 * Call this routine only after a successfull call to argo_log_init.
 * */
void argo_log_global_set_max_mem(uint64_t max_mem_mb);

/*
 * if use_printf is non-zero, will use printf for event logging rather
 * than the collection system. Useful for seeing all output that
 * happens before a crash. (debugging). Not useful for production.
 * use_printf = 1 - no colors
 * use_printf = 2 - color based on log level and argo_priorities_short_color.
 *              Compact format is used, without date
 */
void argo_log_use_printf(int use_printf);
/* Bitmap of allowed prioritied when using printf. Remember that least
 * significant bits are highest priority */
void argo_log_set_printf_allowed_bitmap(int bitmap);


/*
 * Set the instance GID. Any process which is using app_init will read the instance table to get this gid. But it
 * would initialize argo log even before knowing the instance gid - so it is important to set it whenever it knows
 * the instance gid. Process which is using simple_app_init also will know its instance_gid once the log
 * initialization is done, so those process also have to set it. This is because app_init or simple_app_init should
 * be done by any itasca process even before thinking of doing anything else.
 *
 * There are two ways to set instance_gid - call this routine or call the argo_log_init for the second time with a
 * non-zero gid. Two versions are there for flexibility in usage.
 */
void argo_log_set_instance_gid(int64_t instance_gid);

typedef void (*argo_log_collection_create_reader_cb)(void *cookie);

/* Used for passing reader specific info to an argo collection while creating it.
 * This info (if passed in) will be used by a collection_thread for creating
 * a reader for the collection (once the thread for that collection is triggered)
 */
struct argo_log_collection_create_reader {
    char name[1000];
    char domain_name[256];
    char path[1000];
    char identity[1000];
    uint16_t service_port_he;
    int use_ssl;
    argo_log_collection_create_reader_cb create_reader_cb;
};

/*
 * Create a log collection point for this system.
 *
 * Multiple log collections of different sizes can be used for
 * different purposes. They all operate in the same basic manner.
 *
 * log_name is used for debugging and access purposes. It is possible
 * to get a handle on a log collection by name. (Handy for
 * initialization.)
 *
 * reader is used to create a reader for the collection at the time of
 * spawning of the thread underpinning the collection (useful if we want
 * to create reader only when thread for this collection gets fired)
 *
 * If the log was actually created (instead of returning one already
 * created) then created will be set to 1. Otherwise it will be 0.
 *
 * created may be NULL.
 */
struct argo_log_collection *argo_log_create(const char *log_name, struct argo_log_collection_create_reader *reader, int *created);

void  argo_log_collection_set_max_mem(struct argo_log_collection *coll,
                                      uint64_t max_mem_mb);

uint64_t argo_log_collection_get_max_mem(struct argo_log_collection *coll);

uint64_t argo_log_collection_get_evicted_cnt_tot(
							struct argo_log_collection *coll);

void argo_log_collection_mem_view(char *str, size_t str_len);


/* Get a log collection by name. 'log_name' must have originally been
 * created by argo_log_create. */
struct argo_log_collection *argo_log_get(const char *log_name);

/* Get a log name by collection. */
char *argo_log_get_name(struct argo_log_collection *collection);

/* Get our instance name... Did we forget it? hehe.. */
char *argo_log_get_instance_name(void);

/* Get how many objects can be stored in a collection without
 * overrunning any readers. */
size_t argo_log_get_freespace(struct argo_log_collection *collection);

/* Iterate over all collections. WARNING: holds global collection lock
 * while operating, so new collections cannot be made, etc while this
 * is in operation */
typedef void (argo_log_collection_iterate_f)(struct argo_log_collection *collection, void *cookie);
void argo_log_collection_iterate(argo_log_collection_iterate_f *callback, void *cookie);


/*************************************************************
 *
 * Logging functions
 */


/*
 * This is a synchronous logging call. It logs the specified
 * structure, right now.
 *
 * name is the name of the object. (see priodic logging, below)
 */
int argo_log_structure_immediate(struct argo_log_collection *collection,
                                 enum argo_log_priority priority,
                                 int include_role,
                                 const char *name,
                                 struct argo_structure_description *description,
                                 void *structure_data);


/*
 * This routine generates a log object from the given structure that
 * can be queued to a collection (or otherwise used) later.
 *
 * if epoch_us is zero, this routine generates epoch_us from current
 * time.
 *
 * if instance_name is NULL (SHOULD BE THE COMMON CASE), then instance
 * name is inherited from argo_log_init configuration.
 */
struct argo_object *
argo_log_create_log_object(int64_t epoch_in_us,
                           enum argo_log_priority priority,
                           int include_role,
                           const char *log_name,
                           const char *instance_name,
                           int64_t instance_gid,
                           struct argo_structure_description *description,
                           void *structure_data,
                           const char *role);

/*
 * Similar to argo_log_create_log_object, it generates a log object from
 * the given structure that can be queued to a collection (or otherwise used)
 * later. It's only used by zpn_event module to cretae log object.
 */
struct argo_object *
argo_log_create_event_log_object(const char *log_name,
                                 const char *priority,
                                 const char *disposition,
                                 const char *system_name,
                                 const char *category_name,
                                 const char *instance_name,
                                 int64_t instance_gid,
                                 struct argo_structure_description *description,
                                 void *structure_data);

/*
 * This routine queues an argo_object (which must be an argo_log
 * structure!) to the specified collection.
 *
 * This routine increases the reference count on the object for its
 * own purpose.
 */
int argo_log_log_object_immediate(struct argo_log_collection *collection,
                                  struct argo_object *log_object);


/*
 * Prototype of routine called just before logging periodic data.
 */
typedef int (argo_log_pre_log_callback_f)(void *callback_cookie, int counter,
                                          void *structure_data);

/*
 * These are periodic logging calls. One is for registering a single
 * structure. The other if for registering a set of structures, all of
 * which are to be aggregated into a single log. (Generally for
 * aggregating per-thread statistics)
 *
 * These calls requests that argo take a snapshot of structure_data at
 * some microsecond interval and create a log message based on that
 * snapshot. There is no interlocking performed while taking the
 * snapshot- these values can be very slightly inconsistent,
 * particularly for large structures.
 *
 * It is most commonly used for single structures. If multiple
 * structures are specified, then the sum of all structures is
 * generated into a single log message. This is handy for performing
 * per-thread and aggregate statistics. Note that aggregating multiple
 * structures' statistics only works for integer fields. Any string
 * fields assume the value of the first structure specified. (Again-
 * this is only an issue when specifying multiple structures to
 * aggregate)
 *
 * It is the responsibility of the caller registering for this service
 * to deregister from this service BEFORE the structure being
 * referenced becomes invalid for some reason. (connection closed, so
 * no more stats, for example)
 *
 * log - The collection to which to log the periodic snapshots.
 *
 * name - The conversational name of the structure being
 *   logged. Specifically, this is NOT A TYPE. This is the name of the
 *   instance of that type. i.e. for tcp connection stats you might
 *   use the name 'local->*********'
 *
 * priority - The priority of the message. For periodic statistics,
 *   this will almost always be argo_log_priority_info. But you can
 *   use whatever you want...
 *
 * period_us - The frequency with which a snapshot is taken, in
 *   microseconds. The timer accuracy of the OS will likely prevent
 *   accuracy quite that close, but we export it anyway.
 *
 * description - The description of the structure(s) to be snapshot.
 *
 * structure_data - Either a single pointer to the structure to be
 *   snapshot, a pointer to an array of pointers to be snapshot,
 *   depending on which of the two calls is used. In both cases, the
 *   structure data is (described by description). For the array of
 *   void* case, the pointers are copied- the source array can be
 *   ephemeral, though the target data itself must not be.
 *
 * structure_count - Number of structures to aggregate into a single
 *   statistics log message. (for register_structures only). Note that
 *   when we say aggregage, all the int fields in structures are all
 *   summed up and the result is a single message
 *
 * log_immediate - Indicates that a log should be generated for this
 *   structure immediately in addition to every period.
 *
 * pre_log_callback - A routine that can be called immediately before
 *   logging takes place. (To fill in stats, or the like)
 *
 * callback_cookie - A cookie passed to the pre_log_callback, if
 *   pre_log_callback exists. Note that there is only one cookie, even
 *   if calling against an array of structure data. Hope you don't
 *   mind. Having said that we also pass the counter in the callback
 *   on which structure object we are interested in for the moment in
 *   case of array of structure data.
 */
struct argo_log_registered_structure *
argo_log_register_structure(struct argo_log_collection *collection,
                            const char *name,
                            enum argo_log_priority priority,
                            int64_t period_us,
                            struct argo_structure_description *description,
                            void *structure_data,
                            int log_immediate,
                            argo_log_pre_log_callback_f *pre_log_callback,
                            void *callback_cookie);

struct argo_log_registered_structure *
argo_log_register_structures(struct argo_log_collection *collection,
                             const char *name,
                             enum argo_log_priority priority,
                             int64_t period_us,
                             struct argo_structure_description *description,
                             void **structure_data,
                             size_t structure_count,
                             int log_immediate,
                             argo_log_pre_log_callback_f *pre_log_callback,
                             void *callback_cookie);

/*
 * This is the deregistration function for periodic logs. If
 * log_last_time is non-zero, then one last snapshot of the structure
 * is logged during this call, then the structure is deregistered and
 * will no longer be periodically captured.
 */
int argo_log_deregister_structure(struct argo_log_registered_structure *structure,
                                  int log_last_time);


#ifdef __cplusplus
extern "C" {
#endif

/*
 * This is a synchronous logging call for simple text-style event
 * logging. (much like syslog)
 *
 * module- The module (i.e. 'argo', 'wally', etc) that is generating
 *    the log message. NULL legal but not preferred.
 *
 * file- The file name of the source code that is generating the log
 *    message. NULL legal but not preferred.
 *
 * function- The function name of the source code that is generating
 *    the log message. NULL legal but not preferred.
 *
 * line- The line number within the file of the source code that is
 *    generating the log message. NULL legal but not preferred.
 *
 * Note: This routine limits the number of logs that can be generated
 * from a single line of code to, default, 10 per second. This can be
 * changed by argo_log_set_max_text_per_line_per_s().
 */
int argo_log_text(struct argo_log_collection *collection,
                  enum argo_log_priority priority,
                  const char *module,
                  const char *file,
                  const char *function,
                  int line,
                  const char *format,
                  ...)
    __attribute__((format(printf, 7, 8)));

#ifdef __cplusplus
} // extern "C"
#endif

/*
 * All the comments related to argo_log_text applies except for the contents of the log. Whereas argo_log_text()
 * dumps the message as specified by the user, argo_log_text_from_file() takes a file name as input and dumps
 * its contents.
 */
int argo_log_text_from_file(struct argo_log_collection *collection, enum argo_log_priority priority, int insert_role,
                            const char *module, const char *file_to_read, const char *file, const char *function,
                            int line);

int argo_log_set_max_text_per_line_per_s(int max);
int argo_log_get_max_text_per_line_per_s(int priority);
int argo_log_reset_max_text_per_line_per_s(int max, int priority);

/*************************************************************
 *
 * Reading functions
 */

/*
 * This is the prototype for callbacks made by argo_log to send data
 * to a reader. The argo_object in the callback is ALWAYS a reference
 * to an object containing an 'argo_log' structure. The object is only
 * valid for the duration of the call- it must be held if the reader
 * wishes to retain it for a longer duration. (Can't think of why a
 * reader would want to do so...)
 *
 * argo_log_sequence is the message sequence within the collection! It
 * is NOT the ID within the log message itself.
 *
 * This routine must return one of the following result codes:
 *
 * ARGO_RESULT_NO_ERROR: If operation was successful.
 *
 * ARGO_RESULT_WOULD_BLOCK: If operation would result in a blocking
 *    call. (Reader must call argo_log_resume to get data flowing again)
 *
 * ARGO_RESULT_*: Any Other: Error condition indicating reader
 *    failure. Reader will be deregistered and no longer used.
 */
typedef int (argo_log_callback_f)(struct argo_object *log_object,
                                  void *callback_cookie,
                                  int64_t argo_log_sequence);


/*
 * This routine, if it exists, can be used to read the index of the
 * last value fully read (probably written) by the reader.
 *
 * If a reader doesn't have a status_f callback, it is assumed to be
 * synchronous, and its write index will mirror its read index.
 */
typedef int64_t (argo_log_status_f)(void *callback_cookie);


/*
 * This is a read registration- a request for all log messages from a
 * log collection. It is highly recommended that this routine be
 * called without locks held.
 *
 * last_id_seen indicates the starting point whence the data is
 *      requested. Specifying zero for last_id_seen will always ask
 *      for all data. Causes reading to start at the NEXT id available
 *      which is greater than last_id_seen. The ID's in question are
 *      the index (sequence) of the log message within the specified
 *      colleciton. The ID has no bearing on the contents of the log
 *      message itself.
 *
 * continuous - Determines whether or not this reader should exist
 *      beyond the scope of this single call.
 *
 * callback - The callback used to send data to the reader.
 *
 * callback_cookie - cookie for same.
 *
 * stats_collection - If you wish to log stats about this reader to a
 *      collection, specify the collection for stats accumulation
 *      here. (See struct argo_reader_stats)
 *
 * stats_interval_us - The interval in microseconds at which to
 *      collect said stats, if so configured.
 */
struct argo_log_reader *argo_log_read(struct argo_log_collection *collection,
                                      const char *name,
                                      uint64_t last_log_id_seen,
                                      int continuous,
                                      argo_log_callback_f callback,
                                      argo_log_status_f status_callback,
                                      void *callback_cookie,
                                      struct argo_log_collection *stats_collection,
                                      int64_t stats_interval_us);

/*
 * Set the read index of the reader. (Can be used to re-read collection...)
 *
 * index is the index of the element that is desired to be read next.
 *
 * Setting into the future is a bad idea.
 */
void argo_log_read_set_index(struct argo_log_reader *reader,
                             uint64_t index);

/*
 * This is a request to resume sending logs to this reader.
 *
 * It is very best not to call this with any locks held whatsoever.
 */
int argo_log_resume(struct argo_log_reader *reader);

/*
 * This a removal of a read registration.
 *
 */
int argo_log_unread(struct argo_log_reader *reader);

/*
 * This routine will capture a snapshot of all the collection read
 * indexes, such that a later call to verify_snapshot can determine if
 * all the logs up to snapshot have been drained.
 *
 * This pair of routines is used to perform soft shutdown.
 */
int argo_collections_snapshot(void);

/*
 * This routine verifies that all logs that existed at the time of the
 * snapshot have been drained. It returns the number of outstanding
 * logs. (0, if everything up to and including the snapshot has been
 * drained)
 */
int64_t argo_collections_verify_snapshot(void);

/*
 * This callback is attached to syslog- All callbacks will result in a
 * syslog message being generated.
 *
 * The cookie points to an integer (int32_t) bitmask of the priority
 * of messages to send to syslog, where priority 0 is bit 0, etc. (See
 * syslog priorities, above) This makes it possible to syslog only
 * certain priorities of log messages.
 */
int argo_log_syslog_callback(struct argo_object *log_object,
                             void *callback_cookie,
                             int64_t argo_log_sequence);
/* Same as above, but non-JSON output of text log messages. */
int argo_log_syslog_clean_callback(struct argo_object *log_object,
                                   void *callback_cookie,
                                   int64_t argo_log_sequence);
int argo_log_syslog_minimal_callback(struct argo_object *log_object,
                                     void *callback_cookie,
                                     int64_t argo_log_sequence);


/*
 * This callback is attached to stderr- All callbacks will result in a
 * message to stderr being generated.
 *
 * The cookie points to an integer (int32_t) bitmask of the priority
 * of messages to send to stderr, where priority 0 is bit 0, etc. (See
 * syslog priorities, above)
 */
int argo_log_stderr_callback(struct argo_object *log_object,
                             void *callback_cookie,
                             int64_t argo_log_sequence);
/* Cleaner output stderr callback. (less JSON for text messages) */
int argo_log_stderr_clean_callback(struct argo_object *log_object,
                                   void *callback_cookie,
                                   int64_t argo_log_sequence);
int argo_log_stderr_minimal_callback(struct argo_object *log_object,
                                     void *callback_cookie,
                                     int64_t argo_log_sequence);



/*
 * These callbacks are related to using a simple file-based log store.
 */


/*
 * This routine creates a logging file suitable for use with a file
 * logging callback (argo_log_file_callback)
 *
 * This routine will create a file named filename.date, where date is
 * the ISO order: year, then month, then day, then hour, then minute,
 * then second, then microsecond. For example, a logfile might be
 * named: logfile.2013-04-11-19:53:21.432569UTC
 *
 * the short_filename, if it exists, is linked to the long filename.
 *
 * The time appended to the file is ALWAYS UTC, NEVER local time.
 *
 * max_length indicates the length beyond which a new file will be
 * created.
 *
 * argo_serialize_mode allows you to choose binary or text JSON format
 * for file output.
 */
struct argo_log_file *argo_log_file_create(struct argo_log_collection *collection,
                                           const char *filename,
                                           char *short_filename,
                                           size_t max_length,
                                           enum argo_serialize_mode);

/*
 * This callback is to write to a file. The file must previously have
 * been created by argo_log_file_create.
 *
 * The cookie for the callback should be a reference to the
 * argo_log_file.
 */
int argo_log_file_callback(struct argo_object *log_object,
                           void *callback_cookie,
                           int64_t argo_log_sequence);

/*
 * Output epoch_us in text form, relatively standard format.
 *
 * if sumo_form is non-zero, the output will be in sumologic friendly
 * form. (Which includes spaces and commas, of all the asinine things)
 */
#define ARGO_LOG_GEN_TIME_STR_LEN 34
int argo_log_gen_time(int64_t epoch_us, char *time_out, size_t time_out_size, int sumo_form, int utc);
/* time only, compact*/
int argo_log_gen_time_only(int64_t epoch_us, char *time_out, size_t time_out_size);

/*
 * Get the status of a log collection in string form. Useful for
 * debugging.
 */
void argo_log_status(struct argo_log_collection *collection, char *str, size_t str_len);

/*
 * Check to see if the collection has the reader by the name
 */
int argo_log_collection_has_reader(struct argo_log_collection *collection, char *reader_name);

/*
 * Change a registered structure's interval by its log name
 */
int argo_log_set_interval_by_log_name(struct argo_log_collection *collection,
                                      const char                 *name,
                                      int                        name_len,
                                      int64_t                    interval_us);

/*
 * ARGO Dump hepler functions to log
 */
int log_dump_structure(struct argo_log_collection *collection,
                        struct argo_structure_description *description,
                        void *data,
                        const char *label,
                        const char *conn_desc,
                        const char *module_name,
                        const char *func,
                        const char *file,
                        int line);
int log_dump_argo(struct argo_log_collection *collection,
                   struct argo_object *object,
                   const char *label,
                   const char *label2,
                   const char *conn_desc,
                   const char *module_name,
                   const char *func,
                   const char *file,
                   int line);
/*
 * reads single argo message from a argo json file
 *    file_path - file with single argo message
 *    description - argo description of the message
 *    callback - can be NULL for default handler. holder would have the loaded argo_object
 *    max_size - buffer size to read the file into. Set to 0, uses default buffer size
 *    collection - can be NULL, print json on error
 */
int argo_read_from_json(const char *file_path,
                        struct argo_structure_description *description,
                        argo_structure_callback_f *callback,
                        struct st_argo_holder *holder,
                        size_t max_size,
                        struct argo_log_collection *collection);

void get_argo_high_tide_state(uint8_t *argo_high_tide_state);
#endif /* __ARGO_LOG_H__ */
