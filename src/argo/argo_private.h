/*
 * argo_private.h.
 *
 * Copyright (C) 2013,2016 Zscaler, Inc. All Rights Reserved.
 *
 * Argo 2 Limits:
 *
 * Tokenization can be on or off. (Specified by client when client
 * connects and sends argo_description)
 *
 * Stream compression can be on or off. (Specified by client when
 * client connects and sends argo_description)
 *
 * If not specified or if argo_description is not received, then
 * tokenization is off and stream compression is off.
 *
 * Single token pool for entire argo.
 *
 * Token pool size limited to 2K
 *
 * Maximum 256 bytes per token.
 *
 * Token space limited to 64K bytes (average 32 bytes per token)
 *
 * Stream compression can be on or off. Stream compression is LZ4,
 * with framing done by argo.
 *
 * Last field values are ALWAYS stored if 64 bytes or less.
 * (Regardless of tokenization enabled/disabled) and never stored if
 * longer than 64 bytes.
 *
 * The maximum deserialized object size is 1MB.
 *
 * Argo 2 encoding:
 *
 * Byte stream should begin with a JSON object describing the
 * encoding. (See: argo_description)
 *
 * When searching for a an object to decode, all normal whitespace is
 * ignored. A JSON object begins with '{' if in JSON form, or with a
 * varint '1Vxx xxxx' where 'Vxx xxxx' is the length of binary data
 * that is to be decoded into an object
 *
 * JSON form deserialization occurs for the full object, then goes
 * back to search mode.
 *
 * Binary form deserialization occurs for the binary data, then goes
 * back to search mode.
 *
 * Binary encoding of data for ARGO2 is as follows:
 *
 * If stream compression is on, there is a Varint length representing
 * the space that will be occupied by the decompressed data, followed
 * by the actual compressed data.
 *
 * Within an argo stream, compression is continuous (not block
 * encoded), but is flushed so that objects arrive in whole.
 *
 * Default LZ4 settings are used for LZ4 compression.
 *
 * V = Variable integer encoding bit.
 *    V = 0: These are the last bits of the data.
 *    V = 1: There is at least another byte more data.
 *
 * S = Variable integer sign bit. If set, then the value is negative.
 *
 * Each binary chunk encodes one <object>.
 *
 * <object> :
 *   - Not being defined:    <object type> 0Vxx:xxxx <field values>   : x = number of field values
 *   - Being defined:        <object type> 10Vx:xxxx <fields>         : x = number of fields
 *   - NULL Object:          <object type> 1111:1111
 *   When not being defined:
 *      Infer: Field order consistent from last object definition of this type.
 *      Infer: Field type consistent from last object definition of this type. (Including array indication)
 *      Note: If field count specified for object is less than for object definition, fields are still in-order. the 'last' fields are skipped/don't exist
 *
 * <object type> : <name>
 *
 * <name> :
 *   - token:                0Vxx:xxxx         : x = token, take value from token, must be string
 *   - token definition:     10Vx:xxxx <length> <bytes> : x = token, value must be binary, interpreted as string
 *   - direct encoding:      110V:xxxx <data>  : x = length of data, data is ASCII string representing object type, not-null-terminated
 *   - NULL name (object):   1111:1111         : Generally only for a non-existent object. (Thus we don't know it's type)
 *
 * <field>: <field name> <field type> <field value>
 *
 * <field name> : <name>
 *
 * <field type> : If 'A' is 1, then the value is an array of values of this type.
 *   - reserved:             0000:0000
 *   - object:               A000:0001     : Value is <object>       - See below
 *   - null:                 A000:0010     : Value is <value>        - integer value only, only ever 0.
 *   - boolean:              A000:0011     : Value is <value>        - integer value only.
 *   - integer:              A000:0100     : Value is <value>        - any integer or token containing integer
 *   - integer, epoch:       A000:0101     : Value is <value>        - any integer or token containing integer, implies epoch. seconds vs ms vs us is determined dynamically
 *   - float (4-byte):       A000:0110     : Value is <value>        - binary, or token containing binary.
 *   - double (8-byte):      A000:0111     : Value is <value>        - binary, or token containing binary.
 *   - string:               A000:1000     : Value is <value>        - binary, or token containing binary. UTF-8, Not NULL terminated.
 *   - inet:                 A000:1010     : Value is <value>        - binary, or token containing binary. Binary length == sizeof(argo_inet)
 *   - binary:               A000:1011     : Value is <value>        - binary, or token containing binary.
 *   - argo_object:          A000:1100     : Value is <object>       - Same as object, but the decoding is into an argo_object, not a structure.
 *
 * <field value> : Based on <field type>
 *   - array + object:       <count> <objects>
 *   - object:               <object>
 *   - array + value:        <count> <values>
 *   - value:                <value>
 *
 * <count> :
 *   - count                 Vxxx:xxxx         : Unsigned integer
 *
 * <value> :
 *   - token:                000V:xxxx                : x = token, take value from token
 *   - token definition:     001V:xxxx <count> <data> : x = token, define it as whatever value follows.
 *   - integer value:        010S:Vxxx                : x = value
 *   - integer differential: 011S:Vxxx                : x = signed integer to add to last integer value this field had. If had no prior value, then differential from 0
 *   - binary data:          100V:xxxx <data>         : x = data length in bytes
 *   - repeat value:         101V:xxxx                : x repeat values in a row. Cannot extend outside of single object,
 *                                                      does not extend into sub-objects. DOES extend into arrays. Repeat
 *                                                      values cannot repeat anything except integer, binary types.
 *                                                      Repeat values come from last value seen for that field.>
 *   - reserved              110x:xxxx         : Reserved
 *   - reserved              1110:xxxx         : Reserved
 *   - reserved              1111:0xxx         : Reserved
 *   - reserved              1111:10xx         : Reserved
 *   - reserved              1111:110x         : Reserved
 *   - reserved              1111:1110         : Reserved
 *   - NULL value            1111:1111         : Empty value
 *
 *
 *
 *
 *
 * JSON encoding:
 *
 * Field name: "string1:string2", where string1 is name, string2 is
 * type. If string1 == string2, then just one of them is fine. If type
 * is not known, it can be left off (or left off for backwards
 * compatibility)
 *
 * String2 can be of the following types directly:
 *     - float
 *     - mstring
 *     - epoch
 *     - epoch_ms
 *     - epoch_us
 *     - inet
 *     - bin
 *     - Anything else is an object type, and an object must follow
 *
 * Field type encodings:       : type
 *     - object: Object, duh          :
 *     - null: NULL                   : implied
 *     - boolean: true/false          : implied
 *     - integer: number              : implied
 *     - float: number                : float
 *     - double: number               : implied
 *     - string: string               : implied
 *     - multipart string: string     : mstring
 *     - inet: stringified IP address : inet
 *     - binary: base64 binary        : bin
 *
 *
 */

#ifndef __ARGO_PRIVATE_H
#define __ARGO_PRIVATE_H

#include <stdio.h>
#include <stdint.h>
#include <pthread.h>
#include <sys/queue.h>

#include "zpath_misc/zpath_misc.h"

/* Define ARGO_DUMP_TOKENIZATION to see tokenization dumps. Pretty wordy. */
//#define DUMP_TOKENIZATION



/***************************************************
 *
 * Argo2 definitions
 */

/* Maximum number of tokens we suppoert */
#define ARGO2_MAX_TOKENS                       2048

/* Maximum bytes of an object that can be cahced */
#define ARGO2_MAX_CACHE_LEN                    256

/* Maximum number of fields a structure can have */
/* Note: This number is closely correlated to ARGO_BUF_DEFAULT_SIZE,
 * as a the base structure data must fit within
 * ARGO_BUF_DEFAULT_SIZE */
#ifndef ARGO2_MAX_OBJECT_FIELDS
#define ARGO2_MAX_OBJECT_FIELDS                512
#endif

/* Maximum depth of recursion */
#define ARGO2_MAX_OBJECT_DEPTH                 4

/* Maximum size of an array */
#define ARGO2_MAX_ARRAY_SIZE                   (1024*64)

/* Maximum single field size (not including objects). i.e. maximum
 * string size, including NULL */
#define ARGO2_MAX_FIELD_SIZE                   (1024*1024)


#define ARGO2_IS_OBJECT_BEING_DEFINED(x)       ((x) & 0x80)
#define ARGO2_OBJECT_FIELD_COUNT_VBIT          6


#define ARGO2_IS_NAME_TOKEN(x)                 (((x) & 0x80) == 0)
#define ARGO2_NAME_TOKEN                       (0x00)
#define ARGO2_NAME_TOKEN_VBIT                  6
#define ARGO2_IS_NAME_TOKEN_DEF(x)             (((x) & 0xc0) == 0x80)
#define ARGO2_NAME_TOKEN_DEF                   (0x80)
#define ARGO2_NAME_TOKEN_DEF_VBIT              5
#define ARGO2_IS_NAME_DIRECT(x)                (((x) & 0xe0) == 0xc0)
#define ARGO2_NAME_DIRECT                      (0xc0)
#define ARGO2_NAME_DIRECT_VBIT                 4
#define ARGO2_IS_NAME_NULL(x)                  ((x) == 0xff)
#define ARGO2_NAME_NULL                        (0xff)

#define ARGO2_IS_OBJECT_ALREADY_DEFINED(x)     (((x) & 0x80) == 0x00)
#define ARGO2_OBJECT_ALREADY_DEFINED           (0x00)
#define ARGO2_OBJECT_ALREADY_DEFINED_VBIT      6
#define ARGO2_IS_OBJECT_DEFINITION(x)          (((x) & 0xc0) == 0x80)
#define ARGO2_OBJECT_DEFINITION                (0x80)
#define ARGO2_OBJECT_DEFINITION_VBIT           5
#define ARGO2_OBJECT_IS_NULL(x)                (((x) & 0xff) == 0xff)
#define ARGO2_OBJECT_NULL                      (0xff)

#define ARGO2_FIELD_TYPE_RESERVED              0
#define ARGO2_FIELD_TYPE_OBJECT                argo_field_data_type_object
#define ARGO2_FIELD_TYPE_NULL                  argo_field_data_type_null
#define ARGO2_FIELD_TYPE_BOOLEAN               argo_field_data_type_boolean
#define ARGO2_FIELD_TYPE_INTEGER               argo_field_data_type_integer
#define ARGO2_FIELD_TYPE_EPOCH                 argo_field_data_type_epoch
#define ARGO2_FIELD_TYPE_FLOAT                 argo_field_data_type_float
#define ARGO2_FIELD_TYPE_DOUBLE                argo_field_data_type_double
#define ARGO2_FIELD_TYPE_STRING                argo_field_data_type_string
#define ARGO2_FIELD_TYPE_INET                  argo_field_data_type_inet
#define ARGO2_FIELD_TYPE_BINARY                argo_field_data_type_binary
#define ARGO2_FIELD_TYPE_ARGO_OBJECT           argo_field_data_type_argo_object
#define ARGO2_FIELD_TYPE_DICTIONARY            argo_field_data_type_dictionary
#define ARGO2_FIELD_TYPE_LIST                  argo_field_data_type_list

#define ARGO2_FIELD_TYPE_ARRAY                 0x80

#define ARGO2_FIELD_TYPE_MAX                   ARGO2_FIELD_TYPE_LIST

#define ARGO2_VALUE_IS_TOKEN(x)                (((x) & 0xe0) == 0x00)
#define ARGO2_VALUE_TOKEN                      (0x00)
#define ARGO2_VALUE_TOKEN_VBIT                 4
#define ARGO2_VALUE_IS_TOKEN_DEF(x)            (((x) & 0xe0) == 0x20)
#define ARGO2_VALUE_TOKEN_DEF                  (0x20)
#define ARGO2_VALUE_TOKEN_DEF_VBIT             4
#define ARGO2_VALUE_IS_INTEGER(x)              (((x) & 0xe0) == 0x40)
#define ARGO2_VALUE_INTEGER                    (0x40)
#define ARGO2_VALUE_INTEGER_SBIT               4
#define ARGO2_VALUE_IS_INTEGER_DIFF(x)         (((x) & 0xe0) == 0x60)
#define ARGO2_VALUE_INTEGER_DIFF               (0x60)
#define ARGO2_VALUE_INTEGER_DIFF_SBIT          4
#define ARGO2_VALUE_IS_BINARY(x)               (((x) & 0xe0) == 0x80)
#define ARGO2_VALUE_BINARY_LENGTH              (0x80)
#define ARGO2_VALUE_BINARY_LENGTH_VBIT         4
#define ARGO2_VALUE_IS_REPEAT(x)               (((x) & 0xe0) == 0xa0)
#define ARGO2_VALUE_REPEAT_COUNT               (0xa0)
#define ARGO2_VALUE_REPEAT_COUNT_VBIT          4
#define ARGO2_VALUE_IS_NULL(x)                 (((x) & 0xff) == 0xff)
#define ARGO2_VALUE_NULL                       (0xff)

#define ARGO2_ARRAY_COUNT_VBIT                 7
#define ARGO2_GENERIC_COUNT_VBIT               7


#define ARGO_RECORD_CODE_IS_TEXT(x) ((x) == '"')
#define ARGO_RECORD_CODE_BINARY                    0x80
#define ARGO_RECORD_CODE_BINARY_VBIT               6
#define ARGO_RECORD_CODE_IS_BINARY(x) ((x) & 0x80)

/*
 * It is only ever possible to go forward in a stream. It is
 * impossible to go backwards in a stream.
 *
 * The intent is that the records are read and processed in-order, or
 * written out in-order.
 *
 * The 'C' interface operates on objects as structures. The structures
 * are defined using a utility application that creates some structure
 * "definitions" allowing data structure access by the library.
 *
 * This structure "definition" is simply an array, where each member
 * of the array describes the name, offset, type, and maximum size of
 * a structure field.
 *
 * When a stream is created, the various types of structures (types of
 * objects) can be registered with the stream context.
 *
 * Once objects (structures) are registered with a stream context,
 * then those structures can be easily written to the stream
 * context. The library will automatically encode and compress the
 * object properly into the stream context.
 *
 * When writing a stream, the following occurs synchronously:
 *
 * - A structure is passed to the stream to write.
 *
 * - The structure is encoded in scratch memory.
 *
 * - The scratch memory is sent to the stream via the registered
 *   output routine.
 *
 * - Any failure in allocation or output CLOSES the stream.
 *
 * - When writing to the stream, the caller owns all data passed to
 *   write.
 *
 * When reading a stream, the following occurse synchronously:
 *
 * - Stream data is handed to argo to process.
 *
 * - Argo processes the stream data based on the structures registered.
 *
 * - When a complete registered structure has been read, then the
 *   callback associated with that structure is called.
 *
 * - When the callback is called, argo owns all the data during the
 *   callback. If the data is to be retained, it is up to the consumer
 *   to copy it. (XXX- it's probably pretty easy to have argo allocate
 *   the entire structure for the callback, so that it can be retained
 *   by the consumer)
 *
 * There is no enforced synchronization in this library. It is the
 * responsibility of the callers/consumers to implement
 * synchronization as they see fit.
 */

#include <inttypes.h>
#include "zpath_misc/zpath_misc.h"
#include "argo/argo_hash.h"
#include "argo/argo_token.h"
#include "argo/argo_log.h"

#define ARGO_MAX_BLOCK_HEADER_SIZE 1024

/* NOTE: Encoding versions DIRECTLY correspond to major versions... */
#define ARGO_ENCODING_VERSION_ANY 0
#define ARGO_ENCODING_VERSION_2   2
#define ARGO_ENCODING_VERSION_3   3

extern struct zpath_allocator argo_allocator;
#define ARGO_ALLOC(x) zpath_malloc(&argo_allocator, (x), __LINE__, __FILE__)
#define ARGO_CALLOC(x) zpath_calloc(&argo_allocator, (x), __LINE__, __FILE__)
#define ARGO_REALLOC(x, y) zpath_realloc(&argo_allocator, x, y, __LINE__, __FILE__)
#define ARGO_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ARGO_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ARGO_STRDUP(x, y) zpath_strdup(&argo_allocator, x, y, __LINE__, __FILE__)


LIST_HEAD(argo_state_head, argo_state);

extern struct argo_log_collection *argo_log_internal;
#define _ARGO_LOG(priority, format...)                                                                      \
    do {                                                                                                    \
        if (argo_log_internal) {                                                                            \
            ARGO_LOG(argo_log_internal, priority, "argo", ##format);                                        \
        } else {                                                                                            \
            if (argo_watch && (*argo_watch != argo_value)) {                                                \
                fprintf(stderr, "VALUE CHANGED\n");                                                         \
            }                                                                                               \
            if (priority < 5) fprintf(stderr, "\033[30;41;m");                                              \
            fprintf(stderr, "%s: %s: %d: ", argo_log_short_priority_string(priority), __FILE__, __LINE__);  \
            if (priority < 5) fprintf(stderr, "\033[0m");                                                   \
            fprintf(stderr, ##format);                                                                      \
            fprintf(stderr, "\n");                                                                          \
        }                                                                                                   \
    } while (0)
#define _ARGO_DEBUG_LOG(condition, format...)       \
    if (condition) _ARGO_LOG(AL_DEBUG, ##format)

#define ARGO_DEBUG_AGREP_BIT          (uint64_t)0x00000001
#define ARGO_DEBUG_SERIALIZE_BIT      (uint64_t)0x00000002
#define ARGO_DEBUG_DESERIALIZE_BIT    (uint64_t)0x00000004
#define ARGO_DEBUG_TOKEN_BIT          (uint64_t)0x00000008

#define ARGO_DEBUG_NAMES {                      \
    "agrep",                                    \
    "serialize",                                \
    "deserialize",                              \
    "token",                                    \
    NULL                                        \
    }

#if 0
#define _ARGO_DEBUG_AGREP(format...)
#define _ARGO_DEBUG_SER(format...)
#define _ARGO_DEBUG_DES(format...)
#define _ARGO_DEBUG_TOKEN(format...)
#else
#define _ARGO_DEBUG_AGREP(format...) _ARGO_DEBUG_LOG(argo_debug & ARGO_DEBUG_AGREP_BIT, ##format)
#define _ARGO_DEBUG_SER(format...) _ARGO_DEBUG_LOG(argo_debug & ARGO_DEBUG_SERIALIZE_BIT, ##format)
#define _ARGO_DEBUG_DES(format...) _ARGO_DEBUG_LOG(argo_debug & ARGO_DEBUG_DESERIALIZE_BIT, ##format)
#define _ARGO_DEBUG_TOKEN(format...) _ARGO_DEBUG_LOG(argo_debug & ARGO_DEBUG_TOKEN_BIT, ##format)
#endif


extern uint64_t argo_debug;
extern int64_t *argo_watch;
extern int64_t argo_value;
extern int64_t global_argo_adj_time_us;

/*
 * Internal representation of field description.
 */
struct argo_private_field_description {
    /* The public description: */
    struct argo_field_description public_description;

    /* The index of this description. 0 = first described field, etc. */
    size_t index;
};

/*
 *
 */
struct argo_structure_description {
    char *type;
    size_t type_len;

    int64_t allocations;
    int64_t frees;

    int64_t bytes_allocated;
    int64_t bytes_freed;

    /* If set, then this structure description represents a
     * dictionary, and most other fields don't matter */
    int is_dictionary;


    /* If >= 0, contains the field index of the request id */
    int reqid_index;
    /* If >= 0, contains the field index of the 'deleted' field */
    int deleted_index;
    /* If >= 0, contains the field index of the 'sequence' field */
    int sequence_index;

    /* If >= 0, contains the field index of the key field */
    int key_index;

    /* Global index of this structure description. */
    int32_t global_index;

    /* Size of the 'C' structure this description describes. */
    size_t structure_size;

    /* The fields described statically by registration.
     * description_count is the number of actual descriptions.
     * field_count is the count of fields that will be
     * serialized. (many fields simply describe other fields- counts
     * of array elements, etc) */
    const struct argo_field_description *static_description;
    size_t static_description_count;
    size_t static_field_count;

    /* All fields, static and dynamic. Field_count is the count of
     * real fields, excluding those that simply describe other
     * fields. */
    struct argo_private_field_description **description;
    size_t description_count;
    size_t field_count;

    /* The descriptions that represent sub-objects, and how many there
     * are (So they can be walked faster) */
    int *sub_object_indexes;
    size_t sub_object_count;

    /* Hash of ALL the fields. Contains
     * argo_private_field_description's */
    struct zhash_table *described_fields;

    /* Incarnation is updated whenever we learn new fields. */
    // int field_incarnation; USE max_description count instead!

    /* Encoding style for this structure. */
    enum argo_structure_encoding_style encoding_style;
};


/*
 * Per-field memorized state
 */
struct argo_field_memorized {
    union {
        int64_t int_value;
        void *raw_value;
    };
    uint32_t value_len;
    int32_t state;
};

#define AF_MEMORY_STATE_INVALID   0
#define AF_MEMORY_STATE_ALLOCATED 1
#define AF_MEMORY_STATE_INTEGER   2
#define AF_MEMORY_STATE_NULL      3

/*
 * The per-structure state maintained by an argo_state.
 */
struct argo_structure_state {
    /* Master structure state */
    struct argo_structure_description *master;

    /* Callbacks. */
    argo_structure_callback_f *callback_f;
    void *callback_cookie;

    /*
     * There will be as many elements in this array as there are
     * description_count in master. We still need to remember the
     * size of allocated state in case it changes...
     */
    struct argo_field_memorized *field_values;
    int field_values_count;
    int last_number_of_fields_written;

    /* This array translates receive-index order (decoded from a
     * stream) to master description index order. Binary only. */
    int *decode_index_to_description_index;
    int decode_index_count;

    /* Encoding style for this structure. */
    enum argo_structure_encoding_style encoding_style_XXX;

    /* Incarnation is updates whenever our compression state changes. */
    int argo_incarnation;  /* Confirm match with this argo...
                            * (i.e. connection coming/going, things
                            * like that) */
    int field_incarnation; /* When fields of this structure change */
};


/*
 * Global argo state.
 *
 * This is basically the collection of all descriptions. This provides
 * an index for each description, allowing multiple argo instances to
 * use a common structure description.
 *
 * Lock: Used to protect structure descriptions, since they can grow.
 *
 * All consumers of structure desriptions will hold the read lock
 * while using descriptions (common), and the write lock will only be
 * held while structures are globally registered or changed.
 *
 * The write lock is also held when argo instances are coming/going.
 */
struct argo_global_state {
    struct zhash_table *all_descriptions_hash;
    struct argo_structure_description **all_descriptions;
    size_t all_description_count;
    size_t all_description_max;

    struct argo_state_head argos; /* List of all argos. */

    /* Only posix locking for now, sorry. */
    zpath_rwlock_t rwlock;
    zpath_mutex_t argo_states_lock;
};


/* Encoding utility functions. None of these routines perform bounds
 * checking. */

enum argo_read_state {
    read_state_ready = 0,
    read_state_reading_block_description,
    read_state_reading_structure_length
};

/*
 * We use ourselves to read/store our block description. This is that
 * structure.
 */
struct argo_description {		/* _ARGO: object_definition */
    uint64_t major_version;		/* _ARGO: integer */
    uint64_t minor_version;		/* _ARGO: integer */
    uint64_t block_size;		/* _ARGO: integer */
};

struct argo_state {
    /* Our block description, as it currently exists. */
    struct argo_description block_description;

    /* Hash to find named structures. Stores struct
     * argo_structure_state. */
    struct zhash_table *registered_structures;

    /* The structures registered with this argo_state. Indexing for
     * these mirrors global structure indexing. */
    struct argo_structure_state **structures;

    /* Serialization mode: JSON or Binary. */
    enum argo_serialize_mode mode;

    /* Incarnation number. This number is incremented whenever our
     * "compression" state must be reset. Rather than clearing all
     * state, we implicitly invalidate it by changing our incarnation
     * number. This particularly affects tokens, and whether they must
     * be re-written. */
    int argo_incarnation;

    int argo_encoding_version;

    /* Whether or not we're doing strict version checking. Ya
     * think? */
    unsigned strict_version_check:1;
    unsigned skip_version_xmit:1;
    unsigned accept_binary:1;
    unsigned tokenizing:1;
    unsigned received_any_object:1;

    /* Write callback info */
    argo_write_callback_f *write_callback;
    argo_bulk_write_callback_f *bulk_callback;
    argo_structure_callback_f *default_read_callback;
    argo_encoding_version_callback_f *version_callback;
    void *callback_cookie;
    /* Callback_count- number of times bulk_callback has been called. */
    int64_t callback_count;

    struct argo2_token_pool *argo2_token_pool;

    /* Current read parsing state */
    enum argo_read_state read_state;

    /* Current read buffer. */
    struct argo_buf *read_buf;

    /* Parse buffer. Used to walk read_buf. */
    struct argo_buf *parse_buf;

    /* Total bytes that have been read/written */
    uint64_t block_byte_count;

    /* Byte count where we expect the next block description. */
    uint64_t block_byte_count_next_block_description;

    /* Yay for description_description, but that's what it is- a
     * description of "struct argo_description" */
    struct argo_structure_description *argo_description_description;

    LIST_ENTRY(argo_state) argos;
};


/* In case someone wants our global state... */
extern struct argo_global_state argo_global;


#define argo_read_lock() ZPATH_RWLOCK_RDLOCK(&(argo_global.rwlock), __FILE__, __LINE__)
#define argo_unlock() ZPATH_RWLOCK_UNLOCK(&(argo_global.rwlock), __FILE__, __LINE__)

/*
 * Writes the given data using the the argo write callback
 * function. This routine does correct counter maintenance.
 */
int argo_write(struct argo_state *argo, uint8_t *data, size_t size);

/*
 * Writes out padding bytes at least somewhat efficiently.
 */
int argo_pad(struct argo_state *argo, size_t size);


/* Release the *children* of this object, not the object itself */
void argo_object_force_release(struct argo_object *object);

/*
 * Internal structure serialization routine.
 */
int argo_serialize_structure_binary(struct argo_state *argo,
                                    struct argo_structure_description *description,
                                    void *data,
                                    struct argo_buf **buf,
                                    size_t max_fields);


int argo_register_unknown_structure(struct argo_state *argo,
                                    const char *structure_name,
                                    size_t structure_name_length);

/*
 * Textual serialization routine. Outputs into JSON format.
 *
 * This outputs an object of the form:
 *
 * "name": { \n
 *    "field 1":<value1>, \n
 *    "field 2":<value2>, \n
 *    ... \n
 *    "field n":<valuen> \n
 * } \n
 *
 *
 * argo        : Argo state.
 * description : Compiled description of the structure we are
 *               encoding.
 * struct_name : The name of the object we are writing. (needed for
 *               sub-structures)
 * struct_data : ptr to the structure to write.
 * buf         : Variable length output buffer.
 * need_comma  : Whether or not to append a comma to the end of the
 *               json object.
 * depth       : How many times we have recursed.
 * max_fields  : At most how many fields to serialize.
 *
 * depth is just a recursion depth used to facilitate printing the
 * structures with correct indentation.
 *
 * encode_type : whether to encode the argo structure type in
 * structure header
 */
extern int argo_serialize_structure_json(struct argo_structure_description *description,
                                         const char *struct_name,
                                         void *struct_data,
                                         struct argo_buf **buf,
                                         int need_comma,
                                         int depth,
                                         size_t max_fields,
                                         enum argo_serialize_mode mode,
                                         int trim_trailing_newline,
                                         int encode_type,
                                         int limit_string_length);

/*
 * Routine to reset compression state. Should probably only be called
 * internally...
 */
extern int argo_state_reset_compression(struct argo_state *argo);
extern size_t argo_structure_size(struct argo_structure_description *description, void *structure, int excess_field_count);
/*
 * Add a field to an argo structure.
 */
extern int argo_global_structure_add_field(struct argo_structure_description *description,
                                           char *name,
                                           size_t length,
                                           enum argo_field_data_type data_type,
                                           int is_array,
                                           int is_double,
                                           int is_inet);


static inline int64_t argo_read_int(const void *addr, int bytes) {
    int64_t value;
    switch (bytes) {
    default:
        value = 0;
        break;
    case 1:
        value = *((int8_t *) addr);
        break;
    case 2:
        value = *((int16_t *) addr);
        break;
    case 4:
        value = *((int32_t *) addr);
        break;
    case 8:
        value = *((int64_t *) addr);
        break;
    }
    return value;
}

static inline int64_t argo_read_int_offset(const void *addr, size_t offset, int bytes) {
    int64_t value;
    addr = &(((uint8_t *)addr)[offset]);
    switch (bytes) {
    default:
        value = 0;
        break;
    case 1:
        value = *((int8_t *) addr);
        break;
    case 2:
        value = *((int16_t *) addr);
        break;
    case 4:
        value = *((int32_t *) addr);
        break;
    case 8:
        value = *((int64_t *) addr);
        break;
    }
    return value;
}

static inline void argo_write_int(void *addr, int64_t value, int bytes) {
    switch (bytes) {
    default:
        fprintf(stderr, "%s:%s:%d: ERROR: write_int has bytes = %d\n", __FILE__, __FUNCTION__, __LINE__, bytes);
        break;
    case 1:
        *((int8_t *) addr) = value;
        break;
    case 2:
        *((int16_t *) addr) = value;
        break;
    case 4:
        *((int32_t *) addr) = value;
        break;
    case 8:
        *((int64_t *) addr) = value;
        break;
    }
}

static inline void argo_write_int_offset(void *addr, size_t offset, int64_t value, int bytes) {
    addr = &(((uint8_t *)addr)[offset]);
    switch (bytes) {
    default:
        fprintf(stderr, "%s:%s:%d: ERROR: write_int has bytes = %d\n", __FILE__, __FUNCTION__, __LINE__, bytes);
        break;
    case 1:
        *((int8_t *) addr) = value;
        break;
    case 2:
        *((int16_t *) addr) = value;
        break;
    case 4:
        *((int32_t *) addr) = value;
        break;
    case 8:
        *((int64_t *) addr) = value;
        break;
    }
}
//#define ARGO_ALIGN(x) (((x) + 7) & (~7))
#define ARGO_ALIGN(x) (x)


int argo_log_gen_time_only(int64_t epoch_us, char *time_out, size_t time_out_size);
#endif // __ARGO_PRIVATE_H
